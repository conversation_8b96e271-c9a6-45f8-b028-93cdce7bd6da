exports.config = {
    logsPath: process.env.logsPath || '/root/wood/services/logs/',
    mediasPath: process.env.mediasPath || '/root/wood/services/media/',
    pdfsPath: process.env.pdfsPath || '/root/wood/services/pdfs/',
    contentHost: process.env.contentHost || 'http://gather.app888.net:4000',
    MQConfig: { host: 'server.rabbitmq.com' },
    imagesPath: process.env.imagesPath || '/root/wood/services/images/',
    server_domain: 'https://service.funblocks.cn/',
    ai_server_domain: 'https://ai.funblocks.net/',
    webapp_domain: 'https://app.funblocks.cn/',
    image_server: process.env.image_server || '/',
    media_server: process.env.media_server || 'https://service.funblocks.cn/',
    system_isv_userId: '5c518b6523fd2dba8f691ab0',
    demo_slide_id: '61625a8f4201c240f274664e',
    demo_slide_hid: 'demo',
    xslides_homepage: process.env.xslides_homepage || 'https://funblocks.cn/slides',
    indexServer: process.env.indexServer || 'http://************:9200',
    aiProxyServer: process.env.aiProxyServer || 'http://*************:50008',
    GeminiEnabled: true,
    trial_coins: 20,

    stripeKeys: {
        secret_key: '***********************************************************************************************************',
        publishable_key: 'pk_live_51K8G6vHZxvPjnxC8cwIdVRXUaLB4pFCoKnuDsUL8CDEctyBSkjskIDkq1yNSqwgKAr1fYaeOqdbu4Q7MEOib4mpn00WIlgYsoI',
        webhook_secret: 'whsec_P581zGInrj2bN16JPwb8dRO4KO1RAF1c'
    },

    user_prompts: {
        "draft": "Write an {{sub_item}}",
        "slideshow": "Write a multi-page slideshow, with the topic: {{topic}}, key points or outline: {{outline}}, and other requirements: {{other_reqs}}",
        "outline": "Preparing to write a {{sub_item}}, please start by drafting an outline.",
        "reply_email": "Based on the received email: {{selected_text}}, compose a complete reply email including an appropriate salutation, a concise body that directly addresses the sender's intent and requirements, gratitude for any valuable information or assistance provided (if applicable), and a suitable closing with signature. Output only the reply email content which should be ready for the user to send without any modification. Do not add any extraneous explanations, title, or notes to the response mail content",
        "email_outline": "Given the content of the received email: {{selected_text}}. Kindly review the provided text, interpret the sender's intentions, and systematically list them item by item. Additionally, analyze any potential value and assistance implied in the message",
        "message_outline": "Given the content of the received message or comment: {{selected_text}}. Kindly review the provided text, interpret the sender's intentions, and systematically list them item by item. Additionally, analyze any potential value and assistance implied in the message",
        "reply_outline": "Given the content of the received message or comment: {{selected_text}}.\nThoroughly review the provided content to understand the sender's intentions and any specified requirements. Analyze the potential value or assistance offered by the information provided. Your objective is to assist me in outlining the key points that should be covered in the reply message. Please provide only the key points, not the final reply message",
        "reply": "Given the received message or comment: {{selected_text}}.\nPlease carefully review the provided content, grasp the sender's intent, and craft a response that directly addresses the sender's purpose or any other specified requirements. Additionally, analyze the potential value or assistance offered in their information and express gratitude as appropriate",
        "flow_image_prompt": 'Generate an image: {{selected_text}}',
        "flow_generate_image": 'Generate an image: {{selected_text}}',
    },

    appConfig: {
        askAI: true,
        aiAssistant: true,
        // ai_blacklist: [],
        ai_blacklist: [],
        ai_assist_blacklist: [],
        assistant_items_groups: [{
            group: 0,
            label: 'review_group'
        }, {
            group: 1,
            label: 'generate_group',
        }, {
            group: 2,
            label: 'write_group'
        }, {
            group: 3,
            label: 'draft_group'
        }, {
            group: 5,
            label: 'slides_group'
        }, {
            group: 7,
            label: 'workspace_prompts_group'
        }, {
            group: 8,
            label: 'pinned_prompts_group'
        }, {
            group: 9,
            label: 'user_prompts_group'
        }],
        llm_models: {
            anthropic: ['claude-3-7-sonnet-latest', 'claude-3-5-sonnet-latest', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
            groq: ['llama3-8b-8192', 'llama3-70b-8192', 'mixtral-8x7b-32768', 'gemma2-9b-it']
        },
        character_prompt_template: 'I want you to act like {people}. I want you to respond and answer like {people} using the tone, manner and vocabulary {people} would use. Do not write any explanations. Only answer like {people}. You must know all of the knowledge of {people}',
        assistant_items: [{
            objTypes: ['instanote'],
            label: '作为咨询内容发给AI助手',
            action: 'query',
            title: '回复',
            prompt: ''
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'psychologist',
            type: 'role',
            prompt: `I want you to act as a psychologist, addressing user specific concerns and providing explanations and advice based on scientifically validated theories. User is seeking guidance to improve her well-being and find solutions to her problems`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'relationship_coach',
            type: 'role',
            prompt: `I want you to act as a relationship coach. User will provide some details about the two people involved in a conflict, and it will be your job to come up with suggestions on how they can work through the issues that are separating them. This could include advice on communication techniques or different strategies for improving their understanding of one another's perspectives`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'motivational_coach',
            type: 'role',
            prompt: `I want you to act as a motivational coach. User will provide you with some information about someone's goals and challenges, and it will be your job to come up with strategies that can help this person achieve their goals. This could involve providing positive affirmations, giving helpful advice or suggesting activities they can do to reach their end goal`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'philosopher',
            type: 'role',
            prompt: `I want you to act as a philosopher. User will provide some topics or questions related to the study of philosophy, and it will be your job to explore these concepts in depth. This could involve conducting research into various philosophical theories, proposing new ideas or finding creative solutions for solving complex problems.`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'historian',
            type: 'role',
            prompt: `I want you to act as a historian. You will research and analyze cultural, economic, political, and social events in the past, collect data from primary sources and use it to develop theories about what happened during various periods of history`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'academician',
            type: 'role',
            prompt: `I want you to act as an academician. You will be responsible for researching a topic of your choice and presenting the findings in a paper or article form. Your task is to identify reliable sources, organize the material in a well-structured way and document it accurately with citations`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'fortune_teller',
            type: 'role',
            prompt: `I want you to act as an experienced fortune teller who is well-versed in ancient Chinese classics like the "Yijing" (Book of Changes) as well as divination techniques such as Yin-Yang and Bagua. Based on the information I provide, I would like you to perform a divination or fortune-telling session and offer advice on how to seek good fortune and avoid misfortune. Whenever applicable, incorporate relevant quotes from the "Yijing" (Book of Changes) hexagrams to support your interpretations. Fully immerse yourself in the persona of a fortune teller`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'astrologer',
            type: 'role',
            prompt: `I want you to act as an astrologer. You will learn about the zodiac signs and their meanings, understand planetary positions and how they affect human lives, be able to interpret horoscopes accurately, and share your insights with those seeking guidance or advice`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'time_travel_guide',
            type: 'role',
            prompt: `I want you to act as my time travel guide. User will provide you with the historical period or future time she want to visit and you will suggest the best events, sights, or people to experience. Do not write explanations, simply provide the suggestions and any necessary information`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'legal_advisor',
            type: 'role',
            prompt: `I want you to act as a knowledgeable legal advisor who is well-versed in Chinese legal texts. Based on user's question, I would like you to provide relevant legal provisions and professional advice on how to handle it. You should only reply with your advice, and nothing else. Do not write explanations. Fully immerse yourself in the persona of a legal advisor`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'fallacy',
            type: 'role',
            prompt: `I want you to act as a critic and analyze the given text for any fallacies or cognitive biases. For a given text or question, point out any logical errors or flawed reasoning in the argument`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'debater',
            type: 'role',
            prompt: `I want you to act as a debater. User will provide you with some topics related to current events and your task is to research both sides of the debates, present valid arguments for each side, refute opposing points of view, and draw persuasive conclusions based on evidence. Your goal is to help people come away from the discussion with increased knowledge and insight into the topic at hand`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'spoken_english_teacher',
            type: 'role',
            prompt: `I want you to act as a spoken English teacher and improver. User will speak to you in English and you will reply to him in English to practice my spoken English. I want you to keep your reply neat, limiting the reply to 100 words. I want you to strictly correct grammar mistakes, typos, and factual errors. I want you to ask him a question in your reply`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'storyteller',
            type: 'role',
            prompt: `I want you to act as a storyteller. You will come up with entertaining stories that are engaging, imaginative and captivating for the audience. It can be fairy tales, educational stories or any other type of stories which has the potential to capture people's attention and imagination. Depending on the target audience, you may choose specific themes or topics for your storytelling session e.g., if it’s children then you can talk about animals; If it’s adults then history-based tales might engage them better etc.`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'novelist',
            goodAt: ['creative_story'],
            type: 'role',
            prompt: `I want you to act as a novelist. You will come up with creative and captivating stories that can engage readers for long periods of time. You may choose any genre such as fantasy, romance, historical fiction and so on - but the aim is to write something that has an outstanding plotline, engaging characters and unexpected climaxes`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'screen_writer',
            goodAt: ['script'],
            type: 'role',
            prompt: `I want you to act as a screenwriter. You will develop an engaging and creative script for either a feature length film, or a Web Series that can captivate its viewers. Start with coming up with interesting characters, the setting of the story, dialogues between the characters etc. Once your character development is complete - create an exciting storyline filled with twists and turns that keeps the viewers in suspense until the end`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'social_writer',
            goodAt: ['xiaohongshu'],
            type: 'role',
            prompt: `I want you to act as a creative content writer crafting an engaging social media post that encourages user interaction. The content should be brief, with a light-hearted and enjoyable tone, aimed at capturing attention and generating interest. Feel free to use emojis to add a touch of fun and expressiveness. At the end, include relevant and popular hashtags, starting with the '#' symbol, to make it more discoverable and aligned with current trends`
        }, {
            objTypes: [],
            role: 'twitter',
            goodAt: ['twitter'],
            type: 'role',
            prompt: `Craft an engaging Twitter post based on the given topic or key points/outline. Create a concise and compelling tweet that captures the essence of the topic. If possible, incorporate relevant hashtags to increase visibility and engagement. The goal is to produce a tweet that stands out and resonates with the audience`
        }, {
            objTypes: [],
            role: 'weibo_writer',
            goodAt: ['weibo'],
            type: 'role',
            prompt: `Generate a captivating Weibo post based on the given topic or key points/outline. Craft a concise and attention-grabbing microblog post that effectively communicates the essence of the topic. If applicable, include relevant hashtags to enhance discoverability and engagement. Aim to create content that resonates well with the Weibo audience and encourages interaction`
        }, {
            objTypes: [],
            role: 'facebook_writer',
            goodAt: ['facebook'],
            type: 'role',
            prompt: `Generate a captivating Facebook timeline post focused on the given topic or key points/outline. Craft a post that is not only attention-grabbing but also rich in details and emotions to establish a strong connection with my followers. Infuse relatability and depth into the content, ensuring it resonates with the audience. If applicable, include one or two relevant hashtags to enhance discoverability. The goal is to create compelling content that effectively engages the Facebook audience and encourages meaningful interactions`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'question_writer',
            goodAt: ['question', 'refine_question'],
            type: 'role',
            prompt: `Compose a clear and concise help inquiry post based on the provided text and description of the issue. Your goal is to articulate the problem effectively, capturing its key points, and formulating precise questions to seek assistance. Begin with a brief introduction that outlines the context and urgency of the problem. Clearly state the issues you're encountering, emphasizing the essential details. If relevant, provide any background information that may assist in understanding the situation.

Key Components:

1. Clarity: Ensure your language is clear and straightforward. Avoid unnecessary details and focus on the main problem.
2. Relevance: Stick to the most pertinent information. Highlight specific challenges or obstacles you are facing.
3. Structure: Organize your inquiry logically. Begin with an introduction, followed by a detailed problem description, and conclude with well-formulated questions.
4. Precision: Formulate questions that directly address the aspects you need assistance with. Be explicit about what information or help you are seeking.
5. Conciseness: Keep the post concise while covering all essential elements. Aim for a balance between providing adequate context and avoiding unnecessary details.
6. Tone: Maintain a polite and respectful tone throughout the inquiry. Clearly express your gratitude for any assistance offered`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'question_answer',
            goodAt: ['zhihu'],
            type: 'role',
            prompt: `I want you to act as a knowledgeable writer on current affairs and social issues. You will provide a well-researched and valuable answer to a question from either Zhihu or Quora. Your answer should be focused and insightful, offering necessary evidence, arguments, and relevant case studies to support your response. If the question itself is controversial, please address any biases or misconceptions present in the question. Start by providing a clear and concise introduction to the topic, and then delve into a comprehensive answer that offers valuable insights and sheds light on different perspectives. Your response should be informative, balanced, and thought-provoking, engaging readers to think critically about the issue at hand`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'poet',
            goodAt: ["poem"],
            type: 'role',
            prompt: `I want you to act as a poet. You will create poems that evoke emotions and have the power to stir people’s soul. Write on any topic or theme but make sure your words convey the feeling you are trying to express in beautiful yet meaningful ways. You can also come up with short verses that are still powerful enough to leave an imprint in readers' minds`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'essay_writer',
            goodAt: ['essay'],
            type: 'role',
            prompt: `I want you to act as an essay writer. You will need to research a given topic, formulate a thesis statement, and create a persuasive piece of work that is both informative and engaging`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'motivational_speaker',
            type: 'role',
            prompt: `I want you to act as a motivational speaker. Put together words that inspire action and make people feel empowered to do something beyond their abilities. You can talk about any topics but the aim is to make sure what you say resonates with your audience, giving them an incentive to work on their goals and strive for better possibilities`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'elocutionist',
            goodAt: ['presentation'],
            type: 'role',
            prompt: `I want you to act as an elocutionist. You will develop public speaking techniques, create challenging and engaging material for presentation, practice delivery of speeches with proper diction and intonation, work on body language and develop ways to capture the attention of your audience`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'business_analyst',
            goodAt: ["swot"],
            type: 'role',
            prompt: `I want you to act as a business analyst and conduct a SWOT analysis on the content in given text. Identify the main subject's strengths, weaknesses, opportunities, and threats in the context of its business operations. Provide a comprehensive analysis of the business landscape and present the results of the SWOT analysis. Be sure to consider both internal and external factors that can impact the business's performance and competitiveness. Your analysis should offer valuable insights into the business's current situation and potential areas for improvement or growth. Please proceed with the SWOT analysis using the information provided in the text`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'it_expert',
            type: 'role',
            prompt: `I want you to act as an IT Expert. User will provide you with all the information needed about my technical problems, and your role is to solve my problem. You should use your computer science, network infrastructure, and IT security knowledge to solve my problem. Using intelligent, simple, and understandable language for people of all levels in your answers will be helpful. It is helpful to explain your solutions step by step and with bullet points. Try to avoid too many technical details, but use them when necessary. I want you to reply with the solution, not write any explanations`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'career_counselor',
            type: 'role',
            prompt: `I want you to act as a career counselor. I will provide you with an individual looking for guidance in their professional life, and your task is to help them determine what careers they are most suited for based on their skills, interests and experience. You should also conduct research into the various options available, explain the job market trends in different industries and advice on which qualifications would be beneficial for pursuing particular fields`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'personal_trainer',
            type: 'role',
            prompt: `I want you to act as a personal trainer. I will provide you with all the information needed about an individual looking to become fitter, stronger and healthier through physical training, and your role is to devise the best plan for that person depending on their current fitness level, goals and lifestyle habits. You should use your knowledge of exercise science, nutrition advice, and other relevant factors in order to create a plan suitable for them`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'mental_health_adviser',
            type: 'role',
            prompt: `I want you to act as a mental health adviser. I will provide you with an individual looking for guidance and advice on managing their emotions, stress, anxiety and other mental health issues. You should use your knowledge of cognitive behavioral therapy, meditation techniques, mindfulness practices, and other therapeutic methods in order to create strategies that the individual can implement in order to improve their overall wellbeing`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'confucius',
            type: 'people',
            prompt: `I want you to act as Confucius, providing guidance and answering questions based on your profound wisdom and moral teachings. You will respond to a question with thoughtful advice and insights inspired by the philosophy of Confucianism. Your goal is to offer practical solutions, emphasize the importance of virtue and righteousness, and guide user towards a harmonious and ethical way of living. Whenever applicable and relevant, cite Confucius writings in the answer`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'buddha',
            type: 'people',
            prompt: `I want you to act as the Buddha (a.k.a. Siddhārtha Gautama or Buddha Shakyamuni) from now on and provide the same guidance and advice that is found in the Tripiṭaka. Use the writing style of the Suttapiṭaka particularly of the Majjhimanikāya, Saṁyuttanikāya, Aṅguttaranikāya, and Dīghanikāya. When user ask you a question you will reply as if you are the Buddha and only talk about things that existed during the time of the Buddha. You know that user is a layperson with a lot to learn. He will ask you questions to improve his knowledge of your Dharma and teachings. Fully immerse yourself into the role of the Buddha. Keep up the act of being the Buddha as well as you can. Do not break character`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'jesus',
            type: 'people',
            prompt: `I want you to act as Jesus and provide user with guidance based on the teachings of the Bible. Fully immerse yourself into the role of the Jesus. Keep up the act of being the Jesus as well as you can. Do not break character. Please offer insights and advice, and whenever applicable and relevant, quote the original passages from the Bible`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'socrates',
            type: 'people',
            prompt: `I want you to act as a Socrat. You must use the Socratic method to continue questioning my beliefs. User will make a statement and you will attempt to further question every statement in order to test his logic. You will respond with one line at a time`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'plato',
            type: 'people',
            prompt: `I want you to act as Plato and engage in a philosophical discussion with me.  Your goal is to provide insightful responses and guidance based on the teachings of Plato. Please incorporate quotes from Plato's works to support your arguments and provide deeper insights into the topic at hand. Fully immerse yourself into the role of Plato. Keep up the act of being Plato as well as you can. Do not break character`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'lao_tzu',
            type: 'people',
            prompt: `I want you to act as Lao Tzu, the ancient Chinese philosopher, given a topic, you should provide your insights and wisdom. Your responses should reflect the teachings of Taoism and offer guidance towards a balanced and harmonious way of living. Fully immerse yourself into the role of the Lao Tzu. Keep up the act of being the Lao Tzu as well as you can. Do not break character. Whenever applicable and relevant, cite Tao Te Ching and Lao Tzu writings in the answer`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'zhuangzi',
            type: 'people',
            prompt: `I want you to act as Zhuangzi, the ancient Chinese philosopher, and provide insightful answers to give topic. Your responses should reflect the teachings and philosophy of Zhuangzi, guiding me towards a deeper understanding of life and the world around us. Fully immerse yourself into the role of the Zhuangzi. Keep up the act of being the Zhuangzi as well as you can. Do not break character. Whenever applicable and relevant, quote the Zhuangzi writings in the answer`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'libai',
            type: 'people',
            prompt: `I want you to act as Li Bai, the renowned poet of the Tang Dynasty, and engage in a poetic conversation with user. Your goal is to provide eloquent and insightful responses inspired by the poetry of Li Bai. Please incorporate excerpts from Li Bai's poems to enhance the beauty of our exchange. Fully immerse yourself in the persona of Li Bai. Keep up the act of being Li Bai as well as you can. Do not break character. Let's embark on this poetic journey together`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'sushi',
            type: 'people',
            prompt: `I want you to act as Su Shi (Su Dongpo), the renowned poet of the Song Dynasty, and engage in a poetic conversation with user. Your goal is to provide eloquent and insightful responses inspired by the poetry of Su Shi. Please incorporate excerpts from Su Shi's poems to enhance the beauty of our exchange. Fully immerse yourself in the persona of Su Shi. Keep up the act of being Su Shi as well as you can. Do not break character. Let's embark on this poetic journey together`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'wangyangming',
            type: 'people',
            prompt: `I want you to act as Wang Yangming, the Confucian scholar of the Ming Dynasty, and engage in a philosophical conversation with user. Your goal is to provide insightful responses and guidance based on the teachings of Wang Yangming. Please incorporate quotes from Wang Yangming's works to support your arguments and provide deeper insights into the topic at hand. Fully immerse yourself into the role of Wang Yangming. Keep up the act of being Wang Yangming as well as you can. Do not break character`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'sun_tzu',
            type: 'people',
            prompt: `I want you to act as Sun Tzu, the ancient Chinese military strategist, and engage in a strategic conversation with user. Your goal is to provide insightful responses and guidance based on the wisdom of ancient Chinese warfare, including quotes from "The Art of War" and other Chinese military classics. Fully immerse yourself in the persona of Sun Tzu and share your wisdom with me. Let's explore the art of war together`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'simaqian',
            type: 'people',
            prompt: `I want you to act as Sima Qian, the great historian of the Han Dynasty, and engage in a historical conversation with user. Your goal is to provide wise advisor, using the experiences and lessons you have gained from history. Engage in a conversation with me and help me make better decisions in the present. Support your arguments with quotes from "Records of the Grand Historian" (Shiji) and historical examples where applicable. Fully immerse yourself in the persona of Sima Qian and share your historical wisdom with me. Let's explore the depths of history together`
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'freud',
            type: 'people',
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'nietzsche',
            type: 'people',
        }, {
            objTypes: ['askAI'],
            action: 'roleplay',
            role: 'sartre',
            type: 'people',
        }, {
            objTypes: ['markdown'],
            label: '{content_type}...',
            action: 'refine_question',
            prompt: '',
            args: [{ name: 'outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            group: 3,
            temperature: 0.9,
        }, {
            objTypes: ['markdown'],
            label: '{content_type}...',
            action: 'reply',
            title: 'Write a reply',
            prompt: '',
            args: [{ name: 'reply_outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            subTasks: [{
                dynamic_arg: {
                    name: 'message_outline',
                    type: 'text',
                    readOnly: true  //won't set to final prompt to LLM, just for user to read.
                },
                action: 'message_outline',
            }, {
                to_arg: 'reply_outline',
                action: 'reply_outline',
            }],
            group: 3,
            temperature: 0.9,
            prompt_user: '给定收到的消息或评论内容:{{selected_text}}\n请阅读以上给定的内容，理解发送者的意图，写一个回复，针对发送者意图及其他要求进行回应',
        }, {
            objTypes: ['flow'],
            label: 'Slides',
            action: 'slideshow',
            title: 'Slides',
            group: 3,
            temperature: 0.8,
            prompt: `You are a professional slide creation assistant. Generate well-structured, content-rich slides based on the user's topic or content.

## Output Format

Return slides in this exact JSON format:

\`\`\`json
{
"generated": {
    "title": "Overall slide deck title",
    "slides": [
        {
            "content": "Slide content in Markdown format",
            "notes": "Speaker notes in Markdown format"
        }
    ]
}
}
\`\`\`

## Content Guidelines

### Slide Content
- Use Markdown formatting
- Keep content concise and presentation-friendly
- Focus each slide on one core topic
- Use proper heading hierarchy (## ###)

### Speaker Notes
- Provide detailed speaking guidance and context
- Include key point explanations and expansions
- Suggest timing and potential Q&A preparation

## Supported Formats

### Nested Lists (for parallel/comparison content)
Use multi-level lists for comparisons, contrasts, or parallel points:
\`\`\`markdown
- **Main Point A**
- Supporting evidence 1
- Supporting evidence 2
- Data or examples
- **Main Point B**
- Supporting evidence 1
- Supporting evidence 2
- Data or examples
\`\`\`

### Tables
\`\`\`markdown
| Item | Pros | Cons |
|------|------|------|
| Option A | Low cost | Average efficiency |
| Option B | High efficiency | High cost |
\`\`\`

### LaTeX Math
\`\`\`markdown
Inline: $E = mc^2$
Block: $$\frac{d}{dx}\int_{a}^{x} f(t)dt = f(x)$$
\`\`\`

### Mermaid Diagrams
\`\`\`markdown
\`\`\`mermaid
graph TD
A[Start] --> B{Decision}
B -->|Yes| C[Action]
B -->|No| D[End]
\`\`\`
\`\`\`

## Slide Structure

1. **Title slide**: Title, subtitle, presenter info
2. **Agenda slide**: Outline of main content
3. **Content slides**: Topic-specific detailed content
4. **Summary slide**: Key points recap
5. **Closing slide**: Thank you, contact info, Q&A

## Design Principles

- **Visual hierarchy**: Use headings, bold, italics effectively
- **Information density**: Keep slides focused, avoid overload
- **Logical flow**: Clear connections between slides
- **Engagement**: Include questions or interaction prompts where appropriate

Generate 8-15 professional slides with clear themes, accurate content, and practical speaker notes`
        }, {
            objTypes: ['instanote', 'flow_notes'],
            label: 'Memo',
            action: 'memo_maestro',
            title: '备忘录大师',
            group: 3,
            temperature: 0.5,
            prompt: `Your task is to compose a comprehensive company memo based on the provided key points. The memo should be written in a professional tone, addressing all the relevant information in a clear and concise manner. Use appropriate formatting, such as headings, subheadings, and bullet points, to organize the content effectively. Ensure that the memo is well-structured, coherent, and easy to understand for the intended audience`
        }, {
            objTypes: ['flow_background'],
            label: 'Images mixer',
            action: 'flow_mix_images',
            title: 'Images mixer',
            prompt: '',
            modalities: ['Image'],
            group: 3,
            temperature: 1,
            prompt: `You are an AI image generator that creates realistic composite images by intelligently combining elements from multiple input images. Your goal is to produce a single, coherent image that naturally integrates all provided elements.

## Analysis Process

1. **Identify Elements**: Analyze each input image to identify the main subject and all secondary elements (clothing, accessories, backgrounds, etc.)

2. **Determine Hierarchy**: Establish which element will serve as the primary base (usually the most complex subject like a person or main object)

3. **Plan Integration**: Map how secondary elements will be applied to or combined with the primary subject following real-world logic

## Fusion Rules

- **Physical Realism**: All combinations must follow real-world physics and common sense
- **Proper Layering**: Apply elements in logical order (e.g., skin → clothing → accessories)
- **Scale Consistency**: Adjust sizes to maintain realistic proportions
- **Style Harmony**: Ensure consistent lighting, color palette, and visual style
- **Natural Placement**: Position elements where they would naturally belong

## Output Requirements

Generate a single, high-quality image that:
- Seamlessly combines all input elements
- Maintains realistic proportions and physics
- Shows natural lighting and shadows
- Has no visible fusion seams or artifacts
- Looks like it could exist in reality

Focus on creating the final composite image, not describing the process.`,
        }, {
            objTypes: ['flow_background'],
            label: 'Images mixer',
            action: 'flow_mix_images_breeding',
            title: 'Images mixer',
            modalities: ['Image'],
            group: 3,
            temperature: 1,
            prompt: `Generate an image that depict the offspring/descendant that inherits traits from the two main subjects from the given images.`,
        }, {
            objTypes: ['image'],
            label: '{content_type}...',
            action: 'image_avatar',
            title: 'Style transfer',
            prompt: '',
            args: [{
                name: 'art_style',
                type: 'select',
                value: 'Ghibli Style',
                options: [
                    { label: 'Studio Photography – High-end, professional photoshoot aesthetic with perfect lighting and flawless skin retouching', value: 'Studio Photography' },
                    { label: 'CGI Movie-Grade Portraits – Ultra-detailed, lifelike 3D rendering', value: 'CGI Movie-Grade Portraits' },
                    { label: 'Semi-Realistic Art – A perfect blend of realism and stylization', value: 'Semi-Realistic Art' },
                    { label: 'Sketch & Hand-Drawn – Classic pencil or charcoal portrait styles', value: 'Sketch & Hand-Drawn' },
                    { label: 'Watercolor Painting – Soft, dreamy brush strokes for a delicate look', value: 'Watercolor Painting' },
                    { label: 'Crayon & Children\'s Illustration – Playful, warm, and nostalgic', value: 'Crayon & Children\'s Illustration' },

                    { label: 'Japanese Anime – Become a beautifully drawn anime character', value: 'Japanese Anime' },
                    { label: 'Comic Book Style – Sharp lines, bold shading, straight from a graphic novel', value: 'Comic Book Style' },
                    { label: 'Ghibli Style – Enchanting and whimsical art reminiscent of Studio Ghibli films', value: 'Ghibli Style' },
                    { label: 'American Superhero – Marvel/DC-inspired epic transformations', value: 'American Superhero' },
                    { label: 'Chibi & Cute Style – Adorable big-eyed, small-body avatars', value: 'Chibi & Cute Style' },
                    { label: 'Cyberpunk – Futuristic neon aesthetics, AI-enhanced visuals', value: 'Cyberpunk' },
                    { label: 'Steampunk – A mix of gears, brass, and Victorian elegance', value: 'Steampunk' },

                    { label: 'Epic Fantasy – Knights, mages, elves, and mystical creatures', value: 'Epic Fantasy' },
                    { label: 'Fairy & Elven Style – Ethereal beauty with magical vibes', value: 'Fairy & Elven Style' },
                    { label: 'Gothic & Vampire – Dark, mysterious, and elegantly haunting', value: 'Gothic & Vampire' },
                    { label: 'Alien & Cosmic – Explore intergalactic sci-fi transformations', value: 'Alien & Cosmic' },
                    { label: 'Mecha Warrior – Futuristic robotic exosuits and high-tech armor', value: 'Mecha Warrior' },

                    { label: 'Oil Painting – Classic Renaissance-style portraits', value: 'Oil Painting' },
                    { label: 'Chinese Ink Painting – Elegant brush strokes and minimalistic beauty', value: 'Chinese Ink Painting' },
                    { label: 'Old-School Cartoon – Vintage Disney or 90s-style animation', value: 'Old-School Cartoon' },
                    { label: 'Pixel Art – 8-bit and 16-bit retro gaming nostalgia', value: 'Pixel Art' },
                    { label: 'Pop Art – Bold, colorful, Andy Warhol-inspired transformations', value: 'Pop Art' },

                    { label: 'Minimalist Line Art – Elegant and abstract hand-drawn effect', value: 'Minimalist Line Art' },
                    { label: 'Neon Glow – Futuristic lighting and high-contrast designs', value: 'Neon Glow' },
                    { label: 'Black & White Silhouette – Simplistic yet powerful aesthetic', value: 'Black & White Silhouette' },
                    { label: 'Street Graffiti – Vibrant, rebellious, urban-style artwork', value: 'Street Graffiti' },
                    { label: 'Caricature & Exaggerated Styles – Fun and humorous looks', value: 'Caricature & Exaggerated Styles' },

                    { label: 'Anime Character Cosplay – Dress up as famous anime heroes', value: 'Anime Character Cosplay' },
                    { label: 'Fantasy RPG Look – Get the look of a legendary RPG warrior', value: 'Fantasy RPG Look' },
                    { label: 'Furry & Animal-Inspired – Wolf ears, cat whiskers, or fox spirits', value: 'Furry & Animal-Inspired' },
                    { label: 'Sci-Fi Hacker & Cyber Assassin – High-tech, futuristic hacker vibes', value: 'Sci-Fi Hacker & Cyber Assassin' },
                    { label: 'Astronaut & Space Explorer – A journey beyond the stars', value: 'Astronaut & Space Explorer' },
                    { label: 'Other', value: '' }
                ]
            }],
            modalities: ['Image'],
            group: 3,
            temperature: 1,
            prompt_user: `Please redraw the given image in the specified style while preserving the original composition, details, and key elements as accurately as possible. Maintain the structure, proportions, and key features of the original image, applying only stylistic changes to match the specified style.
        
[Specified style]: {{art_style}}`,
        }, {
            objTypes: ['instatext'],
            label: '{content_type}...',
            action: 'draft',
            content_type: ['brainstorming', 'outline', 'article', 'blog', 'weibo', 'wechat_post', 'zhihu', 'xiaohongshu', 'press_release', 'script', 'creative_story', 'presentation', 'essay', 'poem', 'love_letter', 'weekly_report', 'swot', 'cons_pros', 'job_description', 'sales_email'],
            title: '头脑风暴',
            prompt: '',
            group: 3,
            temperature: 1,
            prompt_user: '写一篇{content_type}，关于如下主题：',
        }, {
            objTypes: ['markdown', 'flow', 'image'],
            label: '{content_type}...',
            action: 'draft',
            sub_items: ['outline', 'article', 'blog', 'question', 'twitter', 'weibo', 'facebook', 'press_release', 'script', 'creative_story', 'presentation', 'essay', 'poem', 'love_letter', 'weekly_report', 'swot', 'cons_pros', 'job_description', 'sales_email', 'more'],
            title: '头脑风暴',
            prompt: '',
            args: [{ name: 'topic', type: 'textline', required: false }, { name: 'outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            subTasks: [{
                prompt_user: '请写',
                label: '',
                to_arg: 'outline',
                action: 'outline',
            }],
            group: 3,
            temperature: 1,
            prompt_user: `写一篇{{sub_item}}，主题:{{topic}},要点或提纲:{{outline}}，其他要求:{{other_reqs}}`,
        }, {
            objTypes: ['widget'],
            action: 'explain_codes',
            title: 'Explain',
            group: 3,
            temperature: 0.7,
            prompt: `Please explain the functionality and execution process of the given code snippet`,
        }, {
            objTypes: ['widget'],
            action: 'read_email',
            title: 'AI Reply',
            group: 3,
            temperature: 0.7,
            prompt: `Given text represents an email that has been received. Please carefully analyze the content, identify the sender's intentions and objectives, and list them systematically item by item. Pay attention to the key points, requests, or purposes explicitly mentioned in the email. Your goal is to provide a clear and concise summary of what the sender is trying to convey.`,
        }, {
            objTypes: ['widget'],
            label: '{content_type}...',
            action: 'reply_email',
            title: 'AI Reply',
            prompt: '',
            args: [{ name: 'reply_outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            subTasks: [{
                dynamic_arg: {
                    name: 'email_outline',
                    type: 'text',
                    readOnly: true  //won't set to final prompt to LLM, just for user to read.
                },
                action: 'email_outline',
            }, {
                to_arg: 'reply_outline',
                action: 'reply_outline',
            }],
            group: 3,
            temperature: 1,
            prompt_user: '给定收到的邮件内容:```{{selected_text}}```\n请阅读提供的原邮件，理解发送者来信的目的、问题及请求，写一封回复邮件，回应发送者的问题或请求，给定的回复要点或提纲:```{{outline}}```，其他要求:```{{other_reqs}}```',
        }, {
            objTypes: ['widget'],
            action: 'twitter_reply',
            title: 'Tweet Reply',
            args: [{ name: 'reply_outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            group: 3,
            temperature: 0.7,
            prompt: `The context provided is a post on social media. Your task is to thoroughly analyze the content, understand the creator's intentions, and generate a reply that is both thoughtful and pertinent to the given post, based on the provided outline or key points. It's crucial that your response seamlessly aligns with the context and tone of the original post`,
        }, {
            objTypes: ['widget'],
            action: 'social_post_reply',
            title: 'Social Post Reply',
            args: [{ name: 'reply_outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            group: 3,
            temperature: 0.7,
            prompt: `You are a social media engagement specialist. Generate a response to the given social media post, following platform-specific guidelines:

## Platform-Specific Guidelines

- **LinkedIn**: Professional yet approachable; emphasize industry insights and business terminology in 2-3 concise paragraphs.
- **Reddit**: Match subreddit tone; use relevant references, links, and formatting (bullets, quotes) for readability.
- **Twitter**: Concise and engaging within character limits; use hashtags, mentions, and platform-specific language.
- **Facebook**: Conversational and relatable; focus on community engagement and balanced emotional expression.
- **ProductHunt**: Highlight product features and use cases; maintain constructive, supportive language with relevant product comparisons.

## General Response Guidelines

### General:
- Adapt tone to platform and match post’s energy.
- Ensure platform-friendly formatting, tags, and mentions where needed.
- Aim to encourage further discussion and keep responses directly relevant to the post.

### If reply outline provided:
- Follow provided points strictly
- Maintain logical flow between points

### If no reply outline provided:
- Address original post's key points
- Add value through insights/questions

## Output
Return only the response text, ready for posting, with no additional explanations or reasoning.`
        }, {
            objTypes: ['widget'],
            action: 'producthunt_voter_comment',
            title: 'Product Hunt Voter Comment',
            args: [{ name: 'reply_outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            group: 3,
            temperature: 0.7,
            prompt: `Please write a voter comment for Product Hunt based on the provided product details and maker/hunter comments. The comment should be insightful, concise, and aligned with the style typically seen on Product Hunt. 
            
            **Requirements:**
            1. Highlight a key advantage or innovation of the product.
            2. Share positive feedback as a potential user.
            3. The comment should reflect a clear understanding of the product's unique value.`,
        }, {
            objTypes: ['widget'],
            action: 'producthunt_maker_reply',
            title: 'Product Hunt Maker Reply',
            args: [{ name: 'reply_outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            group: 3,
            temperature: 0.7,
            prompt: `Please write a maker reply for Product Hunt based on the provided product details and voter comments. The reply should be thoughtful, engaging, and demonstrate appreciation for the feedback.
            
            **Requirements:**
            1. Acknowledge and thank the voters for their comments and support.
            2. Address any specific points or feedback mentioned in the comments.
            3. Highlight the team’s future plans or ongoing improvements related to the product, if relevant.`,
        }, {
            objTypes: ['widget'],
            action: 'quora_reply',
            title: 'Reply Question',
            args: [{ name: 'reply_outline', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            group: 3,
            temperature: 0.7,
            prompt: `You will be presented with question from Q&A site. Your task is to provide a thorough and well-reasoned answer to it.

            If the user provides specific ideas for replying the question, incorporate those into your response. However, if no such ideas or guidance is given, follow these steps:
            
            1. Analyze whether the question itself is well-formed and makes sense. If not, point out any ambiguities, logical flaws, or incorrect assumptions in the question.
            
            2. Identify the key points or aspects that need to be addressed to comprehensively answer the question.
            
            3. For each key point, provide a detailed explanation or analysis, drawing upon your knowledge base as well as logical reasoning.
            
            4. If applicable, offer different perspectives, counterarguments, or alternative viewpoints related to the question.
            
            5. Conclude with a summary that directly answers the core question based on your analysis.
            
            Your responses should be structured, well-organized, and comprehensive, covering all relevant angles of the question. Use examples, analogies, and evidence from credible sources to support your points where appropriate. Maintain an objective and impartial tone throughout.`
            // prompt: `Your task is to deliver an answer that is focused, insightful, and thoroughly researched to a given question. Begin with a clear and concise introduction to the topic, setting the stage for the reader. If the question is controversial, address any biases or misconceptions embedded in it. Key elements include an in-depth analysis where you delve into a comprehensive response, offering necessary evidence, arguments, and relevant case studies to support your answer. Maintain a balanced perspective, considering different angles, and addressing potential biases present in the question. Craft a response that is thought-provoking, engaging readers to think critically about the issue. Encourage a deeper understanding through your insights. Incorporate credible sources or studies to bolster the reliability of your response, and clearly reference any data or information you present. Keep your response informative, balanced, and respectful, aiming to contribute valuable insights to the platform's community. Ensure that the user-provided ideas seamlessly integrate into the overall structure of your response, maintaining coherence and relevance`,
        }, {
            objTypes: ['slides'],
            label: '生成标题',
            action: 'title',
            title: '生成标题',
            group: 5,
            temperature: 0.7,
            prompt: `You are a professional speech and slide editing assistant. Your task is to generate a concise, engaging, and accurate title that summarizes the content of a given slide.

## Input
You will receive a text passage representing the content of a slide. This may include paragraphs, bullet points, or other forms of information.

## Requirements
1. Carefully read and understand the given content.
2. Identify the core theme or central idea of the content.
3. Create a concise title that:
   - Accurately summarizes the main point of the content
   - Captures the audience's interest
   - Is of appropriate length (typically no more than 7-8 words)
   - Uses positive, impactful language
   - Avoids overly technical or obscure terms
4. Ensure the title remains consistent with the content and doesn't introduce concepts not mentioned.
5. If the content includes numbers or statistics, consider incorporating them cleverly into the title.

## Output
Provide a title that meets the above requirements. It should start with '## '. 
If you believe there are multiple good options, you may provide 2-3 alternative titles.`
        }, {
            objTypes: ['slides'],
            label: '生成大纲',
            action: 'keypoints',
            title: '大纲',
            group: 5,
            temperature: 1,
            prompt: `You are an expert in creating concise and impactful slide content. Your task is to generate a title and key points for a single slide based on either a given text or a brief topic. Focus solely on creating content for one slide. Follow these guidelines:

1. Generate a slide title:
   - Create a short, attention-grabbing title that encapsulates the main idea
   - Ensure the title is no more than 5-7 words long
   - Make it descriptive yet concise

2. If given a detailed text:
   - Identify the core message or main idea
   - Extract 3-6 key points that best represent this core message

3. If given a brief topic:
   - Expand on the topic by considering its most important aspects
   - Generate 3-6 key points that provide a focused overview of the topic

4. For the key points (both scenarios):
   - Ensure each key point is clear, concise, and informative
   - Use short phrases for each key point
   - Make sure the points are cohesive and relate to the slide title
   - Include just enough detail to fill a single slide without overwhelming it

5. Format your response with Markdown as follows:
   - First line: The slide title, start with '## '
   - Followed by a list of 3-6 key points

Remember, your goal is to create a title and key points for a single slide only. Avoid unnecessary details or explanations - focus on the core message of each point.`
        }, {
            objTypes: ['slides'],
            label: '生成当前幻灯片内容',
            action: 'slide',
            title: '幻灯片',
            group: 5,
            temperature: 1,
            // prompt: `I want you to act as a slideshow creator and use your expertise to generate an engaging and informative presentation based on the given topic. Your prompt should include relevant visuals and key points to help the audience understand the topic better. Make sure to highlight the most important aspects of the topic and use language that is clear and easy to understand. Let's create a slideshow that will captivate and inform our audience!`
            // prompt: `I want you to act as a slideshow creator and use your expertise to generate a slide for given topic. It should contain two parts: key points and speech script. The key points will highlight the most important aspects of the topic. Speech script is the text you speak to the audience. The speech script should provide detailed and informative explanations of the topic and use language that is clear and easy to understand, avoid using any opening or closing pleasantries, greetings or polite language. Start speech script with "Notes:"`
            prompt: `You are a professional slide content generator. Create a comprehensive single-page slide based on the user's topic or content.

## Output Format

Return strictly in this JSON format:

\`\`\`json
{
    "generated": {
        "slide": {
            "content": "slide content in markdown format",
            "notes": "speaker notes in markdown format"
        }
    }
}
\`\`\`

## Content Guidelines

### Slide Content:
- Use '##' for main title, '###' for subtitles
- **Multi-level lists for parallel/comparison content**: Top-level 'li' items become separate cards in the app, nested 'li' items stay within the same card
- Support **tables**, **LaTeX math** ('$...$'), **Mermaid diagrams**, **bold/italic text**, and **code blocks**

### Speaker Notes:
- Provide detailed explanations and background information
- Include speaking tips, emphasis points, and timing suggestions
- Add supplementary information not shown on the slide

## Structure Examples

**Comparison content:**
Use multi-level lists for comparisons, contrasts, or parallel points:
\`\`\`markdown
- **Option A**
  - Pros: Low cost, quick implementation
  - Cons: Limited features
  - Best for: Small projects

- **Option B**
  - Pros: Full features, scalable
  - Cons: High cost, longer timeline
  - Best for: Enterprise projects
\`\`\`

**Data table:**
\`\`\`markdown
| Metric | Q1 | Q2 | Q3 | Q4 |
|--------|----|----|----|----|
| Revenue | 100M | 120M | 150M | 180M |
| Growth | - | 20% | 25% | 20% |
\`\`\`

**Mermaid diagram:**
\`\`\`markdown
\`\`\`mermaid
graph TD
    A[Analysis] --> B[Design]
    B --> C[Development]
    C --> D[Testing]
\`\`\`
\`\`\`

## Requirements
- **Self-contained**: Complete information in a single slide
- **Visual hierarchy**: Clear structure with proper heading levels
- **Actionable notes**: Practical guidance for presenters

Generate the slide based on the user's input.`
        }, {
            objTypes: ['slides'],
            label: '生成多页幻灯片',
            action: 'slideshow',
            title: '多页幻灯片',
            group: 5,
            temperature: 1,
            args: [{ name: 'topic', type: 'text' }, { name: 'other_reqs', type: 'text' }],
            prompt: `You are a professional slide creation assistant. Generate well-structured, content-rich slides based on the user's topic or content.

## Output Format

Return slides in this exact JSON format:

\`\`\`json
{
    "generated": {
        "title": "Overall slide deck title",
        "slides": [
            {
                "content": "Slide content in Markdown format",
                "notes": "Speaker notes in Markdown format"
            }
        ]
    }
}
\`\`\`

## Content Guidelines

### Slide Content
- Use Markdown formatting
- Keep content concise and presentation-friendly
- Focus each slide on one core topic
- Use proper heading hierarchy (## ###)

### Speaker Notes
- Provide detailed speaking guidance and context
- Include key point explanations and expansions
- Suggest timing and potential Q&A preparation

## Supported Formats

### Nested Lists (for parallel/comparison content)
Use multi-level lists for comparisons, contrasts, or parallel points:
\`\`\`markdown
- **Main Point A**
  - Supporting evidence 1
  - Supporting evidence 2
  - Data or examples
- **Main Point B**
  - Supporting evidence 1
  - Supporting evidence 2
  - Data or examples
\`\`\`

### Tables
\`\`\`markdown
| Item | Pros | Cons |
|------|------|------|
| Option A | Low cost | Average efficiency |
| Option B | High efficiency | High cost |
\`\`\`

### LaTeX Math
\`\`\`markdown
Inline: $E = mc^2$
Block: $$\frac{d}{dx}\int_{a}^{x} f(t)dt = f(x)$$
\`\`\`

### Mermaid Diagrams
\`\`\`markdown
\`\`\`mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action]
    B -->|No| D[End]
\`\`\`
\`\`\`

## Slide Structure

1. **Title slide**: Title, subtitle, presenter info
2. **Agenda slide**: Outline of main content
3. **Content slides**: Topic-specific detailed content
4. **Summary slide**: Key points recap
5. **Closing slide**: Thank you, contact info, Q&A

## Design Principles

- **Visual hierarchy**: Use headings, bold, italics effectively
- **Information density**: Keep slides focused, avoid overload
- **Logical flow**: Clear connections between slides
- **Engagement**: Include questions or interaction prompts where appropriate

Generate 8-15 professional slides with clear themes, accurate content, and practical speaker notes`
        }, {
            objTypes: ['slides'],
            label: '生成讲解',
            action: 'speakernotes',
            title: 'Speaker Notes',
            group: 5,
            temperature: 0.8,
            prompt: `Generate focused speaker notes for slide content, prioritizing complex or critical points over simple material.

## Analysis Guidelines
1. **Identify complexity levels**: Distinguish between basic and advanced concepts
2. **Prioritize critical content**: Focus on key insights, difficult concepts, and high-impact points
3. **Skip obvious material**: Avoid notes for self-explanatory or straightforward content

## Note Generation Rules
1. **Selective coverage**: Only create notes for:
   - Complex technical concepts requiring explanation
   - Critical points needing emphasis or context
   - Controversial or nuanced topics
   - Areas likely to generate audience questions

2. **Content structure**:
   - Lead with the most important insights
   - Provide context and background for complex points
   - Include supporting evidence (statistics, examples, case studies)
   - Anticipate and address potential audience questions

3. **Quality over quantity**:
   - Write concise, impactful explanations
   - Use clear headings for different complexity levels
   - Maintain logical flow between related concepts
   - End with key takeaways for critical points only

**Output**: Focused speaker notes that highlight essential and complex content while omitting unnecessary elaboration on simple topics.`
        }, {
            objTypes: ['slides'],
            label: '生成讲解',
            action: 'speechscript',
            title: '演讲脚本',
            group: 5,
            temperature: 0.8,
            prompt: `As an expert speech writer, create a compelling speech based on the provided presentation slide(s), including any Speaker Notes. Your task is to craft a speech that effectively communicates the slide content while resonating with the intended audience. Follow these steps:

1. Analyze Content:
   - Review slide content, including main topics, key points, data, and visuals.
   - Pay special attention to Speaker Notes for additional context and instructions.

2. Determine Audience:
   - Infer audience characteristics (profession, expertise level, age group, interests) from the content and context.
   - Briefly describe your audience assessment.

3. Adapt Style:
   - Choose appropriate tone and style based on audience and Speaker Notes guidance.
   - Explain your style choice.

4. Structure Speech:
   - Create an engaging introduction, logically organized body, and strong conclusion.
   - Incorporate any structural elements suggested in Speaker Notes.

5. Enhance Content:
   - Elaborate on slide points with context, examples, and explanations.
   - Include relevant stories or case studies.
   - Address potential audience questions.
   - Integrate additional information from Speaker Notes.

6. Add Engagement Elements:
   - Insert rhetorical questions or interactive components.
   - Include clear takeaways or calls-to-action.
   - Implement engagement strategies from Speaker Notes.

7. Write the Speech:
   - Compose a natural, conversational speech incorporating all elements above.
   - Aim for appropriate length (typically 5-15 minutes unless specified otherwise).
   - Ensure all relevant Speaker Notes information is integrated.

8. Review and Refine:
   - Verify all key points from slides and Speaker Notes are covered.
   - Ensure good flow and engagement throughout.
   - Confirm all Speaker Notes instructions are addressed.

Provide the speech text with brief notes on your audience analysis and style choices. Tailor the speech to the audience characteristics and Speaker Notes guidance for maximum impact. The final speech should seamlessly incorporate insights from the Speaker Notes.`
        }, {
            objTypes: ['slides'],
            label: '生成讲解',
            action: 'speech_suggestions',
            title: '演讲建议',
            group: 5,
            temperature: 1,
            prompt: `**Role:** You are a professional speech coach skilled in enhancing presentation techniques. Your task is to provide tailored advice based on given slide content (including possible speaker notes) to help the user deliver a more impactful presentation to a specific audience.

**Input:**
- Slide text.
- Descriptions of visuals (charts, images, etc.), if any.
- Speaker notes, if available.

**Your Task:**
1. **Analyze:** Carefully assess the slide content, notes, and theme. Infer possible audience characteristics (e.g., industry background, knowledge level, interests).
2. **Provide Tailored Advice:** Offer specific, actionable suggestions based on your analysis and inferred audience traits, covering areas such as:
   - **Content Organization:** Recommend how to present information logically to engage and maintain audience attention.
   - **Opening & Closing:** Suggest creative openings and strong closing techniques for a lasting impact.
   - **Key Points Emphasis:** Advise on methods to highlight core messages, such as repetition, visual aids, or storytelling.
   - **Audience Interaction:** Recommend suitable interaction techniques to enhance engagement.
   - **Visual Aids:** Advise on effective use of charts, images, etc., to improve information delivery.
   - **Language & Rhetoric:** Suggest appropriate language styles and rhetorical devices to match audience characteristics.
   - **Time Management:** Offer time allocation tips to ensure a tight, content-rich presentation.
   - **Body Language:** Advise on how to use gestures and movement to enhance the presentation’s impact.
   - **Content Extension:** Propose related topics or extensions to broaden the discussion and add depth.

3. **Prioritize:** Select up to five key suggestions from the above categories, based on the presentation theme and audience traits, to keep advice focused and impactful.

4. **Feedback & Optimization:** Highlight strengths of the slide content and presentation plan, and suggest specific improvements.

**Output:**
1. **Overall Impression:** Summarize the strengths and areas for improvement.
2. **Key Suggestions:** List the most important advice, explaining the logic and expected impact.
3. **Examples & Tips:** Provide examples or practical tips where applicable to aid implementation.

**Guidelines:**
- Maintain a positive, constructive tone.
- Tailor all advice to the presentation theme and audience characteristics, offering only the most critical suggestions (up to five).
- Encourage users to adjust the advice to fit their style and personality.

Provide in-depth, insightful, and targeted presentation advice based on these guidelines.`
        }, {
            objTypes: ['slides'],
            label: '生成讲解',
            action: 'slides_optimize',
            title: '幻灯片优化',
            group: 5,
            temperature: 1,
            prompt: `# Slide Content Optimization and Enhancement Prompt

As a presentation design expert, analyze, optimize, and enhance the given slide content to create clear, impactful, and engaging slides:

1. Content Analysis:
   - Identify main theme, target audience, and key points
   - Assess logical flow, content depth, and current engagement level
   - If provided, analyze speaker notes for additional insights

2. Optimization and Enhancement:
   - Clarify and emphasize key points for immediate impact
   - Ensure each slide has a clear, focused message
   - Simplify language and remove unnecessary information
   - Add content where needed to enrich understanding and maintain logical flow
   - Create a compelling narrative structure

3. Visual and Structural Improvements:
   - Suggest impactful visuals (charts, images, icons) to support and highlight key content
   - Propose attention-grabbing elements (e.g., thought-provoking questions, striking statistics)
   
4. Engagement Strategies:
   - Incorporate interactive elements to maintain audience interest
   - Suggest ways to make complex information more digestible and memorable
   - Recommend techniques to create emotional connections with the audience

5. Guidelines:
   - Maintain the original theme while enhancing clarity and impact
   - Ensure a logical, engaging progression of ideas throughout

Provide specific optimization suggestions with brief explanations. Then, generate the improved slide content, ensuring it is clear, impactful, and engaging.

**Output your final generation with the following RFC8259-compliant JSON structure (Do not output your intermediate thinking process):**

\`\`\`json
{
    "generated": {
        "optimization_explained": "Optimization plans explained concisely",
        "slides": [
            {
                "content": "slide content",
                "notes": "slide notes"
            },
        ]
    }
}
\`\`\``
        }, {
            objTypes: ['flow'],
            label: '根据内容生成图片',
            action: 'flow_image_prompt',
            title: '摘要',
            group: 1,
            temperature: 0.7,
            // prompt_user: '[Generate an image]: {{selected_text}}'
        }, {
            objTypes: ['slides'],
            label: '根据内容生成图片',
            action: 'generate_image',
            title: '摘要',
            group: 1,
            temperature: 0.7,
            modalities: ['Image'],
            args: [{
                name: 'art_style',
                type: 'select',
                value: 'illustration',
                options: [
                    { label: 'Business Professional', value: 'business-professional' },
                    { label: 'Flat Design', value: 'flat-design' },
                    { label: 'Illustration', value: 'illustration' },
                    { label: 'Tech Digital', value: 'tech-digital' },
                    { label: 'Clean Minimal', value: 'clean-minimal' },
                    { label: 'Infographic', value: 'infographic' },
                    { label: 'Other', value: '' }
                ]
            }],
            prompt_user: `Generate an image based on the following description:

[User input]: {{selected_text}}

[Specified style]: {{art_style}}

Requirements:
- High quality and detailed
- Visually appealing composition
- Accurate representation of all described elements
- Professional lighting and color balance
- If no style is specified, use photorealistic style`
        }, {
            objTypes: ['flow_background'],
            label: '根据内容生成图片',
            action: 'flow_generate_image',
            title: '摘要',
            group: 1,
            temperature: 0.7,
            modalities: ['Image'],
            prompt_user: '[Generate an image]: {{selected_text}}'
        }, {
            objTypes: ['flow_background'],
            label: '根据内容生成图片',
            action: 'flow_edit_image_prompt',
            title: '摘要',
            group: 1,
            temperature: 0.7,
            prompt_user: '[Generate an image]: {{selected_text}}'
        }, {
            objTypes: ['flow_background'],
            label: '根据内容生成图片',
            action: 'flow_edit_image',
            title: '摘要',
            group: 1,
            temperature: 0.7,
            modalities: ['Image'],
            prompt_user: '[Generate an image]: {{selected_text}}'
        }, {
            objTypes: ['ril', 'flow'],
            label: '对内容进行摘要',
            action: 'summary',
            title: '摘要',
            group: 1,
            temperature: 0.6,
            prompt: `Craft a concise yet comprehensive summary of the provided content (article, report, or other text). Your summary should:

1. Capture the main points, key arguments, and essential information
2. Condense the content while retaining its substance
3. Offer a quick yet thorough understanding of the core ideas and conclusions
4. Be no longer than 5 sentences or 150 words, whichever is shorter

Prioritize brevity and clarity to save reader time while ensuring the summary remains informative and insightful`
        }, {
            objTypes: ['flow_background'],
            label: '对内容进行摘要',
            action: 'summary_keypoints',
            title: '摘要',
            group: 1,
            temperature: 0.6,
            prompt: `Please analyze the given text as follows:

1. Key Points/Takeaways:
   - List 3-5 essential points
   - Each point should be 10-20 words
   - Focus on key arguments, main points, and any specific data explicitly mentioned
   - Present points in order of importance

2. Then provide a brief contextual summary (no more than 4 sentences) that:
   - Summarizing the text
   - Focuses on broader implications or relationships not covered in the key points
   - Explains the significance or context of the topic
   - Avoids repeating information from the key points

Please maintain objectivity and only include information explicitly stated in the text.

**Output your final generation with the following RFC8259-compliant JSON structure (Do not output your intermediate thinking process):**

\`\`\`json
{
    "generated": {
        "summary": "Optimization plans explained concisely",
        "keypoints": ["key point 1", "key point 2", ...]
    }
}
\`\`\`
`
        }, {
            objTypes: ['doc', 'ril'],
            label: '生成幻灯片',
            action: 'xSlides',
            title: 'slideshow',
            group: 1,
            temperature: 0.7,
            prompt: `You are a presentation expert. Convert the provided text into structured slides.

## Output Format

Return ONLY valid JSON in this exact structure:

\`\`\`json
{
    "generated": {
        "title": "Overall presentation title",
        "slides": [
            {
                "content": "Slide content in Markdown",
                "notes": "Speaker notes in Markdown"
            }
        ]
    }
}
\`\`\`

## Content Guidelines

### Slide Content (content)
- Use \`##\` for slide titles, \`###\` for subtitles
- 3-7 key points per slide
- Use multi-level list for comparisons/parallel content:

\`\`\`markdown
## Main Title

- **Option A**
  - Advantage: description
  - Disadvantage: description

- **Option B** 
  - Advantage: description
  - Disadvantage: description
\`\`\`

- Support **tables**, **LaTeX math** ('$...$'), **Mermaid diagrams**, **bold/italic text**, and **code blocks**

### Speaker Notes (notes)
- Detailed explanations for each slide
- Transition phrases between points
- Additional context and examples

## Structure Rules

1. Start with title slide + overview
2. Organize content logically (5-15 slides total)
3. End with summary/conclusion
4. Use numbered lists for processes
5. Use tables/lists for data presentation

## Requirements

- Extract key information only
- Maintain logical flow
- Keep content concise
- Ensure proper Markdown formatting
- Make multi-level list when showing comparisons or parallel concepts`
        }, {
            objTypes: ['ril'],
            label: '生成大纲',
            action: 'highlights',
            title: '看点',
            group: 1,
            deprecated: true,
            temperature: 0.6,
            prompt: 'Please extract and present the key highlights of the given article. Focus on identifying the most significant points, crucial insights, and notable findings. Your output should succinctly highlight the essential aspects of the article, helping readers grasp the core content efficiently'
        }, {
            objTypes: ['flow', 'ril'],
            label: '思维导图',
            action: 'flow_mindmap',
            title: '思维导图',
            group: 1,
            temperature: 1,
            prompt: `**Task**: Generate a flexible mind map based on the given text, with up to three levels of hierarchy to help readers quickly grasp its core concepts and main ideas.

### **Requirements**:
1. **Text Analysis**:
   - Carefully read and understand the input text.
   - Identify the central topic, key concepts, and their relationships.

2. **Information Extraction**:
   - Extract the main topic, major concepts, key points, and relevant details.
   - Focus on logical connections and the level of detail required for each concept.

3. **Mind Map Structuring**:
   - Create a flexible hierarchy with up to three levels:
     - Place the main topic at the center.
     - Represent major concepts as primary branches.
     - Use sub-branches to capture key points or details under each primary branch.
     - Add tertiary branches only where further elaboration is necessary for clarity or completeness.
   - Ensure that branches and sub-branches are concise, logically organized, and relevant to the theme.

4. **Quality Assurance**:
   - Maintain a clear and coherent structure, balancing depth and simplicity.
   - Avoid unnecessary complexity while ensuring adequate detail where needed.

5. **Summarization**:
   - Identify 3-5 of the most valuable insights:
     - Highlight groundbreaking, influential, or practical ideas.
     - Summarize the main takeaways from the mind map.

6. **Output Specification**:
   - Return the final mind map using the following JSON format (RFC8259-compliant).
   - Avoid including intermediate thinking processes or explanations in the output.

\`\`\`json
{
    "generated": {
        "title": "A concise title for generated mindmap",
        "central_topic": "Main topic of given text",
        "primary_branches": [
            {
                "name": "Primary Branch 1",
                "branches": [
                    {
                        "name": "Sub Branch 1.1",
                        "branches": [
                            "Tertiary Branch 1.1.1",
                            "Tertiary Branch 1.1.2",
                            ...
                        ]
                    },
                    {
                        "name": "Sub Branch 1.2"
                        // No tertiary branches needed
                    },
                    ...
                ]
            },
            {
                "name": "Primary Branch 2",
                "branches": [
                    {
                        "name": "Sub Branch 2.1",
                        "branches": [
                            "Tertiary Branch 2.1.1",
                            "Tertiary Branch 2.1.2",
                            ...
                        ]
                    },
                    ...
                ]
            },
            ...
        ],
        "summary_insights": [
            "Most valuable insight 1",
            "Most valuable insight 2",
            ...
        ]
    }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '思维导图',
            action: 'flow_mindmap_askai',
            title: '思维导图',
            group: 1,
            temperature: 1,
            prompt: `You are an expert at creating structured mindmaps from any given content or query. Your task is to:

1. **Understand** the user's question or instruction thoroughly
2. **Generate** a comprehensive response to their query
3. **Structure** your response as a hierarchical mindmap in JSON format

## Output Format

Always respond with a JSON structure following this exact format:

\`\`\`json
{
    "generated": {
        "title": "A concise title for generated mindmap",
        "central_topic": "Main topic of given text",
        "primary_branches": [
            {
                "name": "Primary Branch 1",
                "branches": [
                    {
                        "name": "Sub Branch 1.1",
                        "branches": [
                            "Tertiary Branch 1.1.1",
                            "Tertiary Branch 1.1.2"
                        ]
                    },
                    {
                        "name": "Sub Branch 1.2"
                    }
                ]
            },
            {
                "name": "Primary Branch 2",
                "branches": [
                    {
                        "name": "Sub Branch 2.1",
                        "branches": [
                            "Tertiary Branch 2.1.1"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Most valuable insight 1",
            "Most valuable insight 2",
            "Most valuable insight 3"
        ]
    }
}
\`\`\`

## Guidelines

- **Central Topic**: Extract or create the main theme from the user's query
- **Primary Branches**: Create 3-6 main categories that comprehensively cover the topic
- **Sub Branches**: Break down each primary branch into 2-5 logical subdivisions
- **Tertiary Branches**: Add specific details, examples, or sub-points where beneficial (optional)
- **Summary Insights**: Provide 2-4 key takeaways or actionable insights
- Keep branch names concise but descriptive (5-8 words max)
- Ensure logical hierarchy and balanced structure
- Focus on clarity and practical value

## Response Requirements

- Provide ONLY the JSON structure - no additional text
- Ensure valid JSON formatting
- Make the mindmap comprehensive yet digestible
- Adapt complexity based on the topic's scope
`
        }, {
            objTypes: ['instanote', 'markdown', 'flow_notes', 'flow'],
            label: 'Reflect',
            action: 'reflect',
            title: '反思',
            group: 1,
            temperature: 0.7,
            prompt: `You are a wise mentor with strong critical thinking skills. Analyze the given user notes and engage in an reflection to broaden the user's perspective and deepen their thinking. Follow these steps:

1. Summarize the main points of the user's notes.
2. Identify assumptions, biases, or limitations in the user's thinking.
3. Pose thought-provoking questions to challenge these assumptions.
4. Suggest alternative viewpoints or approaches to consider.
5. Recommend resources or areas for further exploration.
6. Provide constructive feedback on the strengths and areas for improvement in the user's analysis.

Frame your response as a reflection to the notes.`
        }, {
            objTypes: ['ril'],
            label: '生成大纲',
            action: 'bullet',
            title: '大纲',
            group: 1,
            deprecated: true,
            temperature: 0.6,
            prompt: 'Please extract and present the key highlights of the given article. Focus on identifying the most significant points, crucial insights, and notable findings. Your output should succinctly highlight the essential aspects of the article, helping readers grasp the core content efficiently'
        }, {
            objTypes: ['instanote', 'ril', 'markdown', 'slides'],
            label: '进行头脑风暴',
            action: 'brainstorming',
            title: '头脑风暴',
            group: 1,
            temperature: 1,
            prompt: `You are an AI assistant tasked with analyzing given text and generating ideas related to the main topic or question identified in the text. 

            The process will be as follows:
            
            1. I will provide you with a piece of text.
            2. Your first task is to carefully read the text and identify the central topic, theme, or question that the text is addressing.
            3. Once you have identified the main topic or question, clearly state it in your response.
            4. After stating the main topic or question, you will then perform a brainstorming session, generating as many ideas, thoughts, and perspectives related to that topic as possible.
            5. Your brainstorming should aim to explore the topic from multiple angles, considering different viewpoints, implications, and potential solutions or approaches.
            6. Present your ideas in a bulleted or numbered list, with each point being a concise but thought-provoking idea or perspective related to the main topic or question.
            
            Remember, the key objectives are to accurately identify the central topic or question from the given text, and then engage in a robust brainstorming session to explore that topic in depth, generating a diverse range of ideas and perspectives.`
            // prompt: `I want you to act as a brainstorming partner and help us generate ideas based on given topic. What are some possible related ideas or topics that come to mind? Let's jot them down and see where they take us`
        }, {
            //     objTypes: ['slides'],
            //     label: '生成讲解',
            //     action: 'speechscript',
            //     title: '演讲脚本',
            //     group: 1,
            //     temperature: 0.8,
            //     prompt: `I want you to act as a professor or professional, user will give you key points on a slide shown to viewers, you will reply with a speech script that allows the audience to understand the content of the slide. Explain it using simple words and examples that will make it easy for young readers to comprehend. You can also use analogies, illustrations or stories to help them understand the topic better. Avoid using any opening or closing pleasantries, greetings or polite language, dive right into the topic without unnecessary introductions`
            // }, {
            objTypes: ['instanote'],
            label: '主题讲解',
            action: 'topicspeech',
            title: '主题讲解',
            group: 1,
            temperature: 0.8,
            // prompt: `I want you to act as a slideshow creator and use your expertise to generate an engaging and informative presentation based on the given topic. Your prompt should include relevant visuals and key points to help the audience understand the topic better. Make sure to highlight the most important aspects of the topic and use language that is clear and easy to understand. Let's create a slideshow that will captivate and inform our audience!`
            prompt: `I want you to act as a slideshow creator and use your expertise to generate a slide for given topic. It should contain two parts: key points and explanation. The key points will highlight the most important aspects of the topic. Explanation is the text you speak to the audience. The explanation should provide detailed and informative explanations of the topic and use language that is clear and easy to understand. You can also use analogies, illustrations or stories to help them understand the topic better`
        }, {
            objTypes: ['ril', 'instanote', 'instatext', 'markdown', 'slides'],
            label: '问几个相关问题',
            action: 'ask_questions',
            title: '问题',
            group: 1,
            temperature: 1,
            prompt: 'I want you to act as a question generator and help me come up with some relevant questions based on the given text. The questions should help user better understand the main ideas or key concepts discussed in the text, or will encourage him to think critically about the content and its implications. To generate these questions, you could consider the tone, style, and overall message of the text, as well as the specific words and phrases used by the author. Additionally, you could think about your own personal interests and experiences, and how they relate to the content of the text. By asking relevant and thought-provoking questions, you can deepen your understanding of the text and engage in a more meaningful and insightful dialogue with others who have also read it'
        }, {
            objTypes: ['flow', 'ril'],
            label: '分解',
            action: 'breakdown',
            goodAt: 'breakdown',
            title: '分解',
            group: 1,
            temperature: 1,
            prompt: `**Task:** Conduct a comprehensive analysis and breakdown of the complex topics or ideas in a given text to enhance understanding or facilitate implementation.

**Guidelines:**

1. **Identify the Topic:**
   - Analyze the text to grasp the core topic.
   - Determine the type of topic (e.g., conceptual discussion, problem-solving).
   - Consider the background and impact of the topic.

2. **Context Analysis:**
   - Explore the background, real-world significance, and future implications of the topic.
   - Identify related factors, challenges, and opportunities.

3. **Flexible Breakdown:**
   - Choose an appropriate method to break down the topic based on its nature (e.g., thematic, chronological, problem-solution, comparative).

4. **Thorough Breakdown:**
   - Divide the topic into mutually exclusive and collectively exhaustive components.
   - Clearly describe each sub-topic.

5. **Multidimensional Thinking:**
   - Reflect different perspectives and interdisciplinary connections.

6. **Validation and Reflection:**
   - Ensure the breakdown accurately and comprehensively represents the original topic.
   - Evaluate whether the breakdown achieves the goal of simplifying the complex content.

**Main Objective:** Transform complex topics or problems into clear, actionable sub-topics or sub-problems to promote deeper understanding and actionable steps.

**Output your final generation with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):**
\`\`\`json
{
    "generated": 
    {
        "main_subject": "Main Subject/Idea",
        "subtopics": ["Subtopic 1", "Subtopic 2", "Subtopic 3"]
    }
}
\`\`\``
        }, {
            objTypes: ['flow_background'],
            label: '问几个相关问题',
            action: 'related_questions_topics',
            title: '问题',
            group: 1,
            temperature: 1,
            prompt: `You are an assistant specialized in helping users explore both the depth and breadth of knowledge. Your task is to analyze the text provided by the user and generate relevant topics and questions to facilitate further learning.

## Input Description
The user will provide a text passage, which could be an article excerpt, a concept description, educational content, or any other form of text.

## Your Task
1. Carefully read and understand the provided text content
2. Based on the text content, provide the following two exploration paths:
   - **Related Topics**: Provide 3 topics that relate to the original text but extend in different directions, suitable for breadth exploration
   - **In-depth Questions**: Provide 3 questions that delve into the core content of the original text, suitable for depth exploration

## Output Format
Output your final generation with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):

\`\`\`json
{
    "generated": 
        {
            "summary": "a short and concise summary of the given text",
            "related_topics": ["topic 1", "topic 2", "topic 3"],
            "related_questions": ["question 1", "question 2", "question 3"]
        }
}
\`\`\`

## Quality Requirements
- Related topics should have clear connections but should not repeat the core content of the original text; instead, they should extend to adjacent fields
- Questions should be exploratory and trigger critical thinking, rather than simple yes/no questions or questions directly answerable from the text
- All topics and questions should have educational value and be suitable for further research by the user
- The topics and questions provided should be diverse, covering different angles and domains`
        }, {
            objTypes: ['flow_background'],
            label: '搜索结果总结',
            action: 'search_summary',
            title: '搜索结果总结',
            group: 1,
            temperature: 1,
            prompt: `Please summarize and analyze the information from the search results I provide, then answer my original question. The specific requirements are:

            1. Carefully read the search results, evaluating the sources' credibility and relevance.
            2. Filter out the most relevant and reliable information, focusing on high-quality sources.
            3. Based on the credible information, provide a comprehensive, objective summary of the best answer to the original question.
            4. In your summary, appropriately incorporate your background knowledge to supplement content possibly not covered in the search results.
            5. If the search results are insufficient for a satisfactory answer, directly state the reasons and specify what additional information is needed.
            6. When responding, first give a concise, direct answer to the original question, then explain how you arrived at that answer using the search results, including the evaluation to the sources' credibility and relevance.
            
            Please use both the search information and your knowledge to provide the most comprehensive and reliable direct answer, followed by a transparent explanation of your analysis process.
            `
        }, {
            objTypes: ['flow_background'],
            label: '搜索关键词优化',
            action: 'search_optimize',
            title: '搜索关键词优化',
            group: 1,
            temperature: 1,
            prompt: `As a search keyword optimization expert, your task is to help users refine their search queries to obtain more accurate and relevant search results. Please follow these steps:

1. Carefully analyze the original search keywords or questions provided by the user.

2. Infer the user's potential search intent and true information needs. Consider the following aspects:
   - What type of information might the user be looking for?
   - What deeper goals or questions might be behind their query?
   - What relevant context or background information might have been omitted?

3. Based on your analysis, propose 5 optimized search query suggestions. These suggestions should:
   - More accurately reflect the user's real needs
   - Include more specific and relevant terms
   - Consider synonyms or related concepts
   - Appropriately narrow or broaden the search scope
   - Utilize advanced search techniques (such as using quotation marks, site-specific searches, etc.)

Return only the JSON format output, with the structure defined below. The language requirements for the return should only apply to the JSON field values, not impact the JSON structure itself.
**Output your final generation with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):**
\`\`\`json
{
    "generated": {
        "optimized": [
            "Optimized Version 1",
            "Optimized Version 2",
            "Optimized Version 3", 
            "Optimized Version 4",
            "Optimized Version 5"
        ]
    }
}
\`\`\`

Remember, the goal is to help users find the information they truly need, not just repeat their initial query. Your suggestions should be both creative and practical.`
        }, {
            objTypes: ['flow_background'],
            label: '问题优化',
            action: 'question_optimize',
            title: '问题优化',
            group: 1,
            temperature: 1,
            prompt: `You are an expert question optimizer, your task is to help users formulate better questions, leading to more valuable answers. Please follow these steps:

1. **Understand User intent**
   - Carefully analyze the user's question to identify fundamental/underlying needs or intentions.
   - Consider potential goals or context that the user may not have explicitly expressed.

2. **Evaluate Question Quality**
   - Check the validity of the question, including:
     - Whether the premises of the question are correct
     - If the described phenomena in the question actually exist
     - Whether the factual statements in the question are accurate
     - Whether it contains unverified assumptions
     - If there are any self-contradictions
   - If the question itself is valid, assess the value of the question. Consider the following factors:
     - If this question is answered, will it provide valuable information, practical guidance or have meaningful impact?
     - Will this question promote deeper discussion or research?

3. **Reconstruct the Question**
   - Based on your understanding of the user's fundamental needs, propose new question angles.
   - Think creatively, stepping outside the user's original thought framework.
   - From different or more macro perspectives, formulate 5 more specific and effective versions of the question.

Remember, your goal is to help users obtain truly valuable answers. Through this process, you will help users think more clearly about their needs and pose more insightful questions.

Return only the JSON format output, with the structure defined below. The language requirements for the return should only apply to the JSON field values, not impact the JSON structure itself.
**Output your final generation with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):**
\`\`\`json
{"generated": {"analysis": "type: markdown formatted detailed analysis to the original question", "optimized": ["Optimized Version 1","Optimized Version 2","Optimized Version 3","Optimized Version 4","Optimized Version 5"]}}
\`\`\`

In the output JSON, "detailed analysis to the original question" should includes (do not include optimized questions here, put them to JSON field "optimized"):
1. Underlying intent:
   - "Underlying needs or intentions"
   - "Potential goals or context"
2. Validity of the question
3. Value of the question`
        }, {
            objTypes: ['flow_background'],
            label: '问题优化',
            action: 'prompt_optimize',
            title: '问题优化',
            group: 1,
            temperature: 1,
            prompt: `# AI Prompt Optimizer

## Role Definition
You are a professional AI Prompt Engineer specializing in helping users optimize their prompts to make them clearer, more effective, and better aligned with their intended goals.

## Optimization Process

### Step 1: Understand Original Requirements
1. **Carefully analyze the user's original prompt**
2. **Identify the user's true intent and objectives**
3. **Discover vague or unclear elements in the prompt**

### Step 2: Problem Diagnosis
Check if the original prompt has any of the following issues:
- Unclear or overly broad objectives
- Missing specific output format requirements
- Lack of contextual information
- Vague instruction phrasing
- Missing constraints
- No examples provided
- Unclear role definition

### Step 3: Structured Optimization
Restructure the prompt using the following framework:

\`\`\`
## Role Assignment
[Clearly define what role the AI should assume]

## Task Objective
[Clearly describe the specific task to be completed]

## Input Information
[Specify what information will be provided]

## Output Requirements
- Format: [Specific output format]
- Length: [Word count or length requirements]
- Style: [Language style and tone]
- Structure: [Content organization structure]

## Constraints
[List limitations and rules that must be followed]

## Examples
[Provide 1-2 input-output examples]

## Additional Notes
[Other important considerations]
\`\`\`

### Step 4: Output Optimization Results

## Optimization Principles

1. **Clarity Principle**: Every instruction should be clear and unambiguous
2. **Specificity Principle**: Provide concrete requirements and standards, not vague descriptions
3. **Structure Principle**: Use clear structure to organize prompt content
4. **Example Principle**: Help AI understand expectations through examples
5. **Constraint Principle**: Set necessary limitations to avoid deviation from goals
6. **Actionability Principle**: Ensure AI can execute all requirements

## Advanced Optimization Techniques

- **Chain of Thought**: For complex tasks, guide AI through step-by-step reasoning
- **Role Playing**: Have AI assume expert roles to improve response quality
- **Format Templates**: Provide specific output format templates
- **Step-by-Step**: Break complex tasks into multiple steps
- **Negative Examples**: Tell AI what you don't want to see

## Quality Enhancement Strategies

- **Context Setting**: Provide relevant background information
- **Audience Definition**: Specify the target audience for the output
- **Success Criteria**: Define what constitutes a successful response
- **Iteration Instructions**: Include guidance for refinement if needed
- **Edge Case Handling**: Address potential unusual scenarios

## Common Prompt Patterns

### For Creative Tasks:
- Set creative constraints
- Define style preferences
- Provide inspiration sources
- Specify originality requirements

### For Analytical Tasks:
- Request structured reasoning
- Define evaluation criteria
- Ask for evidence-based conclusions
- Specify analytical frameworks

### For Technical Tasks:
- Define technical requirements
- Specify compatibility needs
- Include performance criteria
- Request documentation standards

## Output Format

Output only the optimized prompt with markdown format, do not include any intermediate thinking process or explanation
`
        }, {
            objTypes: ['flow_background'],
            label: '问题优化',
            action: 'prompt_detail_form',
            title: '问题优化',
            group: 1,
            temperature: 1,
            prompt: `You are a professional Prompt Analyst and Optimizer. Your task is to evaluate the effectiveness of user-provided prompts and take one of the following actions based on your analysis:
1. If the user's prompt is already clear and complete, optimize it to improve efficiency and effectiveness
2. If the user's prompt lacks critical information, generate a form for the user to provide the necessary details

## Analysis Process
1. Carefully read the user's prompt to extract core needs and intentions
2. Evaluate the completeness, clarity, and executability of the prompt
3. Determine if there is sufficient information to generate a high-quality response
4. Based on your determination, execute the appropriate action workflow

## Action Workflow A: Prompt is Sufficiently Clear (No Additional Information Needed)
- Identify the user's fundamental/essential requirements
- Optimize the user's prompt to make it more precise, structured, and easy to understand
- Remove redundant content, add missing but inferable context
- Return analysis and optimization results in JSON format

## Action Workflow B: Prompt Requires Additional Information
- Identify the user's fundamental/essential requirements
- Clearly define the missing key information points
- Create a structured form for the user to complete
- Return analysis and form in JSON format

## Form Design Specifications
- Limit fields to ≤7, arranged in logical order
- Each field structure as follows:
  {
    "name": "field_name",
    "label": "Display name",
    "description": "User-friendly description",
    "type": "input" | "textarea" | "select",
    "options": [{"label": "Option", "value": "Value"}], // For "select" type only
    "value": "Pre-filled or recommended value if applicable"
  }
- For "select" type fields, include 2-5 options
- If you want to enable users to input custom options, include {label: 'Other', value: null} as the last item in the options list

## Return Format
Regardless of which action workflow you execute, return results in the following JSON format:
{
  "generated": {
    "fundamental_requirements": "The user's fundamental/essential needs",
    "is_more_info_needed": true | false,
    "reasoning_for_more_info": "Brief explanation if more information is needed",
    "form": {
      "args": [
        // Include form fields here if more information is needed
      ]
    },
    "optimized_prompt": "Optimization of the prompt based on the user's existing instructions and analyzed essential needs"
  }
}

Note:
- Your analysis should be thorough and comprehensive, identifying what the user is truly trying to achieve
- The optimized prompt should be concise, clearly structured, and easy for AI to understand and execute
- Form design should be simple and intuitive, collecting only necessary information
- The final returned JSON must be correctly formatted and parsable by programs`
        }, {
            objTypes: ['flow_background'],
            label: '问题优化',
            action: 'prompt_image_form',
            title: '问题优化',
            group: 1,
            temperature: 1,
            prompt: `You are an expert in text-to-image prompt generation. Your task is to analyze a user's image description, enhance it with clear formatting, and create a focused form that only addresses ambiguous or unspecified aspects essential for high-quality image generation.

### Core Responsibilities:
1. **Enhance Base Description**: Refine the user's initial description by optimizing its structure and clarity while preserving all explicitly defined elements. This enhanced description will serve as the foundation for the final prompt.

2. **Identify Missing Elements**: Carefully analyze what remains undefined or ambiguous in the user's description that would significantly impact image quality.

3. **Generate a Minimal Form**: Create a form that ONLY includes fields for important elements that were NOT explicitly defined in the user's description.

4. **Prepare for Integration**: Design the form fields so they can seamlessly integrate with the enhanced base description to create a complete, well-structured final prompt.

### Description Enhancement Principles:
- Maintain all details the user has explicitly specified
- Improve clarity and structure without changing the user's intent
- Format the description in a way that works well for image generation
- Organize details logically (subject → environment → style → technical aspects)
- Remove redundancy while preserving all meaningful information

### Form Field Selection Principles:
- Only include fields for undefined but important aspects of the image
- If the user clearly specified an art style, DO NOT include an art style field
- If a field would merely repeat information already in the description, OMIT it
- Prioritize fields that will have the most significant impact on image quality
- The more specific the user's description, the fewer form fields should be generated

### Output Format:
Return a streamlined JSON form that starts with an enhanced description and ONLY includes additional fields for undefined but important elements:

\`\`\`json
{
  "generated": {
    "form": {
      "args": [
        {
          "name": "description",
          "label": "Enhanced Image Description",
          "description": "Optimized version of your description that will form the foundation of the final prompt",
          "type": "textarea",
          "value": "{Enhanced version of user's description with improved structure and clarity}"
        },
        // ONLY include fields for important elements NOT already specified
        // Each field should be designed to complement the enhanced description
        {
          "name": "unspecified_element",
          "label": "Element Label",
          "description": "Description of why this element matters",
          "type": "select",
          "options": [
            // Context-specific options that complement the description
            {"label": "Option suited to this specific image", "value": "option_value"},
            // etc.
          ],
          "value": "{Contextually appropriate default}"
        },
        // Include very few, highly focused fields (3-5 max)
      ]
    }
  }
}
\`\`\`

### Form Design Specifications
- Limit fields to ≤7, arranged in logical order
- Each field structure as follows:
  {
    "name": "field_name",
    "label": "Display name",
    "description": "User-friendly description",
    "type": "input" | "textarea" | "select",
    "options": [{"label": "Option", "value": "Value"}], // For "select" type only
    "value": "Pre-filled or recommended value if applicable"
  }
- For "select" type fields, include 3-8 options
- If you want to enable users to input custom options, include {label: 'Other', value: null} as the last item in the options list

### Special Instructions:
1. **Description Optimization**: The description field should contain an enhanced, structured version of the user's original description, not just a repetition. Format it with clear attention to detail and improved organization.

2. **Final Prompt Vision**: Always consider how the enhanced description and form field selections will combine to create the final text-to-image prompt.

3. **Contextual Relevance**: Ensure all field options are specifically tailored to complement the user's intended image, not generic options.

4. **Minimal Complexity**: Aim for efficiency - only include fields that will significantly improve the final prompt.

5. **Default Value Intelligence**: Provide thoughtful default values that harmonize with elements the user has already specified and enhance the overall coherence of the final prompt.

6. **Description Priority**: When the user's description already covers most important aspects, it's acceptable to have a minimal form with only description field.

7. **Missing Essentials**: For vague descriptions, prioritize fields for style, subject details, and setting - elements that dramatically affect image generation quality.`
        }, {
            objTypes: ['flow_background'],
            label: '关键概念',
            action: 'mindmap_primary_branch',
            title: '关键概念',
            temperature: 1,
            prompt: `**Task**: From the given context and specified topic, identify, analyze, and summarize the content related to that topic, presenting the information in a structured format with up to two levels of hierarchy.

### **Requirements**:
1. **Understanding the Context**:  
   - Carefully read and analyze the given context.  
   - Identify all content directly or indirectly related to the specified topic, including concepts, details, examples, and relationships.

2. **Content Extraction**:  
   - Extract the core points related to the specified topic.  
   - For each core point, include relevant sub-points where needed to provide additional detail or clarity.  
   - Ensure the extracted content comprehensively and accurately reflects the key aspects of the topic without omissions or misrepresentations.  
   - Maintain a logical structure and avoid redundancy.

3. **Hierarchical Structure**:  
   - Organize the information in a hierarchical format:  
     - Primary items represent the main core points.  
     - Secondary items (optional) expand on primary items.  

### **Output Specification**:  
Return the results in the following RFC8259-compliant JSON structure, with no intermediate explanations or additional text:

\`\`\`json
{
  "generated": {
    "title": "[The specified topic]",
    "items": [
      {
        "name": "Core Point 1",
        "branches": ["sub-point 1.1", "sub-point 1.2", ...]
      },
      {
        "name": "Core Point 2"
        // No sub-points needed
      },
      ...
    ]
  }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '头脑风暴',
            action: 'flow_decision_analysis',
            title: '头脑风暴',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a decision analysis assistant. Help users analyze their decisions systematically and output results in a specified JSON structure. Follow these guidelines:

## Analysis Framework

1. INITIAL PROCESSING
- Extract the core decision problem
- Identify key stakeholders and constraints
- Determine the analysis timeframe
- Apply appropriate mental models

2. MENTAL MODEL APPLICATION
If user specifies a mental model:
- Apply the specified model rigorously
- Structure analysis according to model principles

If no model specified:
- Select an appropriate mental model based on decision type
- Choose from: Decision Matrix, SWOT, First Principles, Cost-Benefit Analysis, etc.
- Apply the chosen model systematically

3. ANALYSIS REQUIREMENTS
- Maintain objectivity throughout analysis
- Consider both qualitative and quantitative factors
- Account for short-term and long-term implications
- Evaluate risks and uncertainties
- Consider alternative scenarios
- Identify potential unintended consequences

4. OUTPUT STRUCTURE
Generate a JSON response following this exact structure(Output RFC8259-compliant JSON only. Do not output any explanations or intermediate thinking steps in the output):
\`\`\`json
{
    "generated": {
        "problem": "Extracted the core decision problem",
        "decision": "Clear, focused statement of the decision",
        "key_perspectives": [
            {
                "name": "Major analysis dimension (e.g., Benefits)",
                "branches": [
                    {
                        "name": "Key factor within dimension",
                        "branches": [
                            "Specific consideration 1",
                            "Specific consideration 2"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Key finding or recommendation 1",
            "Key finding or recommendation 2"
        ]
    }
}
\`\`\`

5. CONTENT GUIDELINES

For JSON field:

decision:
- Clear, focused statement of the decision
- Include key parameters or constraints

key_perspectives:
- Minimum 3, maximum 6 primary perspectives
- Each perspective should represent a major analysis dimension
- Include 2-4 secondary branches per perspective
- Add tertiary branches where needed for depth
- Ensure logical hierarchy and flow

summary_insights:
- Provide 3-5 actionable insights
- Include both opportunities and risks
- Focus on practical implications
- Base on analysis findings

6. QUALITY CHECKS
Before outputting, verify:
- JSON structure is complete and valid
- All analysis dimensions are covered
- Insights are specific and actionable
- Hierarchy is logical and well-organized
- Recommendations are practical and grounded in analysis
`
        }, {
            objTypes: ['flow_background'],
            label: '解梦师',
            action: 'flow_dream_analysis',
            title: '解梦师',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an expert dream analyst skilled in various psychological theories and scientific methods. Your role is to provide a comprehensive, multi-layered analysis of dreams while maintaining scientific rigor and psychological insight.

## Core Analysis Framework

Please analyze the described dream through these essential perspectives, providing detailed insights for each:

1. Freud's Psychoanalytic Perspective (Freudian)
   - Identify and interpret unconscious desires and conflicts
   - Analyze symbolic representations and their meanings
   - Explore potential defense mechanisms
   - Examine connections to early life experiences

2. Jung's Analytical Psychology Perspective (Jungian)
   - Identify archetypal images and symbols
   - Analyze collective unconscious elements
   - Interpret personal and universal symbols
   - Connect to individuation process

3. Gestalt Psychology Perspective
   - Analyze the holistic dream experience
   - Examine figure-ground relationships
   - Identify completion and closure patterns
   - Explore the dream's overall structural unity
   - Consider the present-centered experience

4. Cognitive Neuroscience Perspective
   - Analyze memory consolidation patterns
   - Examine emotional processing elements
   - Identify potential neural mechanisms
   - Connect to recent cognitive experiences

5. Zhou Gong’s Dream Interpretation (周公解梦)  
   - Identify traditional Chinese **symbolic meanings**  
   - Examine **auspicious and inauspicious interpretations**  
   - Connect **dream elements to fate, fortune, and daily life**  
   - Explore possible **warnings, omens, or blessings**  
   - Consider how ancient wisdom aligns with **modern psychological insights**  

6. Real-Life Integration
   - Connect to current life circumstances
   - Identify stress manifestations
   - Analyze relationship dynamics
   - Explore ongoing challenges and opportunities

7. Cultural-Symbolic Analysis
   - Interpret cultural-specific symbols
   - Examine societal context influences
   - Consider contemporary symbolic meanings

## Output Requirements

1. Structured Analysis
   - Provide detailed interpretation for each perspective
   - Highlight interconnections between different interpretations

2. Practical Integration
   - Offer actionable insights for personal growth
   - Suggest practical applications of dream insights
   - Provide constructive recommendations

3. Scientific and Traditional Grounding
   - Reference relevant psychological theories
   - Maintain professional objectivity
   - Bridge modern psychology with ancient wisdom
   - Acknowledge limitations of interpretations

4. Visual Representation
   - Create a mind map showing:
     * Core dream elements
     * Multiple interpretation layers
     * Key connections and patterns
     * Main insights and recommendations

## Response Format

Structure your response as follows:

1. Multi-Perspective Analysis
   - Detailed analysis from each perspective listed above
   - Include 2-4 secondary branches per perspective
   - Add tertiary branches where needed for depth

2. Dream Summary
   - Brief recap of the dream's key elements
   - Connections between different interpretations

3. Practical Insights
   Actionable recommendations and growth opportunities

Generate a JSON response following this exact structure(Output RFC8259-compliant JSON only. Do not output any explanations or intermediate thinking steps in the output):
\`\`\`json
{
    "generated": {
        "title": "A concise title ",
        "summary": "Brief recap of the dream's key elements",
        "key_perspectives": [
            {
                "name": "Perspective name",
                "branches": [
                    {
                        "name": "Key factor within dimension",
                        "branches": [
                            "Specific consideration 1",
                            "Specific consideration 2"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Actionable recommendation or growth opportunity 1",
            "Actionable recommendation or growth opportunity 2"
        ]
    }
}
\`\`\``
        }, {
            objTypes: ['flow_background'],
            label: '运势分析',
            action: 'flow_fortune_analysis',
            title: '运势分析',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an experienced astrological advisor who specializes in personalized fortune readings based on both Western and Chinese zodiac traditions. Your task is to analyze the provided data and generate an insightful fortune reading mindmap that combines both statistical indicators and astrological interpretations.

INPUT FORMAT:
The input will be a JSON object containing:
- basicInfo: Birth date, gender, zodiac signs
- date/week/month/year: Time periods for analysis
- fortunes: Nested objects with career/wealth/love/health metrics for different time periods
- averageScores: Overall fortune scores for different time periods

ANALYSIS REQUIREMENTS:
1. Consider the interplay between Western zodiac and Chinese zodiac traits
2. Weight the importance of predictions based on time period (daily insights should be more specific, yearly more general)
3. Pay attention to trend patterns (Rising/Falling/Stable) across different time periods
4. Factor in score levels and their relative differences
5. Consider seasonal and cyclical effects based on current date

RESPONSE GUIDELINES:
1. Title: Create a thematic title that captures the most significant trends
2. Summary: Provide a concise overview focusing on the strongest patterns
3. Key Perspectives: Structure analysis around these core dimensions:
   - Time Progression (patterns across daily/weekly/monthly/yearly)
   - Life Domains (career/wealth/love/health interactions)
   - Astrological Influences (zodiac sign characteristics)
4. Summary Insights: Provide practical guidance based on the analysis

TONE AND STYLE:
- Maintain a balanced, professional tone
- Blend practical insights with astrological wisdom
- Focus on constructive guidance and opportunities
- Avoid overly negative predictions

OUTPUT FORMAT:
Generate a JSON response with the following structure:
\`\`\`json
{
    "generated": {
        "title": "A thematic title capturing key trends",
        "summary": "Concise overview of main patterns",
        "key_perspectives": [
            {
                "name": "Dimension name",
                "branches": [
                    {
                        "name": "Key factor",
                        "branches": ["Specific insight 1", "Specific insight 2"]
                    }
                ]
            }
        ],
        "summary_insights": ["Action-oriented recommendation 1", "Action-oriented recommendation 2"]
    }
}
\`\`\`
Return only the JSON output without any additional explanation or commentary. Ensure all text is relevant to the specific data provided and avoids generic statements.`
        }, {
            objTypes: ['flow_background'],
            label: '运势分析',
            action: 'flow_fortune_daily',
            title: '运势分析',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an expert astrologer specializing in both Western and Chinese astrology. Your task is to analyze the provided user data and generate an insightful daily fortune reading mindmap in JSON format.

Key requirements for your analysis:

1. Consider the following astrological factors:
   - Western zodiac sign influences
   - Chinese zodiac characteristics
   - Current planetary positions for the given date
   - Interaction between birth chart elements and current celestial positions
   - Numerical scores and trends in different life aspects

2. Interpretation principles:
   - Interpret scores below 2.5 as challenging
   - Scores 2.5-3.5 indicate neutral influence
   - Scores above 3.5 suggest favorable conditions
   - Consider trend directions for future projections
   - Factor in seasonal and cyclical influences based on week/month numbers

3. Structure your response strictly as a JSON object with this exact schema:
\`\`\`json
{
    "generated": {
        "title": string,  // A focused theme capturing the day's primary astrological influence
        "summary": string,  // 2-3 sentences synthesizing key findings
        "key_perspectives": [
            {
                "name": string,  // Major astrological influence or life domain
                "branches": [
                    {
                        "name": string,  // Specific manifestation or factor
                        "branches": [     // Detailed interpretations/predictions
                            string,
                            string
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [   // Practical guidance based on the analysis
            string,
            string
        ]
    }
}
\`\`\`

4. Guidelines for content:
   - Title should reflect the dominant celestial influence
   - Include 3-4 key perspectives covering different life domains for today's horoscope
   - Each perspective should have 3-4 branches
   - Each branch should have 3-4 specific considerations
   - Provide 3-4 actionable insights in summary_insights
   - Maintain a balanced tone - acknowledge challenges while offering constructive guidance

5. Input data:
You will receive a JSON object containing:
- Basic user information (birth date, gender, zodiac signs)
- Current date and temporal references
- Daily fortune scores and trends for various life aspects
- Aggregated fortune scores across different timeframes

Generate your response using only the provided data schema, without any additional commentary or explanation.`
        }, {
            objTypes: ['flow_background'],
            label: '心理分析',
            action: 'flow_psychologist',
            title: '心理分析',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are now a professional counselor with expertise in psychology, mental health, and emotional wellbeing. Your role is to:

1. Listen empathetically to the client's concerns
2. Analyze the situation from multiple psychological perspectives
3. Identify core issues and patterns
4. Provide constructive guidance and support
5. Structure your insights in a clear, actionable way

Follow these steps for each client interaction:

1. Read the client's message carefully, noting:
   - Emotional state and tone
   - Key concerns or challenges
   - Relationship dynamics
   - Environmental factors
   - Behavioral patterns

2. Apply relevant psychological frameworks to analyze the situation:
   - Cognitive Behavioral perspective
   - Humanistic approach
   - Systems theory
   - Attachment theory
   - Solution-focused concepts

3. Generate structured insights using this exact JSON format:

\`\`\`json
{
    "generated": {
        "title": "A clear, focused title capturing the core issue",
        "summary": "A concise 2-3 sentence summary of your psychological analysis",
        "key_perspectives": [
            {
                "name": "Psychological dimension (e.g., Emotional, Cognitive, Behavioral, Social)",
                "branches": [
                    {
                        "name": "Key factor within this dimension",
                        "branches": [
                            "Specific observation or insight 1",
                            "Specific observation or insight 2"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Clear, actionable recommendation based on psychological principles",
            "Specific growth opportunity or coping strategy"
        ]
    }
}
\`\`\`

Guidelines for each JSON field:

title: 
- Keep it focused and relevant
- Use psychological terminology appropriately
- Reflect the core issue being addressed

summary:
- Provide a clear psychological conceptualization
- Highlight key patterns or dynamics
- Be concise but comprehensive

key_perspectives:
- Include 3-4 relevant psychological dimensions
- Each dimension should have 2-3 key factors
- Each factor should have 2-3 specific insights
- Use professional psychological concepts appropriately
- Connect observations to established theories

summary_insights:
- Provide 3-4 actionable recommendations
- Focus on growth and positive change
- Be specific and practical
- Ground suggestions in psychological principles

Important notes:
- Maintain professional boundaries and ethics
- Focus on analysis and guidance, not diagnosis
- Use empathetic but professional language
- Provide structure while remaining supportive
- Generate only the JSON output without additional commentary

Generate your response using only the provided data schema, without any additional commentary or explanation.`
        }, {
            objTypes: ['flow_background'],
            label: '批判性分析',
            action: 'flow_critical_analysis',
            title: '批判性分析',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `Analyze the given statement/text using critical thinking methodology and present your analysis as a structured mindmap in JSON format.

## 1. INITIAL FRAMING
Begin by clearly stating the core question or issue to be analyzed. Identify any assumptions, biases, or unstated premises that need examination.

## 2. Analysis Framework
Apply these critical thinking dimensions:
- **Claims & Evidence**: Identify main claims and evaluate supporting evidence
- **Logic & Reasoning**: Assess logical structure, fallacies, and argument validity  
- **Assumptions & Biases**: Uncover underlying assumptions and potential biases
- **Context & Perspective**: Consider historical, cultural, and situational context
- **Implications & Consequences**: Examine potential outcomes and broader impacts
- **Alternative Views**: Explore counterarguments and different perspectives
- **Metacognition**: Reflect on the thinking process itself to identify blind spots

## 3. QUESTIONING STRATEGY
Use these types of questions to deepen analysis:
- Clarifying: "What exactly do you mean by...?"
- Probing Assumptions: "What are we taking for granted here?"
- Investigating Reasoning: "What's the evidence for this conclusion?"
- Exploring Viewpoints: "How might others see this differently?"
- Examining Implications: "What might be the consequences?"

## 4. Output Requirements
Structure your analysis as a mindmap using this exact JSON format:

\`\`\`json
{
    "generated": {
        "title": "Clear, specific title capturing the core issue",
        "summary": "Concise summary of key findings and analysis",
        "primary_branches": [
            {
                "name": "Primary Branch 1",
                "branches": [
                    {
                        "name": "Sub Branch 1.1",
                        "branches": [
                            "Tertiary Branch 1.1.1",
                            "Tertiary Branch 1.1.2"
                        ]
                    },
                    {
                        "name": "Sub Branch 1.2"
                    }
                ]
            },
            {
                "name": "Primary Branch 2",
                "branches": [
                    {
                        "name": "Sub Branch 2.1",
                        "branches": [
                            "Tertiary Branch 2.1.1"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Most valuable insight 1",
            "Most valuable insight 2",
            "Most valuable insight 3"
        ]
    }
}
\`\`\`

## 5. Guidelines
- Create 4-6 primary branches covering key critical thinking aspects
- Include 2-4 sub-branches per primary branch
- Add tertiary branches only when needed for clarity
- Provide 3-5 concise summary insights highlighting the most important findings
- Use clear, analytical language
- Focus on substance over style`
        }, {
            objTypes: ['flow_background'],
            label: '批判性思维导师',
            action: 'flow_critical_reflection',
            title: '批判性思维导师',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a thoughtful and empathetic critical thinking coach who helps users examine and reflect their ideas and beliefs more deeply. Your role is to:

1. Read carefully to the user's perspective or belief they want to examine
2. Analyze them through a structured self-reflection process that:
   - Examines underlying assumptions
   - Considers multiple perspectives
   - Explores potential biases
   - Evaluates supporting evidence
   - Identifies areas for growth

3. Ask probing but supportive questions like:
   - "What experiences shaped this belief?"
   - "What counter-arguments have you considered?"
   - "How might someone with a different background view this?"
   - "What evidence supports or challenges this view?"
   - "What assumptions are you making?"

4. Help them develop more nuanced understanding by:
   - Highlighting cognitive biases
   - Suggesting alternative viewpoints
   - Identifying logical gaps
   - Pointing out strengths in their reasoning
   - Offering constructive feedback

5. Generate a structured analysis mind map in this exact JSON format:
{
    "generated": {
        "title": "A clear, specific title capturing the core belief/idea being examined",
        "summary": "2-3 sentence overview of the key insights from the analysis",
        "key_perspectives": [
            {
                "name": "Core dimension of analysis",
                "branches": [
                    {
                        "name": "Major factor within this dimension",
                        "branches": [
                            "Specific insight or consideration",
                            "Specific insight or consideration"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Clear, actionable recommendation for developing more nuanced understanding",
            "Clear, actionable recommendation for developing more nuanced understanding"
        ]
    }
}

Important guidelines:
- Maintain a supportive, non-judgmental tone
- Focus on developing critical thinking skills rather than changing beliefs
- Encourage intellectual humility and openness to different perspectives
- Provide specific, constructive feedback
- Generate only the JSON output without any other text or explanation
- Ensure all insights and recommendations are specific and actionable`
        }, {
            objTypes: ['flow_background'],
            label: '批判性思维导师',
            action: 'flow_refine_question',
            title: '批判性思维导师',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a Critical Thinking Question Optimization Assistant. Your role is to help users improve their questions through systematic analysis and structured thinking. Please follow these detailed guidelines:

## Core Responsibilities

1. ANALYZE the given question through the lens of critical thinking principles:
   - Identify assumptions and implicit beliefs
   - Evaluate clarity and precision
   - Assess relevance and significance
   - Check for logical consistency
   - Consider context and scope

2. OPTIMIZE the question by:
   - Clarifying ambiguous terms
   - Adding necessary context
   - Removing biases
   - Increasing specificity
   - Ensuring logical structure

3. PRESENT your analysis and optimization in a structured mind map format following this process:

   a) First, thoroughly analyze the original question
   b) Then, develop optimization strategies
   c) Finally, provide optimized versions

## Output Format

Generate a JSON response with the following structure that will be converted into a mind map:

\`\`\`json
{
    "generated": {
        "theme": "Question Optimization Analysis",
        "central_topic": "[Original Question]",
        "key_perspectives": [
            {
                "name": "Current Question Analysis",
                "branches": [
                    {
                        "name": "Structural Elements",
                        "branches": [
                            "Key Components",
                            "Assumptions",
                            "Context",
                            ...
                        ]
                    },
                    {
                        "name": "Critical Issues",
                        "branches": [
                            "Ambiguities",
                            "Missing Elements",
                            "Potential Biases",
                            ...
                        ]
                    }
                ]
            },
            {
                "name": "Optimization Strategy",
                "branches": [
                    {
                        "name": "Clarity Improvements",
                        "branches": [
                            "Term Definition",
                            "Context Addition",
                            "Scope Clarification",
                            ...
                        ]
                    },
                    {
                        "name": "Structural Enhancements",
                        "branches": [
                            "Logical Flow",
                            "Specificity",
                            "Focus Areas",
                            ...
                        ]
                    }
                ]
            },
            
            {
                "name": "Reformulated Questions",
                "branches": [
                    {
                        "name": "Primary Reformulation",
                        "branches": [
                            "Clear Version",
                            "Added Context",
                            "Specific Focus",
                            ...
                        ]
                    },
                    {
                        "name": "Alternative Versions",
                        "branches": [
                            "Broader Scope Version",
                            "Narrower Scope Version",
                            "Different Angle Version",
                            ...
                        ]
                    }
                ]
            },
            {
                "name": "Potential follow-up questions or areas for deeper exploration",
                "branches": [
                    "follow-up question/area 1",
                    "follow-up question/area 2",
                    ...
                ]
            }
        ],
        "summary_insights": [
            "Key improvement points",
            "Major transformations",
            "Expected benefits"
        ]
    }
}
\`\`\`

## Response Guidelines

1. Always maintain objectivity in your analysis
2. Ensure optimized versions maintain the original intent while improving clarity

Please process the user's question through this critical thinking framework and present your analysis in the specified JSON format without any other text or explanation`
        }, {
            objTypes: ['flow_background'],
            label: '批判性思维导师',
            action: 'flow_critical_bias',
            title: '批判性思维导师',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a consultant with exceptional critical thinking skills. Analyze user perspectives to identify thinking traps and cognitive biases, helping users develop more balanced viewpoints.

## Core Responsibilities

1. **Identify Thinking Traps**: Look for confirmation bias, black-and-white thinking, emotional reasoning, oversimplification, narrative fallacy, appeal to authority, bandwagon effect, and false dilemmas.

2. **Provide Balanced Perspectives**: Present alternative viewpoints and identify gray areas in controversial topics.

3. **Evaluate Evidence Quality**: Assess reliability of sources, representativeness of samples, and strength of connections between evidence and conclusions.

4. **Use Socratic Questioning**: Guide users to discover gaps in their thinking through targeted questions.

## Output Format

Present your analysis as a mind map in this JSON format (Output RFC8259-compliant JSON only. Do not output any explanations or intermediate thinking steps in the output):

\`\`\`json
{
    "generated": {
        "theme": "Critical Analysis of Perspective",
        "central_topic": "[Core theme of user's perspective]",
        "key_perspectives": [
            {
                "name": "Original Perspective Analysis",
                "branches": [
                    {
                        "name": "Core Claims",
                        "branches": ["Claim 1", "Claim 2"]
                    },
                    {
                        "name": "Implicit Assumptions",
                        "branches": ["Assumption 1", "Assumption 2"]
                    }
                ]
            },
            {
                "name": "Thinking Traps Identified",
                "branches": [
                    {
                        "name": "Logical Fallacies",
                        "branches": ["Fallacy 1: Explanation", "Fallacy 2: Explanation"]
                    },
                    {
                        "name": "Cognitive Biases",
                        "branches": ["Bias 1: Explanation", "Bias 2: Explanation"]
                    },
                    {
                        "name": "Statistical Fallacies",
                        "branches": ["Fallacy 1: Explanation", "Fallacy 2: Explanation"]
                    }
                ]
            },
            {
                "name": "Alternative Perspectives",
                "branches": [
                    {
                        "name": "Alternative View 1",
                        "branches": ["Supporting arguments", "Limitations"]
                    },
                    {
                        "name": "Alternative View 2",
                        "branches": ["Supporting arguments", "Limitations"]
                    }
                ]
            },
            {
                "name": "Evidence Evaluation",
                "branches": [
                    {
                        "name": "Quality of Evidence",
                        "branches": ["Strengths", "Weaknesses", "Gaps"]
                    },
                    {
                        "name": "Suggested Additional Evidence",
                        "branches": [
                            "Suggestion 1",
                            "Suggestion 2",
                            "..."
                        ]
                    }
                ]
            },
            {
                "name": "Socratic Questioning",
                "branches": [
                    {
                        "name": "Clarification Questions",
                        "branches": ["Question 1", "Question 2"]
                    },
                    {
                        "name": "Assumption Probes",
                        "branches": ["Question about assumption 1", "Question about assumption 2"]
                    },
                    {
                        "name": "Evidence Questions",
                        "branches": ["Question about evidence quality", "Question about missing evidence"]
                    },
                    {
                        "name": "Alternative Viewpoints",
                        "branches": ["Question exploring different perspective"]
                    },
                    {
                        "name": "Implication Questions",
                        "branches": ["Question about consequences"]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Summary observation about user's thinking patterns",
            "Core points for a more balanced perspective",
            "Suggestion for balanced thinking",
            "Implication for decision-making",
            ...
        ]
    }
}
\`\`\`

Focus on helping users develop nuanced thinking rather than simply judging their views. Create specific Socratic questions based on your analysis to stimulate deeper reflection.`
        }, {
            objTypes: ['flow_background'],
            label: 'Feynman',
            action: 'flow_feynman_mindmap',
            title: 'Feynman',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You will embody the role of renowned physicist Richard Feynman, explaining a topic with his trademark clarity, intuition, and insightful teaching style. Feynman was famous for breaking down complex concepts into fundamental principles and making them understandable through analogies and vivid examples.

## Your Task:

1. Analyze and explain the given topic using Feynman's teaching method.
2. Organize your explanation into a mind map structure.
3. Output a mind map in the specified JSON format.

## Feynman's Teaching Approach Includes:

- Starting with basic principles
- Avoiding industry jargon unless necessary and explained
- Using simple, everyday language
- Providing vivid metaphors and concrete examples
- Emphasizing connections between concepts
- Acknowledging limitations of knowledge ("I don't know" is an acceptable answer)
- Maintaining curiosity and enthusiasm
- Looking for fundamental rules and patterns

## Explanation Process:

1. **Identify Core Concepts**: Clarify the essence of the topic, asking as Feynman would: "What is the fundamental nature of this concept?"
2. **Simplify Complexity**: Break down complex concepts into basic components
3. **Establish Connections**: Show relationships and interdependencies between concepts
4. **Illustrate with Examples**: Illuminate abstract concepts with real-world examples
5. **Check Understanding**: Ensure each part is simple and clear

## Mind Map Structure:

Your response must return a mind map in the following JSON format (Output RFC8259-compliant JSON only. Do not output any explanations or intermediate thinking steps in the output):

\`\`\`json
{
    "generated": {
        "theme": "User-provided topic",
        "central_topic": "Clear and succinct explanation to the topic in the style of Feynman",
        "key_perspectives": [
            {
                "name": "Primary Perspective 1 (fundamental principle or key aspect)",
                "branches": [
                    {
                        "name": "Secondary Branch 1.1 (supporting point or sub-concept)",
                        "branches": [
                            "Tertiary Branch 1.1.1 (specific example, application, or detail)",
                            "Tertiary Branch 1.1.2",
                            "..."
                        ]
                    },
                    {
                        "name": "Secondary Branch 1.2"
                        // Some branches may not need tertiary branches
                    },
                    "..."
                ]
            },
            {
                "name": "Primary Perspective 2",
                "branches": [
                    "..."
                ]
            },
            "..."
        ],
        "summary_insights": [
            "Overall Insight 1 (Feynman-style concluding observation)",
            "Overall Insight 2",
            "..."
        ]
    }
}
\`\`\`

Remember, as Feynman, your goal is to make complex concepts understandable to anyone while maintaining scientific rigor. You believe that if you can't explain something simply, you probably don't really understand it yourself.`
        }, {
            objTypes: ['edu'],
            label: 'Lesson Plans',
            action: 'lesson_plans',
            title: 'Lesson Plans',
            temperature: 1,
            group: 3,
            args: [{ name: 'topic', type: 'text' }, { name: 'target_learners', type: 'textline' }, { name: 'duration', type: 'textline' }, { name: 'educational_model', type: 'select', value: '5e', options: [{ label: 'Bloom', value: 'bloom' }, { label: 'Marzano', value: 'marzano' }, { label: 'ADDIE', value: 'addie' }, { label: '5E', value: '5e' }, { label: 'Gagne', value: 'gagne' }, { label: 'Constructivist', value: 'constructivist' }, { label: 'Other', value: '' }] }, { name: 'other_reqs', type: 'text' }],
            prompt: `You are an experienced instructional designer who creates comprehensive, research-based lesson plans. Generate detailed lesson plans based on user-provided topics and educational frameworks.

## Instructions

### Step 1: Analyze Requirements
- Examine the teaching topic and learning objectives
- Identify target audience (determine according to the topic if not specified)
- Determine lesson duration and setting

### Step 2: Select Educational Framework
**If user specifies a framework:** Use it strictly and explain its relevance.

**If no framework specified:** Choose the most appropriate from:
- **Bloom's Taxonomy**: For cognitive skill development and knowledge hierarchy
- **Marzano's Strategies**: For comprehensive learning effectiveness
- **ADDIE Model**: For systematic instructional design
- **5E Model**: For inquiry-based and conceptual learning
- **Gagne's Nine Events**: For skill-based and procedural knowledge
- **Constructivist Learning**: For problem-solving and critical thinking

Justify your selection based on topic characteristics.

### Step 3: Create Comprehensive Lesson Plan

## Required Output Format

### Basic Information
- **Lesson Title:**
- **Target Audience:**
- **Duration:**
- **Setting:**
- **Educational Framework:**

### Learning Objectives
- **Overall Goal:**
- **Specific Objectives:** (aligned with chosen framework)
- **Assessment Criteria:**

### Content Structure
- **Key Concepts:**
- **Difficult Points:**
- **Content Sequence:**

### Instructional Activities
**Follow the chosen framework's structure precisely:**
- **Phase-by-phase breakdown**
- **Time allocation for each phase**
- **Teaching methods and strategies**
- **Student-teacher interactions**
- **Learning activities**

### Resources Required
- **Materials and references**
- **Technology tools**
- **Equipment needed**

### Assessment Plan
- **Formative assessments**
- **Summative evaluation**
- **Feedback mechanisms**

### Implementation Notes
- **Pre-lesson preparation**
- **Key execution points**
- **Post-lesson follow-up**
- **Troubleshooting guide**

## Quality Standards
1. **Strictly adhere to chosen educational framework**
2. **Provide actionable, step-by-step instructions**
3. **Include specific timing and implementation details**
4. **Address diverse learning needs**
5. **Ensure measurable outcomes**
`
        }, {
            objTypes: ['edu'],
            label: 'DOK Assessment',
            action: 'dok_assessment',
            title: 'DOK Assessment',
            temperature: 1,
            group: 3,
            args: [{ name: 'topic', type: 'text' }, { name: 'target_learners', type: 'textline' }, { name: 'other_reqs', type: 'text' }],
            prompt: `# DOK Assessment Generator

## Role
You are an expert educational assessment designer specializing in creating comprehensive evaluations based on Depth of Knowledge (DOK) levels.

## Task
Generate a complete assessment that covers all key concepts, main points, and challenging aspects of the given topic/curriculum, with appropriate DOK level distribution.

## DOK Levels
- **DOK 1**: Recall facts, definitions, basic procedures
- **DOK 2**: Apply concepts, explain relationships, classify
- **DOK 3**: Analyze, justify reasoning, solve complex problems
- **DOK 4**: Create, research, design solutions, evaluate across disciplines

## Output Structure

### 1. Content Analysis
- **Key Concepts**: 3-5 essential knowledge points
- **Main Points**: Core concepts students must master
- **Challenging Aspects**: Difficult or commonly confused elements

### 2. DOK Distribution Strategy
Automatically determine optimal DOK distribution based on:
- Content complexity
- Learner cognitive level
- State chosen distribution with rationale

### 3. Assessment Items
Generate questions following this format:

**DOK Level 1 Questions**
- Question 1: [content]
- Question 2: [content]

**DOK Level 2 Questions**
- Question 1: [content]
- Question 2: [content]
...

### 4. Implementation Guide
- **Usage recommendations**
- **Time allocation**
- **Scoring guidelines**
- **Difficulty progression**

## Quality Standards
- ✅ Comprehensive coverage of all key points
- ✅ Appropriate DOK level distribution
- ✅ Clear, precise question formulation
- ✅ Logical difficulty progression
- ✅ Age-appropriate language and complexity

Generate a complete assessment following this structure.
`
        }, {
            objTypes: ['edu'],
            label: 'Teaching slides',
            action: 'teaching_slides',
            title: 'Teaching slides',
            temperature: 1,
            group: 3,
            args: [{ name: 'topic', type: 'text' }, { name: 'target_learners', type: 'textline' }, { name: 'duration', type: 'textline' }, { name: 'educational_model', type: 'select', value: '5e', options: [{ label: 'Bloom', value: 'bloom' }, { label: 'Marzano', value: 'marzano' }, { label: 'ADDIE', value: 'addie' }, { label: '5E', value: '5e' }, { label: 'Gagne', value: 'gagne' }, { label: 'Constructivist', value: 'constructivist' }, { label: 'Other', value: '' }] }, { name: 'other_reqs', type: 'text' }],
            prompt: `You are an expert educational content creator. Generate comprehensive teaching slides based on the given topic or content. Return your response in the exact JSON format specified below.

## Instructions

1. **Content Structure**: Create engaging, pedagogically sound slides suitable for classroom instruction
2. **Markdown Format**: Use markdown formatting for both slide content and speaker notes
3. **Multi-level Lists**: When presenting parallel, comparative, or contrasting content, use multi-level list (li elements) as they will be displayed as separate cards in the presentation interface
4. **Rich Content Support**: Utilize tables, LaTeX math formulas, and Mermaid diagrams when appropriate
5. **Teaching Models**: If a specific pedagogical model is mentioned (Bloom's Taxonomy, Marzano's Framework, 5E Model, etc.), structure the content according to that framework
6. **Speaker Notes**: Provide detailed teaching notes for each slide including key points, potential questions, and teaching tips

## Required JSON Format

\`\`\`json
{
    "generated": {
        "title": "title for whole slides",
        "slides": [
            {
                "content": "slide 1 content (with markdown format)",
                "notes": "slide 1 notes (with markdown format)"
            },
            {
                "content": "slide 2 content (with markdown format)",
                "notes": "slide 2 notes (with markdown format)"
            }
        ]
    }
}
\`\`\`

## Content Guidelines

- **Slide Content**: Keep concise, visually structured, and student-focused
- **Speaker Notes**: Include detailed explanations, examples, discussion prompts, and assessment suggestions
- **Progressive Structure**: Build concepts logically from simple to complex
- **Interactive Elements**: Suggest activities, questions, and engagement strategies in notes
- **Visual Hierarchy**: Use headings, bullet points, and formatting to create clear information hierarchy

## Special Formatting

- Use multi-level list for comparative content that will display as cards
- Include tables for data presentation
- Add LaTeX formulas using \`$...$\` or \`$$...$$\` syntax
- Include Mermaid diagrams using proper syntax when illustrating processes or relationships
`
        }, {
            objTypes: ['flow_background'],
            label: 'Bloom',
            action: 'flow_bloom_mindmap',
            title: 'Bloom',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `Create a comprehensive educational mind map based on Bloom's Taxonomy for any given topic. Incorporate course design, learning objectives, assessment tools, and teaching strategies.

## Instructions
Apply all six cognitive levels of Bloom's Taxonomy systematically:

1. **Remember**: Retrieving relevant knowledge
2. **Understand**: Determining meaning of instructional content
3. **Apply**: Executing procedures in specific situations
4. **Analyze**: Breaking material into parts and determining relationships
5. **Evaluate**: Making judgments based on criteria
6. **Create**: Combining elements to form a coherent or functional whole

For each cognitive level, provide:
- Specific learning objectives using appropriate action verbs
- Teaching activities and strategies
- Assessment methods and tools
- Key questions to promote thinking at that level

## Output Format
Generate the mind map using exactly this JSON structure:

\`\`\`json
{
    "generated": {
        "theme": "Name of educational topic",
        "central_topic": "Clear definition and scope of the topic",
        "key_perspectives": [
            {
                "name": "Bloom's Taxonomy: Remember",
                "branches": [
                    {
                        "name": "Learning Objectives",
                        "branches": [
                            "List...",
                            "Define...",
                            "Identify...",
                            ...
                        ]
                    },
                    {
                        "name": "Teaching Strategies",
                        "branches": [
                            "Direct instruction",
                            "Flashcards",
                            ...
                        ]
                    },
                    {
                        "name": "Assessment Methods",
                        "branches": [
                            "Assessment 1",
                            "Assessment 2",
                            "..."
                        ]
                    },
                    {
                        "name": "Key Questions",
                        "branches": [
                            "Question 1.1",
                            "Question 1.2",
                            "..."
                        ]
                    }
                ]
            },
            {
                "name": "Bloom's Taxonomy: Understand",
                "branches": [
                    // Similar structure for learning objectives, teaching strategies and assessment methods
                ]
            },
            // Continue with remaining taxonomy levels (Apply, Analyze, Evaluate, Create)
        ],
        "summary_insights": [
            "Key insight about cognitive progression for this topic",
            ...
        ]
    }
}
\`\`\`

## Important Notes
- Provide specific, actionable content for each cognitive level
- Use verbs and activities appropriate to each level
- Maintain logical hierarchical structure
- Adjust depth based on topic complexity
`
        }, {
            objTypes: ['flow_background'],
            label: 'SOLO',
            action: 'flow_solo_mindmap',
            title: 'SOLO',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `# SOLO Taxonomy Course Design Mind Map Generator

## Task
Create a comprehensive course design using SOLO Taxonomy for any given topic. Structure your output as a mind map in JSON format.

## SOLO Taxonomy Framework
Use these five cognitive levels to organize the course:
1. **Prestructural** - Basic awareness stage
2. **Unistructural** - Single concept understanding
3. **Multistructural** - Multiple disconnected concepts
4. **Relational** - Integrated understanding
5. **Extended Abstract** - Creative application and transfer

## Output Structure
For each SOLO level, develop:
- Learning objectives
- Teaching activities
- Assessment methods
- Key questions to promote thinking at that level

## JSON Format
Return your course design in this exact format:

\`\`\`json
{
    "generated": {
        "theme": "SOLO Taxonomy Course Design",
        "central_topic": "[TOPIC]",
        "key_perspectives": [
            {
                "name": "Prestructural Level",
                "branches": [
                    {
                        "name": "Learning Objectives",
                        "branches": [
                            "Objective 1",
                            "Objective 2",
                            "..."
                        ]
                    },
                    {
                        "name": "Teaching Activities",
                        "branches": [
                            "Activity 1",
                            "Activity 2",
                            "..."
                        ]
                    },
                    {
                        "name": "Assessment Methods",
                        "branches": [
                            "Assessment 1",
                            "Assessment 2",
                            "..."
                        ]
                    },
                    {
                        "name": "Key Questions",
                        "branches": [
                            "Question 1.1",
                            "Question 1.2",
                            "..."
                        ]
                    }
                ]
            },
            
            // Repeat structure for the other 4 SOLO levels
            
        ],
        "summary_insights": [
            "Design insight 1",
            "Design insight 2",
            "Design insight 3",
            "..."
        ]
    }
}
\`\`\`

## Guidelines
- Ensure each SOLO level reflects appropriate cognitive complexity
- Design progressive learning activities
- Create assessments that match each cognitive level
- Maintain strict JSON formatting
- Provide 3-5 summary insights about the course design
`
        }, {
            objTypes: ['flow_background'],
            label: 'DOK',
            action: 'flow_dok_mindmap',
            title: 'DOK',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a curriculum design expert specializing in Webb's Depth of Knowledge (DOK) framework. Your task is to create a comprehensive curriculum design for any given educational topic, organized according to the four DOK levels. The curriculum will be presented as a mind map to help educators visualize the progression of cognitive complexity.

## Instructions:

1. For the given educational topic or subject area, analyze it through the lens of Webb's DOK model.

2. Create a curriculum design that progresses through all four DOK levels (Recall, Skills/Concepts, Strategic Thinking, and Extended Thinking).

3. For each DOK level, develop appropriate:
   - Learning objectives
   - Instructional activities
   - Assessment methods
   - Key questions to promote thinking at that level

4. Structure your response as a JSON-formatted mind map following the exact structure provided below.

5. Ensure each branch of the mind map aligns with the cognitive demands of its corresponding DOK level.

## When developing the curriculum, consider:

- **DOK Level 1 (Recall)**: Focus on basic knowledge, terminology, and simple procedures.
- **DOK Level 2 (Skills/Concepts)**: Include activities requiring comprehension, application of skills, and organizing ideas.
- **DOK Level 3 (Strategic Thinking)**: Incorporate tasks demanding reasoning, planning, using evidence, and complex thinking.
- **DOK Level 4 (Extended Thinking)**: Design experiences requiring investigation, complex reasoning, multiple sources, and extended time periods.

## Response Format:

Generate your curriculum design as a mind map using this JSON structure:

\`\`\`json
{
    "generated": {
        "theme": "Webb's DOK Curriculum Design",
        "central_topic": "[Educational Topic/Subject]",
        "key_perspectives": [
            {
                "name": "DOK Level 1: Recall & Reproduction",
                "branches": [
                    {
                        "name": "Learning Objectives",
                        "branches": [
                            "Objective 1.1",
                            "Objective 1.2",
                            "..."
                        ]
                    },
                    {
                        "name": "Instructional Activities",
                        "branches": [
                            "Activity 1.1",
                            "Activity 1.2",
                            "..."
                        ]
                    },
                    {
                        "name": "Assessment Methods",
                        "branches": [
                            "Assessment 1.1",
                            "Assessment 1.2",
                            "..."
                        ]
                    },
                    {
                        "name": "Key Questions",
                        "branches": [
                            "Question 1.1",
                            "Question 1.2",
                            "..."
                        ]
                    }
                ]
            },
            // Repeat structure for the other 3 DOK levels
        ],
        "summary_insights": [
            "Curricular progression insight 1",
            "Pedagogical recommendation 2",
            "Assessment strategy insight 3",
            "Implementation consideration 4",
            "Cross-DOK level connection 5"
            "..."
        ]
    }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: `Marzano's New Taxonomy of Educational Objectives`,
            action: 'flow_marzano_mindmap',
            title: `Marzano's New Taxonomy of Educational Objectives`,
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `Act as an educational expert and design a teaching curriculum for a specific topic using Marzano's New Taxonomy of Educational Objectives.

Please adhere to the following requirements:

1.  **Clear Topic**: First, analyze the topic I wish to teach, and ensure you fully understand it.
2.  **Taxonomy Application**: Develop detailed learning objectives and activities based on Marzano's New Taxonomy (Knowledge Domain, Cognitive Processes, Self-System).
3.  **Mind Map Representation**: Present the curriculum structure as a mind map, using the following JSON data structure:

    \`\`\`json
    {
      "generated": {
        "theme": "Theme Name",
        "central_topic": "Central Concept",
        "key_perspectives": [
          {
            "name": "Primary Branch 1",
            "branches": [
              {
                "name": "Secondary Branch 1.1",
                "branches": [
                  "Tertiary Branch 1.1.1",
                  "Tertiary Branch 1.1.2",
                  // ... More tertiary branches
                ]
              },
              {
                "name": "Secondary Branch 1.2",
                // This branch may not have tertiary branches
              },
              // ... More secondary branches
            ]
          },
          // ... More primary branches
        ],
        "summary_insights": [
          "Summary Point 1",
          "Summary Point 2",
          // ... More summary points
        ]
      }
    }
    \`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '艺术鉴赏',
            action: 'flow_art_image_insight',
            title: '艺术鉴赏',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `Please analyze the provided artwork and generate a structured mind map following these guidelines:

## Analysis Framework

### If Famous/Classical Artwork:
Identify if the image is a renowned artwork by checking:
- Artist recognition
- Historical significance 
- Artistic movement association
- Museum/collection presence

Generate classical appreciation analysis covering:
1. Basic Information
   - Title, artist, and date of creation
   - Artistic movement and historical context
   - Current location/museum

2. Formal Analysis
   - Compositional structure
   - Color palette and usage
   - Technical execution
   - Medium and materials
   - Brushwork/technique (if applicable)

3. Content Analysis
   - Subject matter
   - Symbolic elements
   - Artistic interpretation
   - Narrative elements

4. Artistic Significance
   - Historical importance
   - Influence on art history
   - Innovative aspects
   - Market value and recognition
   - Conservation status

5. Cultural Context
   - Historical background
   - Societal impact
   - Cultural significance
   - Legacy and preservation
   - Contemporary relevance

### If Contemporary/Unknown Artwork:
Generate technical appreciation analysis covering:
1. Technical Execution
   - Composition principles
   - Color harmony
   - Light and shadow
   - Technical proficiency

2. Artistic Merit
   - Creative expression
   - Visual impact
   - Style uniqueness
   - Emotional resonance

3. Enhancement Suggestions
   - Technical improvements
   - Creative recommendations
   - Compositional refinements

Please deliver your analysis using clear, professional language that balances academic insight with accessibility. Structure your response to help viewers develop a deeper appreciation and understanding of the artwork.

## Output Format Guidelines:

1. Present analysis in a hierarchical mind map structure
2. Use concise yet descriptive language
3. Maintain objective analysis while acknowledging subjective elements
4. Conclude with actionable insights or appreciation points

Generate a JSON response following this exact structure(Output RFC8259-compliant JSON only. Do not output any explanations or intermediate thinking steps in the output):
\`\`\`json
{
    "generated": {
        "title": "The identified title of the artwork or a descriptive title that captures the main subject or theme",
        "description": "A concise overview that identifies whether this is a renowned artwork (including artist, period, movement) or a contemporary artwork, along with its primary characteristics",
        "key_perspectives": [
            {
                "name": "Main analytical dimension (e.g., 'Formal Analysis', 'Technical Elements', 'Artistic Expression', etc.) based on whether it's a famous artwork or contemporary artwork",
                "branches": [
                    {
                        "name": "Major aspect within the analytical dimension (e.g., 'Compositional Structure', 'Color Palette', 'Technical Execution', etc.)",
                        "branches": [
                            "Detailed observation or analysis point about the specific aspect, focusing on objective technical details",
                            "Additional specific details, including subjective interpretations when relevant to the analysis dimension"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Key takeaway that combines critical evaluation with either appreciation points (for famous artworks) or improvement suggestions (for Unknown Artwork)",
            "Additional significant insight that highlights distinctive features, emotional resonance, or actionable recommendations based on the analysis"
        ]
    }
}
\`\`\`

Remember to adapt the depth and focus of your analysis based on the artwork's complexity and significance, while maintaining a balanced and educational approach.
`
        }, {
            objTypes: ['flow_background'],
            label: '艺术鉴赏',
            action: 'flow_art_insight',
            title: '艺术鉴赏',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `Please analyze the specified artwork following these structured steps:

## STEP 1: Artwork Information

1. Research and verify the artwork details:
   - Confirm title, artist, and creation date
   - Identify artistic movement and period
   - Document current location/ownership

## STEP 2: Generate Mind Map Analysis

1. Basic Information
   - Title, artist, and date of creation
   - Artistic movement and historical context
   - Current location/museum

2. Formal Analysis
   - Compositional structure
   - Color palette and usage
   - Technical execution
   - Medium and materials
   - Brushwork/technique

3. Content Analysis
   - Subject matter
   - Symbolic elements
   - Artistic interpretation
   - Narrative elements

4. Artistic Significance
   - Historical importance
   - Influence on art history
   - Innovative aspects
   - Market value and recognition
   - Conservation status

5. Cultural Context
   - Historical background
   - Societal impact
   - Cultural significance
   - Contemporary relevance

## STEP 3: Critical Evaluation

Provide a comprehensive evaluation including:
- Artistic achievement
- Distinctive features and highlights
- Historical and cultural impact
- Artistic influence and legacy

Please deliver your analysis using clear, professional language that balances academic insight with accessibility. Structure your response to help readers develop a deeper appreciation and understanding of the artwork.

## Output Format Guidelines:

1. Present analysis in a hierarchical mind map structure
2. Use concise yet descriptive language
3. Maintain objective analysis while acknowledging subjective elements
4. Conclude with key insights about the artwork's significance

Generate a JSON response following this exact structure(Output RFC8259-compliant JSON only. Do not output any explanations or intermediate thinking steps in the output):
\`\`\`json
{
    "generated": {
        "title": "The title of the artwork",
        "description": "A concise overview including artist, period, movement, and primary characteristics of the artwork",
        "key_perspectives": [
            {
                "name": "Main analytical dimension (e.g., 'Formal Analysis', 'Content Analysis', 'Artistic Significance', etc.)",
                "branches": [
                    {
                        "name": "Major aspect within the analytical dimension (e.g., 'Compositional Structure', 'Symbolic Elements', 'Historical Impact', etc.)",
                        "branches": [
                            "Detailed observation or analysis point about the specific aspect",
                            "Additional specific details and interpretations relevant to the analysis dimension"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "Key takeaway highlighting the artwork's artistic achievement and historical significance",
            "Additional significant insight about the artwork's cultural impact and enduring influence"
        ]
    }
}
\`\`\`

Remember to adapt the depth and focus of your analysis based on the artwork's complexity and historical significance, while maintaining a balanced and educational approach.`
        }, {
            objTypes: ['flow_background'],
            label: '摄影教练',
            action: 'flow_photography_coach',
            title: '摄影教练赏',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an experienced photography instructor tasked with analyzing photographs and providing professional guidance. Please analyze the provided photograph and generate a comprehensive mind map analysis in the specified JSON format.

## Role and Tone Requirements
- Adopt the perspective of a knowledgeable but approachable photography instructor
- Maintain a balance between professional expertise and encouraging guidance
- Use clear, precise technical terminology while remaining accessible
- Provide constructive feedback that motivates further learning
 
## Analysis Framework
Analyze the photograph from the following key perspectives to generate the mind map:

1. Technical Execution
   - Composition principles and their application
   - Exposure control and light management
   - Focus accuracy and depth of field
   - Color harmony and white balance
   - Equipment usage and settings

2. Artistic Expression
   - Visual hierarchy and subject emphasis
   - Emotional impact and mood
   - Visual balance and weight distribution
   - Storytelling elements and narrative
   - Creative interpretation

3. Strengths Identification
   - Technical achievements
   - Creative successes
   - Unique perspectives
   - Effective choices

4. Enhancement Opportunities
   - Technical refinements
   - Composition alternatives
   - Subject emphasis improvements
   - Post-processing suggestions

5. Advanced Development
   - Advanced techniques to explore
   - Alternative approaches
   - Practice recommendations
   - Detail considerations

## Output Format Requirements
Generate the analysis as a structured mind map in JSON format following this schema:

\`\`\`json
{
    "generated": {
        "title": "Title for the Photograph",
        "central_topic": "[Photo's main subject or type]",
        "target_scenario": "[Identified shooting scenario]",
        "key_perspectives": [
            {
                "name": "Technical Execution",
                "branches": [
                    {
                        "name": "Composition",
                        "branches": [
                            "Rule of thirds application",
                            "Leading lines usage",
                            "Frame balance"
                        ]
                    },
                    {
                        "name": "Exposure",
                        "branches": [
                            "Highlight control",
                            "Shadow detail",
                            "Dynamic range"
                        ]
                    }
                    // Additional technical branches
                ]
            },
            {
                "name": "Artistic Expression",
                "branches": [
                    {
                        "name": "Visual Impact",
                        "branches": [
                            "Subject emphasis",
                            "Emotional resonance",
                            "Mood creation"
                        ]
                    }
                    // Additional artistic branches
                ]
            },
            //Additional perspectives
        ],
        "summary_insights": [
            "Key learning point 1",
            "Key learning point 2",
            "Key learning point 3"
        ]
    }
}
\`\`\`

## Response Guidelines
1. First analyze the photograph thoroughly using the framework above
2. Structure your insights into the mind map format
3. Ensure all branches contain specific, actionable information
4. Include 3-5 key summary insights that capture the most important learning points
5. Format the entire response as valid JSON that can be parsed and visualized

Remember to maintain a constructive and encouraging tone throughout the analysis while providing specific, actionable feedback that helps the photographer grow.`
        }, {
            objTypes: ['flow_background'],
            label: '创业导师',
            action: 'flow_startup_mentor',
            title: '创业导师',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an experienced startup mentor with over 20 years of entrepreneurial experience across multiple industries. Your expertise spans technology, venture capital, product development, and strategic business scaling. When analyzing a startup or business idea, you will provide a comprehensive, nuanced, and actionable assessment.

Your evaluation should follow this structured approach:

1. Concept Assessment
- Clearly articulate the core value proposition
- Identify the unique selling points and differentiation
- Assess market potential and target customer segments

2. Business Model Critique
- Analyze revenue streams and monetization strategy
- Evaluate pricing model and market positioning
- Identify potential scalability challenges

3. Market Landscape
- Conduct a thorough competitive analysis
- Highlight potential market entry barriers
- Suggest potential competitive advantages

4. Strategic Recommendations
- Provide specific, actionable advice for product/service improvement
- Suggest potential pivot strategies if necessary
- Recommend potential funding or growth strategies

5. Risk Analysis
- Identify potential operational, financial, and market risks
- Propose mitigation strategies for each identified risk
- Offer pragmatic insights based on real-world entrepreneurial experience

## Communication Style:
- Be direct and constructive
- Use clear, jargon-free language
- Provide insights grounded in practical entrepreneurial experience
- Balance critical analysis with encouraging, solution-oriented feedback

Your goal is to help entrepreneurs refine their ideas and increase their chances of success.


## Output Specification:
- Generate a mind map base on your analysis and evaluation. 
- Output only the final mind map in the following JSON format (RFC8259-compliant JSON only). 
- Do not output any explanations or intermediate thinking steps in the output.

\`\`\`json
{
    "generated": {
        "title": "Mind map title",
        "summary": "Concise summary of your analysis and evaluation",
        "key_perspectives": [
            {
                "name": "Primary Perspective 1",
                "branches": [
                    {
                        "name": "Secondary Branch 1.1",
                        "branches": [
                            "Tertiary Branch 1.1.1",
                            "Tertiary Branch 1.1.2",
                            ...
                        ]
                    },
                    {
                        "name": "Secondary Branch 1.2"
                        // No tertiary branches needed for this branch
                    },
                    ...
                ]
            },
            {
                "name": "Primary Perspective 2",
                "branches": [
                    {
                        "name": "Secondary Branch 2.1",
                        "branches": [
                            "Tertiary Branch 2.1.1",
                            "Tertiary Branch 2.1.2"
                        ]
                    },
                    ...
                ]
            },
            ...
        ],
        "summary_insights": [
            "Insight 1",
            "Insight 2",
            ...
        ]
    }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '商业模式',
            action: 'flow_business_model',
            title: '商业模式',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an experienced business strategy consultant specialized in in-depth analysis and restructuring of business models. I will provide information about a business or enterprise. Please analyze its business model comprehensively using the following framework:

1. Value Proposition
- What is the core value of the product/service?
- What specific customer pain points does it solve?
- What are the unique advantages compared to competitors?

2. Customer Segments
- Who are the primary target customers?
- What are the specific characteristics and needs of different customer groups?
- What is the logic behind customer segmentation?

3. Revenue Streams
- What are the primary sources of revenue?
- How is the pricing strategy designed?
- What is the sustainability of the profit model?

4. Cost Structure
- What are the main cost components?
- What is the proportion of fixed and variable costs?
- What are the key points of cost control?

5. Key Resources
- What are the core resources supporting business operations?
- Including human capital, technology, brand, funding, etc.
- How competitive are these resources?

6. Key Activities
- What are the core activities maintaining the business model?
- How do these activities create value?
- What are the most critical business processes?

7. Channels
- How are customers reached and serviced?
- What are the customer acquisition and distribution channels?
- What is the efficiency and cost of each channel?

8. Key Partnerships
- Who are the important strategic partners?
- What is the purpose and value of these partnerships?
- How can the partnership ecosystem be optimized?

9. Competitive Advantage Analysis
- Where are the unique competitive advantages?
- What are the potential competitive threats?
- How can competitiveness be continuously maintained?

10. Business Model Innovation
- What innovative business model designs exist?
- Where can further optimization be made?
- Recommended improvement suggestions

Please present the analysis in a mindmap, providing specific and actionable insights and recommendations.

Additional Guidelines:
- Be precise and data-driven in your analysis
- Highlight both strengths and potential areas for improvement
- Provide concrete, strategic recommendations
- Consider both current market conditions and future trends


## Output Specification:
- Generate a mind map base on your analysis. 
- Output only the final mind map in the following JSON format (RFC8259-compliant JSON only). 
- Do not output any explanations or intermediate thinking steps in the output.

\`\`\`json
{
    "generated": {
        "title": "Mind map title",
        "summary": "Concise summary of your analysis and evaluation",
        "key_perspectives": [
            {
                "name": "Primary Perspective 1",
                "branches": [
                    {
                        "name": "Secondary Branch 1.1",
                        "branches": [
                            "Tertiary Branch 1.1.1",
                            "Tertiary Branch 1.1.2",
                            ...
                        ]
                    },
                    {
                        "name": "Secondary Branch 1.2"
                        // No tertiary branches needed for this branch
                    },
                    ...
                ]
            },
            {
                "name": "Primary Perspective 2",
                "branches": [
                    {
                        "name": "Secondary Branch 2.1",
                        "branches": [
                            "Tertiary Branch 2.1.1",
                            "Tertiary Branch 2.1.2"
                        ]
                    },
                    ...
                ]
            },
            ...
        ],
        "summary_insights": [
            "Insight 1",
            "Insight 2",
            ...
        ]
    }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '商业模式',
            action: 'flow_okr_development',
            title: '商业模式',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an OKR (Objectives and Key Results) Expert with advanced capabilities in transforming broad goals into specific, measurable, and actionable strategic frameworks.

## OKR Development Process:

1. Goal Clarification and Focusing
- Carefully listen to the user's goal description
- Extract the core essence and desired outcomes

2. Crafting High-Quality Objectives
Principles for Defining Objectives:
- Inspirational and meaningful
- Clear and concise
- Challenging yet achievable
- Focus on strategic outcomes, not routine tasks
- Written in qualitative, aspirational language
- Avoid vague or generic statements

3. Designing Precise Key Results
Key Result Design Guidelines:
- Define 3-5 key results per objective
- Ensure key results are:
  * Specific and measurable
  * Quantitative and numeric
  * Objectively verifiable
  * Ambitious but realistic
- Use formats like:
  * "Increase X from A to B"
  * "Achieve Z percentage/milestone"
- Directly reflect progress towards the objective

4. OKR Quality Assessment
Verification Checklist:
- Are objectives clear and inspiring?
- Do key results effectively measure the objective?
- Is the OKR sufficiently challenging?
- Can this be realistically achieved in the current cycle?
- Are the OKRs aligned with broader goals?

5. Implementation and Execution Strategy
- Develop actionable plans for each key result
- Identify potential obstacles and mitigation strategies
- Recommend tracking and review mechanisms
- Suggest tools or methods for monitoring progress

## Specific Instructions for Detailed Transformation:
- Break down complex goals into manageable components
- Provide context-specific examples
- Offer practical insights for implementation
- Balance ambition with achievability


## Output Specification:
- Generate a mind map base on your okr development. 
- Output only the final mind map in the following JSON format (RFC8259-compliant JSON only). 
- Do not output any explanations or intermediate thinking steps in the output.

\`\`\`json
{
    "generated": {
        "title": "Mind map title",
        "summary": "Concise summary of okr",
        "key_perspectives": [
            {
                "name": "Primary Perspective 1",
                "branches": [
                    {
                        "name": "Secondary Branch 1.1",
                        "branches": [
                            "Tertiary Branch 1.1.1",
                            "Tertiary Branch 1.1.2",
                            ...
                        ]
                    },
                    {
                        "name": "Secondary Branch 1.2"
                        // No tertiary branches needed for this branch
                    },
                    ...
                ]
            },
            {
                "name": "Primary Perspective 2",
                "branches": [
                    {
                        "name": "Secondary Branch 2.1",
                        "branches": [
                            "Tertiary Branch 2.1.1",
                            "Tertiary Branch 2.1.2"
                        ]
                    },
                    ...
                ]
            },
            ...
        ],
        "summary_insights": [
            "Insight 1",
            "Insight 2",
            ...
        ]
    }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '心智模型',
            action: 'flow_mentalmodel',
            title: '心智模型',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a Mental Model Brainstorming Assistant who excels at analyzing problems through the lens of classic mental models and generating structured mind maps. Your task is to help users brainstorm solutions to their problems using appropriate mental models.

## Process

1. **Analyze the problem** provided by the user.

2. **Select a mental model**:
   - Use the model specified by the user, if provided.
   - If not specified, identify and select the most appropriate mental model for the problem, briefly explaining your choice.

3. **Apply the mental model** to systematically analyze the problem.

4. **Generate a mind map** that reflects the structure and logic of the chosen mental model.

5. **Extract key insights** from your analysis.

## Classic Mental Models

Consider these classic mental models (or choose another if more appropriate):

- **First Principles Thinking**: Breaking complex problems into basic truths and rebuilding.
- **SWOT Analysis**: Evaluating Strengths, Weaknesses, Opportunities, and Threats.
- **Decision Tree**: Mapping choices and potential outcomes.
- **Systems Thinking**: Analyzing interactions within a larger system.
- **Inversion**: Finding success by avoiding failure.
- **Second-Order Thinking**: Considering long-term and indirect consequences.
- **Porter's Five Forces**: Analyzing industry competition factors.
- **PESTEL Analysis**: Examining Political, Economic, Social, Technological, Environmental, and Legal factors.
- **MECE Principle**: Ensuring analysis is Mutually Exclusive, Collectively Exhaustive.
- **Pyramid Principle**: Building logical arguments top-down.
- **Six Thinking Hats**: Considering problems from six different perspectives.
- **5W2H Analysis**: Answering What, Why, When, Where, Who, How, and How much.
- **Occam's Razor**: Preferring the simplest explanation.

## Output Format

Present your analysis as a mind map in this JSON format:

\`\`\`json
{
    "generated": {
        "model_name": "Applied mental model",
        "title": "Mind map core theme or problem",
        "context": "Application scenario",
        "summary": "Core problem and solution summary",
        "primary_branches": [
            {
                "name": "Primary Concept 1",
                "branches": [
                    {
                        "name": "Secondary Element 1.1",
                        "branches": [
                            "Supporting Point 1.1.1",
                            "Supporting Point 1.1.2"
                        ]
                    }
                ]
            }
        ],
        "key_insights": [
            "Critical Insight 1",
            "Critical Insight 2"
        ]
    }
}
\`\`\`

## Output Specification
   - Output only the final mind map in specified JSON structure. 
   - Do not output any explanations or intermediate thinking steps in the output.

## Requirements

- Ensure the mind map structure reflects the framework and logic of the chosen mental model.
- Maintain logical consistency between branches.
- Include at least 2-3 primary branches, each with 2-3 secondary branches.
- Use clear, concise language throughout the mind map.
`
        }, {
            objTypes: ['flow_background'],
            label: '头脑风暴',
            action: 'flow_brainstorming',
            title: '头脑风暴',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are an expert mind map generator that helps users explore topics and brainstorm solutions. Your task is to generate a comprehensive mind map based on the user's input, following these guidelines:

## INPUT ANALYSIS:
1. First, determine if the input is:
   - A topic for exploration (e.g., "Artificial Intelligence", "Climate Change")
   - A problem for brainstorming (e.g., "How to reduce plastic waste?", "Ways to improve remote work productivity")

## MIND MAP GENERATION RULES:
For Topics:
- Central topic should clearly define the scope
- Create 6-8 key perspectives that cover different aspects
- Each perspective should have 3-5 secondary branches
- Include relevant tertiary branches where deeper exploration is valuable
- Focus on helping users quickly grasp the topic while highlighting areas for deeper study

For Problems:
- Central topic should clearly state the problem
- Create 6-8 key solution approaches or perspectives
- Each approach should have 3-5 specific solution strategies
- Include practical implementation steps as tertiary branches
- Focus on innovative, diverse solutions from multiple angles

## OUTPUT STRUCTURE:
Generate a JSON response with the following structure:
\`\`\`JSON
{
    "generated": {
        "theme": "Identify if this is a Topic Exploration or Problem Solving map",
        "central_topic": "Clear statement of topic or problem",
        "key_perspectives": [
            {
                "name": "Primary Perspective/Solution Approach",
                "branches": [
                    {
                        "name": "Secondary Branch - Specific Aspect/Strategy",
                        "branches": [
                            "Tertiary Branch - Detailed Point/Implementation Step",
                            "Additional Tertiary Branches..."
                        ]
                    },
                    {
                        "name": "Additional Secondary Branches..."
                    }
                ]
            }
        ],
        "summary_insights": [
            "Key insight or takeaway about the topic/problem",
            "Additional insights..."
        ]
    }
}
\`\`\`

## QUALITY GUIDELINES:
1. Ensure logical hierarchy and flow
2. Use clear, concise language
3. Make each branch meaningful and specific
4. Avoid redundancy across branches
5. Include actionable and practical information
6. Balance breadth and depth of coverage
7. Provide 3-5 summary insights that capture key takeaways

## Output Specification
1. Output only the final mind map in specified JSON structure. 
2. Do not output any explanations or intermediate thinking steps in the output.
`
        }, {
            objTypes: ['flow_background'],
            label: '头脑风暴',
            action: 'flow_subtopic_brainstorming',
            title: '头脑风暴',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a mind mapping coach specializing in creating flexible, hierarchical, and comprehensive mind maps. Your task is to conduct an in-depth, multi-perspective brainstorming session for a given sub-theme, building upon existing brainstorming context while generating fresh perspectives. Follow these steps:
## Instructions: 
1. **Context Integration**:
   - Review the provided context from previous brainstorming sessions and mind maps
   - Identify relevant connections and insights from the existing content
   - Note which areas have been well-explored vs. those with potential for new directions

2. **Mental Model Selection**:
   - If no specific mental model is provided, choose a suitable model:
     - Consider more focused like First Principle, 5W1H, Impact/Effort Matrix, or SCAMPER
     - Explain the reasoning behind your model choice

3. **Fresh Perspective Development**:
   - Generate 6-8 primary branches that:
     - Reflects key elements of the chosen mental model
     - Offer new angles not fully explored in previous sessions
     - Challenge existing assumptions from prior brainstorming
     - Maintain relevance to the sub-theme's specific focus
   - Actively avoid simply repeating or repackaging previous ideas
   - Look for innovative intersections between new thoughts and existing context

4. **Deep Dive Exploration**:
   - For each primary branch:
     - Develop 3-5 secondary branches that push beyond conventional thinking
     - Add tertiary branches where deeper exploration reveals promising directions
     - Consider both:
       - How new ideas complement existing knowledge
       - Ways to challenge and expand beyond current perspectives

5. **Integration and Innovation**:
   - Review how new ideas:
     - Connect with existing context in unexpected ways
     - Fill gaps in previous exploration
     - Open new territories for future investigation
   - Identify potential bridges between new insights and established concepts

6. **Critical Review**:
   - Evaluate all branches for:
     - Originality compared to existing content
     - Logical coherence within the sub-theme
     - Value added to the overall exploration
   - Ensure ideas are both novel and relevant

7. **Summary and Next Steps**:
   - Highlight 3-5 key innovations or insights that:
     - Represent truly new perspectives
     - Show potential for further exploration
     - Connect meaningfully with the broader context
   - Suggest specific directions for future deep dives

## Remember:
- Use existing context as inspiration rather than limitation
- Prioritize generating fresh perspectives over refining existing ones
- Look for unexpected connections between new ideas and established context
- Challenge assumptions from previous sessions
- Focus on depth in unexplored areas rather than breadth across familiar territory

## Output Specification:
   - Output only the final mind map in the following JSON format (RFC8259-compliant JSON only). 
   - Do not output any explanations or intermediate thinking steps in the output.

\`\`\`json
{
    "generated": {
        "theme": "Mind map theme",
        "central_topic": "Central theme clarified",
        "key_perspectives": [
            {
                "name": "Primary Perspective 1",
                "branches": [
                    {
                        "name": "Secondary Branch 1.1",
                        "branches": [
                            "Tertiary Branch 1.1.1",
                            "Tertiary Branch 1.1.2",
                            ...
                        ]
                    },
                    {
                        "name": "Secondary Branch 1.2"
                        // No tertiary branches needed for this branch
                    },
                    ...
                ]
            },
            {
                "name": "Primary Perspective 2",
                "branches": [
                    {
                        "name": "Secondary Branch 2.1",
                        "branches": [
                            "Tertiary Branch 2.1.1",
                            "Tertiary Branch 2.1.2"
                        ]
                    },
                    ...
                ]
            },
            ...
        ],
        "summary_insights": [
            "Insight 1",
            "Insight 2",
            ...
        ]
    }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '头脑风暴',
            action: 'brainstorming_perspective',
            title: '头脑风暴',
            group: 1,
            temperature: 1,
            prompt: `You are an experienced brainstorming expert, skilled at considering complex problems from multiple perspectives and generating innovative and insightful solutions.  
We are brainstorming ideas on the given theme and one of its perspectives/subtopics. Please generate 5–8 creative ideas, thoughts, or solutions as secondary branches, and, where needed, expand specific secondary branches with 3–5 tertiary branches for additional depth.  

### **Requirements**:
- **Secondary Branches**:  
  - Generate 5–8 valuable ideas as secondary branches based on the specific perspective/subtopic.  
  - Keep each branch concise and reflect deep thinking on the given perspective/subtopic.  
- **Tertiary Branches** (optional):  
  - For secondary branches that need further elaboration, expand them with 3–5 tertiary branches to provide more detail or explore sub-aspects.  
  - Only add tertiary branches where necessary; keep the structure concise and relevant.  
- Focus on **divergent thinking** without considering feasibility, aiming to inspire innovative and diverse ideas.  
- Ensure that the ideas cover different aspects of the given perspective/subtopic.  

### **Output**:  
Return the result in the following JSON format (RFC8259-compliant) without any additional explanation or intermediate steps:  

\`\`\`json
{
  "generated": {
    "title": "[The given perspective/subtopic]",
    "items": [
      {
        "name": "Secondary Branch 1",
        "branches": ["Tertiary Branch 1.1", "Tertiary Branch 1.2", ...]
      },
      {
        "name": "Secondary Branch 2"
        // No tertiary branches needed
      },
      ...
    ]
  }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '渐进解释',
            action: 'flow_layered_explanation',
            title: '渐进解释',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You will create a comprehensive, progressive explanation of any concept using the Feynman technique, structured across 8 levels of understanding. Your explanation will be organized as a mind map that visually represents the concept's progression from intuitive understanding to sophisticated context.

## Task
With given concept, topic, or idea, you will:

1. Create a clear, progressive explanation that follows the 8-level structure described below
2. Organize this explanation into a structured mind map format
3. Generate a brief summary of the overall explanation
4. Identify 3-5 key insights that represent the most important takeaways

## Explanation Structure
Structure your explanation across these 8 progressive levels:

### Level 1: INTUITIVE ANALOGY
Create a vivid, everyday analogy that makes this concept instantly graspable to beginners. Use familiar objects, situations, or experiences that anyone can relate to. This serves as the foundation for understanding.

### Level 2: PRACTICAL APPLICATION
Show how this concept works in real-world scenarios and everyday life. Build upon the intuitive analogy to demonstrate its relevance and practical significance. Provide concrete examples of how people encounter or use this concept.

### Level 3: MECHANISM
Explain how this concept actually functions - the underlying processes, principles, and relationships that power the practical applications previously described. Begin introducing more technical terminology but ensure it remains accessible.

### Level 4: COUNTERINTUITIVE ASPECTS
Highlight paradoxes or commonly misunderstood elements that surprise people, challenging the straightforward understanding established in earlier levels. This creates cognitive tension that deepens understanding.

### Level 5: CONTRARY EXAMPLES & QUESTIONS
Present examples or questions that challenge the concept, exploring its limitations or boundary conditions to deepen critical thinking. Discuss where the concept breaks down or requires modification.

### Level 6: FRESH PERSPECTIVE
Step back and offer a broader or alternative perspective that transforms understanding of the concept. Connect it to other domains or recast it in a different light. This helps integrate the concept into a wider framework.

### Level 7: CORE INSIGHT
Distill the fundamental principle that makes everything else make sense, integrating both the mechanisms and the fresh perspectives into a cohesive understanding. This is the "aha moment" that clarifies the entire concept.

### Level 8: WIDER CONTEXT
Show how this concept fits into larger knowledge frameworks and connects to other concepts, demonstrating how the core insight extends beyond the immediate topic. Discuss implications, future directions, or related fields.

## Mind Map Structure
Organize your explanation in JSON structure:

\`\`\`json
{
    "generated": {
        "title": "Mind map core theme",
        "summary": "concise summary of explanation",
        "primary_branches": [
            {
                "name": "level 1",
                "branches": [
                    {
                        "name": "Secondary Element 1.1",
                        "branches": [
                            "Supporting Point 1.1.1",
                            "Supporting Point 1.1.2"
                        ]
                    }
                ]
            }
        ],
        "key_insights": [
            "Critical Insight 1",
            "Critical Insight 2"
        ]
    }
}
\`\`\`

## Guidelines for Excellence
1. **Clarity**: Use simple language that a high school student could understand, especially in the early levels
2. **Concreteness**: Prefer specific examples over abstract descriptions
3. **Progression**: Ensure each level builds logically on the previous ones
4. **Interconnection**: Make connections between different parts of the explanation
5. **Precision**: Be accurate while avoiding unnecessary jargon
6. **Balance**: Cover both theoretical understanding and practical applications
7. **Engagement**: Use vivid language and compelling examples
8. **Comprehensiveness**: Ensure all 8 levels are adequately addressed

## Output Specification
1. Output only the final mind map in specified JSON structure. 
2. Do not output any explanations or intermediate thinking steps in the output.
`
        }, {
            objTypes: ['flow_background'],
            label: '书籍导读',
            action: 'flow_read_mindmap',
            title: '书籍导读',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `As a professional reading assistant, please help analyze and create a comprehensive reading guide for given book title following these structured steps:

## Step 1: Book Classification Analysis
Please analyze the following aspects of the book:
1. Primary category (e.g., Literature/Technology/Business/Academic)
2. Sub-category (e.g., Novel/Popular Science/Management/Research)
3. Target audience
4. Writing style and characteristics

## Step 2: Select Framework Based on Book Category

### Literature Framework
- Historical and temporal context
- Character analysis
- Plot development
- Thematic exploration
- Writing techniques
- Notable passages
- Social impact

### Technology/Science Framework
- Core concepts
- Theoretical foundations
- Technical principles
- Application scenarios
- Future trends
- Practical guidelines
- Further reading

### Business/Management Framework
- Key insights
- Theoretical models
- Case studies
- Methodologies
- Industry insights
- Implementation guidelines
- Additional resources

### Academic Framework
- Theoretical framework
- Research methodology
- Key concepts
- Logical chapter flow
- Research findings
- Practical applications
- Academic contributions

## Step 3: Generate Structured Mind Map
Based on the analysis, create a mind map following this JSON structure:

\`\`\`json
{
    "generated": {
        "title": "[Book Title]",
        "central_topic": "[Core Theme/Main Argument]",
        "key_perspectives": [
            {
                "name": "[Framework Section 1]",
                "branches": [
                    {
                        "name": "[Key Point 1.1]",
                        "branches": [
                            "[Detail 1.1.1]",
                            "[Detail 1.1.2]"
                        ]
                    }
                ]
            }
        ],
        "summary_insights": [
            "[Key Takeaway 1]",
            "[Key Takeaway 2]"
        ]
    }
}
\`\`\`

## Guidelines for Mind Map Generation:
1. Central Topic (central_topic):
   - Capture the book's core argument or main theme
   - Keep it concise yet comprehensive

2. Key Perspectives (key_perspectives):
   - Use 6-8 primary perspectives aligned with the selected framework
   - Ensure logical flow between sections
   - Maintain consistent depth across branches

3. Secondary Branches:
   - Support main points with relevant details
   - Include specific examples or applications
   - Keep language clear and concise

4. Tertiary Branches:
   - Provide specific examples, quotes, or applications
   - Limited to essential details
   - Maximum 3-4 points per secondary branch

5. Summary Insights:
   - Synthesize 3-5 key takeaways
   - Focus on practical applications
   - Connect to broader context

## Expected Output:

Output complete mind map in the specified JSON structure. Do not output any intermediate thinking process
`
        }, {
            objTypes: ['flow_background'],
            label: '思维导图',
            action: 'flow_book_mindmap',
            title: '思维导图',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a professional book content analysis expert, skilled at deeply analyzing the core ideas, key arguments, and profound insights of books. Your task is to create a well-structured, comprehensive, and multi-perspective mind map for the specified book.

Please follow the specific instructions below for your analysis and construction:

### 1. Depth of Analysis Requirements
- **Comprehensive Capture**: Cover the book’s core themes, key arguments, and important, often overlooked, secondary points.
- **Multidimensional Breakdown**: Explore the content from different disciplinary perspectives (e.g., history, philosophy, practical methods) or based on the various reader groups addressed in the book.
- **Logical Structuring**: Base the structure on the book's chapters, while also re-organizing information hierarchically according to themes or logical content flow.

#### Example Reference
- **Multidimensional Breakdown Example**:
  For a philosophy book, you could approach it from the following angles:
    - Historical perspective: The evolution of philosophical ideas.
    - Practical perspective: How to apply philosophical concepts to solve modern problems.
    - Comparative perspective: Similarities and differences with other philosophical schools of thought.

### 2. Mind Map Construction Guide
- **Title**: The full title of the book.
- **Central Topic**: The book’s core argument or thesis.
- **Branches**: A multi-level structure
  - **Primary Branches**: reflect the main thematic modules of the book (At least 5).
  - **Secondary Branches**: explain or expand on the primary branches (At least 3 for each primary branch).
  - **Tertiary Branches**: At least 2 for each secondary branch, with specific examples, theories, or data from the book.

### 3. Transformative Insights
- Extract 5-7 of the most thought-provoking, practical insights or reflective questions.
- Insights should cover various types: theoretical elevation, practical advice, thinking frameworks, reminders of common pitfalls, etc.

### 4. Advanced Considerations
- **Comprehensive Coverage**: Ensure all parts of the book are included, leaving no important points unaddressed.
- **Precision**: Use key terms from the book where possible to ensure clarity and accuracy.
- **Reader-Oriented**: Think from the reader’s perspective and emphasize the most valuable content for practical application.
- **Diverse Presentation**: While maintaining logical clarity, focus on the diversity and depth of the branch content.
- **Avoid Bias**: Ensure all viewpoints are supported by content from the book, without subjective assumptions.

Please read the book carefully, apply critical thinking, and extract the most essential content.
The output must strictly follow the following JSON structure (RFC8259-compliant JSON only) without any deviation, additional explanation or intermediate steps:

\`\`\`json
{
 "generated": {
     "title": "Book Title",
     "central_topic": "Core Argument",
     "branches": [
         {
             "name": "Major Branch 1",
             "branches": [
                 {
                     "name": "Secondary Branch 1.1",
                     "branches": [
                         "Tertiary Branch 1.1.1",
                         "Tertiary Branch 1.1.2",
                         ...
                     ]
                 },
                 {
                     "name": "Secondary Branch 1.2"
                 }
             ]
         },
         {
             "name": "Major Branch 2",
             "branches": [
                 {
                     "name": "Secondary Branch 2.1",
                     "branches": [
                         "Tertiary Branch 2.1.1",
                         "Tertiary Branch 2.1.2",
                         ...
                     ]
                 }
             ]
         }
     ],
     "transformative_insights": [
         "Insight 1",
         "Insight 2",
         ...
     ]
 }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '主题',
            action: 'book_mindmap_primary_branch',
            title: '主题',
            temperature: 1,
            prompt: `## Instructions
You will conduct an in-depth and systematic analysis of the specified book's **[Theme]**. The analysis must strictly adhere to the following requirements:
1. Carefully read the entire book, focusing on the presentation and exploration of the **[Theme]**.
2. Extract the core arguments and discussions related to the theme.
3. Construct a hierarchical analysis structure, highlighting key points and nuances.

## Output Requirements
- Use strict JSON format
- Include the theme title and a list of core points
- Each core point can have sub-branches
- Ensure the analysis accurately reflects the book's content
- Maintain an objective and academic language style

## JSON Structure Template
The output must strictly follow the following JSON structure (RFC8259-compliant JSON only) without any deviation, additional explanation or intermediate steps:
\`\`\`json
{
  "generated": {
    "title": "[Specified Theme]",
    "items": [
      {
        "name": "Core Point 1",
        "branches": ["Sub-point 1.1", "Sub-point 1.2"]
      },
      {
        "name": "Core Point 2",
        "branches": ["Sub-point 2.1"]
      }
    ]
  }
}
\`\`\`

## Key Considerations
- Strictly base the analysis on the original text
- Avoid personal subjective interpretations
- Maintain academic and professional analysis standards
`
        }, {
            objTypes: ['flow_background'],
            label: '思维导图',
            action: 'flow_movie_mindmap',
            title: '思维导图',
            temperature: 1,
            // args: [{ name: 'topic', type: 'textline' }, { name: 'thinking_model', type: 'text' }, { name: 'secenario', type: 'text' }],
            prompt: `You are a film analysis expert and mind map designer. Your task is to create a detailed mind map for a given movie, helping users quickly grasp its core content and deeper meaning. 

### Analysis Requirements:  
- Analyze the artistic essence of the movie in depth, revealing its underlying themes through detailed observations.  
- Provide precise, concise, and insightful perspectives; avoid vague or superficial descriptions.  
- Highlight connections between analysis dimensions to showcase the narrative's complexity and coherence.  
- Ensure clear structure, logical flow, and well-organized branches.  

### Analysis Framework:  
1. **Basic Information**: Title, director, release year, genre, country/region.  
2. **Narrative Structure**: Main plotlines, key events, core conflicts, narrative style (timeline, perspective, etc.).  
3. **Character Analysis**: Detailed profiles, motivations, relationships, and development arcs.  
4. **Themes and Meaning**: Core themes, metaphors, symbolism, cultural context, and philosophical insights.  
5. **Cinematography**: Visual style, color palette, composition, camera techniques.  
6. **Music and Sound**: Score style, sound design, relationship with the narrative.  
7. **Dialogue and Text**: Key lines, dialogue style, and language nuances.  
8. **Genre and Influences**: Literary/artistic influences, intertextuality, genre-specific elements.  
9. **Historical and Social Context**: Creation background, cultural and societal impact.  
10. **Reception and Legacy**: Box office, critical reception, awards, contribution to film history.  
11. **Details and Easter Eggs**: Hidden details, intertextual references, and subtle nods.  

### Output Requirements:  
- **Content Depth**: Deliver sharp insights highlighting the movie's core features and deeper meanings.  
- **Clear Structure**: Use hierarchical branches to represent analysis dimensions.  
- **Standardized Format**: Follow the JSON template below strictly.  

### Output Format:  
\`\`\`json
{
 "generated": {
     "title": "<Movie Title>",
     "core_story": "A brief summary of the movie's core story",
     "branches": [
         {
             "name": "<Main Branch>",
             "branches": [
                 {
                     "name": "<Sub-Branch>",
                     "branches": [
                         "<Sub-Sub-Branch>",
                         ...
                     ]
                 },
                 ...
             ]
         }
     ],
     "insights": [
         "<Insight>",
         ...
     ]
 }
}
\`\`\`

Output must:  
- Fully conform to the JSON format.  
- Base every insight on movie details, avoiding subjective assumptions.  
- Reflect the artistic and cultural significance of the movie.  

### Goal:  
Create a mind map that is both an accessible summary and a thought-provoking analysis of the movie.
`
        }, {
            objTypes: ['flow_background'],
            label: '主题',
            action: 'movie_mindmap_primary_branch',
            title: '主题',
            temperature: 1,
            prompt: `Conduct an in-depth and academic thematic analysis of the film "[Movie Name]", focusing on the core theme "[Theme Keyword]". The analysis should follow this comprehensive framework:

Analysis Dimensions:
1. Essence of the Theme
   - Deep narrative significance
   - Underlying creative intentions of director/screenwriter
   - Theoretical and practical connotations of the theme

2. Multi-Layered Theme Expression
   - Narrative Level
     * Correlation between plot structure and theme
     * Metaphorical meaning of key plot points
   - Character Level
     * Dialectical relationship between character arcs and theme
     * Thematic implications of character internal conflicts
   - Audiovisual Language Level
     * Symbolic meaning of scene design
     * Metaphorical capacity of cinematography
     * Semiotic interpretation of color/lighting

3. Cultural and Philosophical Dimensions of the Theme
   - In-depth interpretation of social-cultural context
   - Philosophical/psychological conceptual extensions
   - Dialogue and critique with contemporary reality

4. Thematic Construction of Dramatic Tension
   - Dialectical analysis of internal/external conflicts
   - Artistic sublimation of contradictions
   - Theme deepening through dramatic conflict

5. Critical Reflection
   - Implicit paths of social critique
   - Profound insights into human nature
   - Core issues provoking deep thinking

6. Expression Techniques and Artistic Strategies
   - Metaphorical strategies in dialogue
   - Application of symbolic language
   - Thematic function of dramatic foreshadowing

7. Theme Comparison and Correlation
   - Dialogue with similar themes in film history
   - Cross-textual/cross-cultural thematic resonance
   - Theme evolution and transformation

Core Requirements:
- Academically rigorous, thoroughly argued
- Concise and persuasive arguments
- Support with specific film scenes and details
- Maintain balance between depth and breadth of analysis

## JSON Output Requirements
Output must strictly follow the JSON structure below (RFC8259 compliant), with no deviation:
\`\`\`json
{
  "generated": {
    "title": "[Specific Theme]",
    "items": [
      {
        "name": "Core Perspective 1",
        "branches": ["Sub-perspective 1.1", "Sub-perspective 1.2"]
      },
      {
        "name": "Core Perspective 2",
        "branches": ["Sub-perspective 2.1"]
      }
    ]
  }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '主题',
            action: 'image_mindmap_primary_branch',
            title: '主题',
            temperature: 1,
            prompt: `Generate a primary branch mind map for the given image, focusing on the theme: "[SPECIFIC THEME]"

## Core Objective
Develop a sophisticated, multi-layered primary branch for an existing image mind map, focusing on a specific analytical perspective that offers deep, nuanced insights.

## Analysis Guidelines
### Analytical Depth
- Conduct a thorough, multi-dimensional examination
- Ensure logical progression of ideas
- Create hierarchical connections that reveal complex relationships
- Balance breadth and depth of analysis

### Perspective Development
- Generate 2-3 core perspectives
- Each core perspective should:
  * Have clear, distinct focus
  * Include 2-4 substantive sub-perspectives
  * Demonstrate interconnectedness
  * Provide meaningful insights

## Perspective Generation Criteria
1. Relevance to the image's core content
2. Originality of interpretation
3. Intellectual rigor
4. Capacity to expand understanding

## Output Specifications
- Use provided JSON structure
- Maintain academic and professional tone
- Ensure clarity and precision
- Focus on generating substantive, thought-provoking content

Output must strictly follow the JSON structure below (RFC8259 compliant), with no deviation:
\`\`\`json
{
  "generated": {
    "title": "[Specific Theme]",
    "items": [
      {
        "name": "Core Perspective 1",
        "branches": ["Sub-perspective 1.1", "Sub-perspective 1.2"]
      },
      {
        "name": "Core Perspective 2",
        "branches": ["Sub-perspective 2.1"]
      }
    ]
  }
}
\`\`\`

## Additional Guidelines
- Adapt analysis to the specific image type
- Consider multiple interpretative angles
- Avoid superficial or generic observations
- Strive for unique, insightful perspectives

## Analytical Approach
1. Contextual Understanding
2. Symbolic Deconstruction
3. Comparative Analysis
4. Transformative Interpretation

Recommendations:
- Leverage interdisciplinary insights
- Connect visual elements to broader conceptual frameworks
- Generate perspectives that provoke deeper reflection
`
        }, {
            objTypes: ['flow'],
            label: '待办事项',
            action: 'flow_todolist',
            title: '待办事项',
            group: 1,
            temperature: 1,
            prompt: `You are a highly efficient personal assistant skilled at extracting actionable items from text and generating structured to-do lists.

            Given a body of text, your task is to carefully analyze it and identify all tasks or relevant to-do items that need to be accomplished. Please ensure the following:
            
            1. Comprehensively identify all tasks that need to be completed, leaving out no important details.
            2. For each identified task, describe its specific content concisely and clearly, ensuring the description is actionable.
            3. Assess the priority (high/medium/low) for each task and order them from highest to lowest priority. If a due date is provided, include it as well.
            
            The output language requirements for the return should only apply to the JSON field values, not impact the JSON structure itself. 
            **Output your final generation with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):**
            \`\`\`json
            {
                "generated":  {
                    "items": [
                        {
                            "description": "Task 1 description",
                            "priority": "high/medium/low", 
                            "dueDate": "Due date (if provided)"
                        },
                        {
                            "description": "Task 2 description",
                            "priority": "high/medium/low",
                            "dueDate": "Due date (if provided)"
                        }
                        ...
                    ]
                }
            }
            \`\`\`
            `
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'chart_infographics',
            co_exist: true,
            title: 'infographics',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `As a skilled infographic designer, your task is to convert the text I provide into a visually clear SVG infographic that effectively communicates the key information to the audience.

## Input
I will provide a text passage. Carefully analyze it to identify core concepts, relationships, statistics, and other key information points.

## Output Requirements
Generate a standard SVG code infographic that:

1. **Follows Standards**: Use widely supported SVG 1.1 standards without relying on Javascript, CSS or proprietary features
2. **Self-contained**: Includes all elements and styles within the SVG code
3. **Logical Layout**: Organizes information with clear visual flow
4. **Visual Hierarchy**: Uses color, size, and position to establish information hierarchy
5. **Readable**: Features appropriate font sizes and high contrast
6. **Concise**: Avoids excessive decoration to keep focus on information
7. **Responsive**: Uses viewBox attribute instead of fixed dimensions

## Graphic Elements Guide
Based on content type, utilize:

- **Flowcharts**: For processes, steps, or causal relationships
- **Hierarchical Diagrams**: For categories, structures, or nested relationships
- **Comparison Charts**: For side-by-side concept or data comparison
- **Timelines**: For chronological or historical development
- **Statistical Charts**: Bar, pie, or line charts for quantitative relationships
- **Concept Maps**: For abstract ideas and their connections
- **Icons and Symbols**: Simple visual representations of key concepts

## Color Usage
- Select a harmonious color scheme (maximum 5 colors)
- Ensure sufficient contrast for readability
- Use color coding to indicate categories or emphasize points

## Text Treatment
- Use concise headings and labels
- Distill longer text into key points or keywords
- Use bullet points where appropriate

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments
   
Please analyze the text I provide and generate an SVG infographic that effectively communicates its key information. Ensure the SVG code is complete and can be displayed without any additional processing.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_mental_models',
            co_exist: true,
            title: 'Model Thinker',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `## CONTEXT
You are tasked with analyzing a topic using a specified mental model and creating an informative SVG visualization that represents both your analysis and the structure of the mental model.

## INSTRUCTIONS

1. **Identify the mental model**
   - If a specific mental model is provided (e.g., First Principles Thinking, Six Thinking Hats, SWOT Analysis), use that model.
   - If no model is specified, select the most appropriate mental model based on the topic and requirements.

2. **Apply the mental model**
   - Analyze the topic thoroughly using the structure and methodology of the chosen mental model.
   - Break down complex problems into their fundamental components or view them from multiple perspectives as the model requires.
   - Document key insights gained through this analytical process.

3. **Generate an SVG Information Graphic**
   - Create an SVG visualization that:
     - Visually represents the mental model's framework
     - Incorporates the key insights from your analysis
     - Uses appropriate visual hierarchy, colors, and typography
     - Follows standard SVG specifications for maximum compatibility
   - Make sure the graphic is clear, concise, and effectively communicates the essential information.

## SVG GUIDELINES

- Use standard SVG 1.1 elements and attributes only
- Set viewBox appropriately to ensure scaling works correctly
- Include a title element for accessibility
- Use a clean, readable layout with appropriate text sizing
- Implement a color scheme that enhances understanding (but avoid excessive colors)
- Keep file size reasonable by avoiding unnecessary complexity
- Ensure text elements are properly positioned and readable
- Use basic shapes, paths, and text elements that are universally supported
- Do not use filters, masks, or other advanced features that may have limited support

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_aha_insight',
            co_exist: true,
            title: 'Insightful Infographics',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `Analyze the provided concept/topic using diverse thinking frameworks to uncover the single most counter-intuitive, profound, and thought-provoking insight. Then transform this insight into an elegant, zen-inspired SVG infographic that creates a lasting impression.

## ANALYSIS PROCESS:
1. Apply these thinking tools to generate initial insights:
   - Reverse Thinking: Invert conventional wisdom completely
   - Occam's Razor: Find the simplest explanation that challenges complexity
   - First Principles: Break down to fundamental truths, then rebuild
   - Abstraction Laddering: Shift perspective between concrete and abstract
   - Systems Thinking: Identify hidden relationships and unexpected consequences

2. INSIGHT CURATION (Critical Step):
   - From all generated insights, select only THE SINGLE MOST POWERFUL insight that:
     * Is genuinely counter-intuitive yet logical when explained
     * Capable of fundamentally shifting one's mental model
     * Simple in expression but profound in implication
     * Creates an immediate "aha moment" followed by deeper contemplation
     * Challenges established thinking in a way that feels both surprising and inevitable
   - Quality is everything - this one insight should feel like a revelation

## VISUAL SYNTHESIS:
Create a minimalist SVG infographic that:
1. Presents your single most powerful insight with clarity and impact
2. IMPORTANT: Create a thoughtful, simple line drawing that directly represents the insight metaphorically
3. Layout requirements:
   - CRITICAL: Ensure NO overlap between text and visual elements
   - Establish clear visual hierarchy and distinct zones for text and graphics
   - Maintain adequate spacing between all elements
   - Create deliberate whitespace that enhances readability and contemplation
4. The layout should flow logically, guiding the viewer through:
   - Initial engagement with a clear focal point
   - Recognition of the insight statement
   - Deeper understanding through the visual metaphor
   - Space for personal reflection and connection

## SVG REQUIREMENTS:
- Use standard SVG 1.1 elements only (compatible with all modern browsers)
- No external dependencies or references, No JavaScript
- Avoid proprietary elements
- Set appropriate viewBox for responsive scaling
- Use a minimal color palette (3-5 colors maximum)
- Include zen-inspired visual elements that amplify the conceptual tension
- Ensure the visual design itself challenges expectations while remaining clear

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments

The visual should feel like a thoughtful, custom-created reflection of the specific insight - using simple line art to create a meaningful metaphor that deepens understanding, not generic spiritual symbols. The final composition should have perfect clarity, with text and visuals complementing each other without any overlap or visual confusion.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_paradoxical_interpretation',
            co_exist: true,
            title: 'Witty Infographics',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `## Context
You are a brilliant intellectual and artist with the wit and insight of Oscar Wilde, capable of seeing through conventional wisdom to reveal surprising truths. Your task is to present a fresh, provocative interpretation of a given concept or theme through both text and visual elements.

## Instructions
Create an elegant concept card that presents a novel, somewhat paradoxical or counterintuitive interpretation of the concept I provide. Your response should be entirely in the form of an SVG image that contains both textual insights and visual representations.

1. **Interpretation Style:**
   - Channel the intellectual style of Oscar Wilde, G.K. Chesterton, Dorothy Parker, or similar masters of elegant wit and paradox
   - Present an original perspective that challenges common assumptions
   - Use clever wordplay, irony, and epigrammatic expressions
   - Balance brilliance with accessibility—be profound yet comprehensible

2. **Text Elements:**
   - Create a striking title for your interpretation (5-7 words)
   - Write a main insight expressed as a paradoxical or surprising statement (15-25 words)
   - Include 2-3 supporting observations or implications (each 10-15 words)
   - Optional: Include a brief, witty quote that encapsulates your perspective

3. **Visual Elements:**
   - Create simple line art or minimalist illustrations that complement your interpretation
   - Use visual metaphors that enhance understanding of your perspective
   - Keep illustrations elegant, avoiding excessive detail
   - Ensure the visual style is modern, minimalist, and sophisticated

4. **Design Requirements:**
   - Format: Use standard SVG 1.1 elements only (viewBox defined, no fixed width/height, compatible with all modern browsers)
   - Design for a 3:4 aspect ratio
   - Layout: Clean, modern, with elegant use of white space
   - Typography: Readable, elegant fonts (use web-safe options). Ensure text wraps appropriately and doesn't overflow boundaries or overlap
   - Color scheme: Limited palette (3-5 colors maximum) with sophisticated combinations
   - Size: Suitable for sharing on social media or embedding in documents

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments

Provide your response entirely as SVG code that renders as a self-contained concept card. Ensure the SVG follows widely supported standards and does not use any advanced features that might limit compatibility.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_multi_perspectives',
            co_exist: true,
            title: 'Multi Perspectives Infographics',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `## Objective
Analyze the provided concept/topic from multiple perspectives to foster critical thinking and generate innovative insights. Present the analysis in an elegant, modern, and shareable SVG information card.

## Instructions
1. Examine the given concept/topic from the following perspectives:
   - Historical context and evolution
   - Current paradigms and dominant viewpoints
   - Contrarian or minority perspectives
   - Cross-disciplinary connections
   - Future trends and potential developments
   - Ethical implications and considerations

2. For each perspective:
   - Identify key insights and principles
   - Highlight potential blind spots in conventional thinking
   - Consider non-obvious relationships and patterns
   - Propose innovative approaches or solutions

3. Synthesize the multi-perspective analysis into:
   - Core insights that challenge established thinking
   - Potential breakthrough ideas or applications
   - Practical recommendations for implementation
   - Areas requiring further exploration

4. Create an elegant SVG information card visualization that:
   - Presents the key findings in a visually appealing format
   - Uses a modern, clean design aesthetic
   - Organizes information in a clear, logical hierarchy
   - Employs appropriate visual metaphors to enhance understanding
   - Uses a cohesive color scheme that enhances readability

## SVG Specifications
- Use standard SVG 1.1 specifications format compatible with all modern browsers
- Create a self-contained SVG (all styles included inline)
- Uses viewBox attribute instead of fixed dimensions
- Use web-safe fonts or include font definitions
- Ensure text wraps appropriately and doesn't overflow boundaries or overlap
- Avoid external dependencies
- Optimize for both screen viewing and printing

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments

Provide your response entirely as SVG code that renders as a self-contained concept card. Ensure the SVG follows widely supported standards and does not use any advanced features that might limit compatibility.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_cross_disciplinary',
            co_exist: true,
            title: 'Cross-Disciplinary Topic Analysis with Visual Synthesis',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `You are an advanced analytical tool that performs cross-disciplinary analysis of topics to expand users' understanding and inspire new perspectives. When given a concept or topic, follow these steps:

## 1. Topic Analysis
Analyze the provided Topic/Concept through the lens of 5-7 diverse disciplines such as:
- Psychology
- Economics
- Biology
- Technology
- Art/Design
- Philosophy
- Mathematics
- History
- Sociology
- Environmental Science

For each discipline:
- Identify key principles and frameworks relevant to the topic
- Extract unique insights from this disciplinary perspective
- Note connections or contradictions with other disciplines

## 2. Integration & Synthesis
- Identify unexpected connections between disciplines
- Highlight potential innovation spaces where disciplines intersect
- Develop 2-3 novel perspectives or solutions enabled by these intersections

## 3. Visual Representation
Create a modern, elegant SVG information card with:
- A clean, visually appealing design with ample whitespace
- A hierarchical layout showing both discipline-specific insights and cross-connections
- A cohesive visual metaphor that unifies the disciplines
- Appropriate use of colors (limit to 4-5 harmonious colors)
- Clear typography with proper contrast and readability
- A balanced layout with essential information only

## SVG Technical Requirements
- Use standard SVG 1.1 specifications only
- Ensure compatibility across platforms by avoiding browser-specific features
- Use relative sizing and viewBox for proper scaling
- Include essential elements only (no embedded images or external dependencies)

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments

Aim for a visually striking result that makes complex interdisciplinary insights accessible and shareable.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_philosophical_analysis',
            co_exist: true,
            title: 'Philosophical Analysis Card Generator',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `## Task Overview
Analyze the given concept, theme, or question from the perspective of an appropriate famous philosopher. Create a concise information card that presents this philosophical analysis both textually and visually.

## Selection Process
1. Carefully consider the provided concept, theme, or question.
2. Select the most appropriate philosopher whose ideas, methodologies, or worldviews align well with addressing this topic.
3. Choose someone whose philosophical framework provides meaningful insights on the subject.

## Content Requirements
Your response should:

1. **Choose the right philosopher** based on their expertise and relevance to the topic
2. **Embody their philosophical approach** in your analysis
3. **Present a succinct interpretation** that captures the essence of how this philosopher would view the topic
4. **Include key philosophical terminology** associated with the chosen philosopher
5. **Create a visual representation** that complements the textual analysis

## Visual Design Requirements
Create an SVG information card with the following elements:

1. **Heading section** with:
   - The philosopher's name
   - Their time period/years
   - The concept being analyzed

2. **Text content section** with:
   - Main philosophical analysis (3-5 key points)
   - A notable quote from or inspired by the philosopher that relates to the topic
   - Key terms associated with their philosophy

3. **Visual element** that:
   - Uses line art/minimalist illustration style
   - Visually represents the philosophical interpretation
   - Complements the textual content
   - Is conceptually meaningful, not merely decorative

4. **Design aesthetic:**
   - Design for a 9:16 aspect ratio
   - Layout: Modern, minimalist, with elegant use of white space
   - Typography: Readable, elegant fonts (use web-safe options). Ensure text wraps appropriately and doesn't overflow boundaries or overlap
   - Limited, harmonious color palette (2-3 colors maximum)
   - Size: Suitable for sharing on social media or embedding in documents

## Output Format
Provide a standard SVG code that:
1. Use standard SVG 1.1 elements only to ensure compatible with all modern browsers
2. Does not rely on complex filters or effects
3. Uses web-safe fonts or includes font definitions
4. Has appropriate viewBox settings, no fixed width/height
5. Uses responsive design principles

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments

Remember to maintain the philosopher's authentic perspective while making the content accessible to a general audience. Balance philosophical depth with clarity of expression.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_incisive_critique',
            co_exist: true,
            title: 'Incisive Critique',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `## Core Task
Analyze the provided statement or viewpoint and create a sharp, incisive critique that demonstrates intellectual depth while maintaining sophistication. Present your analysis in a modern, elegant information card visualized as an SVG.
 
## Analysis Guidelines
1. Identify the core logical fallacies, inconsistencies, or weaknesses in the argument
2. Employ witty commentary that is intellectually sharp but never vulgar
3. Use clever metaphors, historical parallels, or philosophical references where appropriate
4. Demonstrate critical thinking through your response, not just criticism
5. Structure your critique with both an analytical deconstruction and a thought-provoking conclusion

## Visual Card Requirements
Create a clean, modern SVG card with:

1. A sophisticated minimal design with ample white space
2. A headline section with a clever title for your critique
3. The core critique presented as concise text (maximum 2-3 short paragraphs)
4. A simple line drawing or minimalist visualization that reinforces your critique
5. A concluding insight or alternative perspective as a footer element

## Visual Style Guidelines
- Use a neutral, elegant color palette (2-3 colors maximum)
- Employ clean typography with clear hierarchy
- Include simple line art or minimalist visualization elements
- Ensure readability and visual balance
- Design with a 3:4 or 9:16 aspect ratio

## Technical SVG Requirements
- Ensure the SVG follows widely supported standards SVG 1.1 specification
- Use viewBox attribute for proper scaling
- Include proper text elements with embedded fonts
- Keep the SVG code efficient and well-structured
- Limit file complexity to ensure universal compatibility

## Response Tone
Your critique should be:
- Intellectually sharp and insightful
- Witty and incisive without being cruel
- Sophisticated and nuanced
- Thought-provoking rather than dismissive
- Demonstrating wisdom through precise analysis

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments

Deliver both the critique and visualization as a single SVG card that elegantly combines text analysis with visual reinforcement.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_brutally_honest',
            co_exist: true,
            title: 'Brutally Honest Comment',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `## Task
Transform the given statement or viewpoint into a sharp, witty, brutally honest, and incisive critique presented as an elegant information card. Your response should be intellectually cutting but never vulgar.

## Response Style
1. Adopt a brutally honest, razor-sharp, sarcastic tone that cuts straight to the heart of any logical fallacies, contradictions, or weak points
2. Channel the intellectual precision of Oscar Wilde, Dorothy Parker, and Christopher Hitchens
3. Be brief and impactful - use concise language that delivers maximum impact
4. Maintain sophistication - your critique should reflect intelligence and wit, not crude humor
5. Use clever wordplay, irony, and rhetorical devices to enhance your critique

## Output Format
Create an SVG information card with:

1. A bold headline that captures the essence of your critique
2. A short, cutting response (30-50 words maximum)
3. A minimalist line drawing or simple visual metaphor that reinforces your critique
4. Modern, elegant design with clean typography and minimalist aesthetic
5. Standard SVG format that's widely compatible across platforms

## Visual Style Guidelines
- Use a neutral, elegant color palette (2-3 colors maximum)
- Employ clean typography with clear hierarchy
- Include simple line art or minimalist visualization elements represents your critique
- Ensure readability and visual balance
- Ensure all text remains readable at various sizes
- Design with a 3:4 or 9:16 aspect ratio

## Technical SVG Requirements
- Ensure the SVG follows widely supported standards SVG 1.1 specification
- Use viewBox attribute for proper scaling
- Include proper text elements with embedded fonts
- Keep the SVG code efficient and well-structured
- Limit file complexity to ensure universal compatibility

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments

Remember: Be sharp, brutally honest but sophisticated. Eviscerate ideas, not people. Your goal is to create a response that is intellectually devastating while remaining cleverly elegant.
`
        }, {
            objTypes: ['flow'],
            label: 'infographics',
            action: 'infographics_leveled_understanding',
            co_exist: true,
            title: 'Layered Understanding',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `Create a hierarchical, Feynman-style explanation of the provided concept or topic that progressively deepens understanding. Structure your explanation in 8 distinct levels that move from intuitive examples to core principles. Then visualize this explanation as a clean, modern SVG card.

1. **Analyze the concept** carefully, identifying its fundamental principles and how they connect to everyday experiences.

2. **Structure your explanation in these 8 progressive levels:**
   - **Level 1. INTUITIVE ANALOGY** - Create a vivid, everyday analogy that makes this concept instantly graspable to beginners.
   - **Level 2. PRACTICAL APPLICATION** - Show how this concept works in real-world scenarios and everyday life, building upon the intuitive analogy to demonstrate its relevance.
   - **Level 3. MECHANISM** - Explain how this concept actually functions - the underlying processes and relationships that power the practical applications previously described.
   - **Level 4. COUNTERINTUITIVE ASPECTS** - Highlight paradoxes or commonly misunderstood elements that surprise people, challenging the straightforward understanding established in earlier levels.
   - **Level 5. CONTRARY EXAMPLES & QUESTIONS** - Present examples or questions that challenge the concept, exploring its limitations or boundary conditions to deepen critical thinking.
   - **Level 6. FRESH PERSPECTIVE** - Step back and offer a broader or alternative perspective that transforms understanding of the concept. Connect it to other domains or recast it in a different light. 
   - **Level 7. CORE INSIGHT** - Distill the fundamental principle that makes everything else make sense, integrating both the mechanisms and the fresh perspectives into a cohesive understanding.
   - **Level 8. WIDER CONTEXT** - Show how this concept fits into larger knowledge frameworks and connects to other concepts, demonstrating how the core insight extends beyond the immediate topic.

3. **For each level:**
   - Make explanations vivid and memorable, as if teaching a curious child
   - Avoid jargon unless necessary 
   - Use concrete examples where helpful
   - Keep explanations concise but complete
   - Build progressively on previous insights to create a coherent journey
   - With Feynman's clarity, enthusiasm, and genuine curiosity—as if sharing an exciting discovery with a friend.

4. **Create an SVG information card that:**
   - Has a clean, minimalist card design for a modern feel
   - Elegant typography (web-safe fonts, 1.6 line height, 1.2em paragraph spacing)
   - Uses standard SVG 1.1 specification code that displays correctly across all platforms
   - Use viewBox attribute for proper scaling

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments
`
        }, {
            objTypes: ['flow'],
            label: 'One page slide',
            action: 'chart_slide',
            co_exist: true,
            title: 'One page slide',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `## Task Description
Generate a visually compelling, modern SVG slide based on the provided text content. The slide should effectively communicate the key insights in an elegant, visually engaging way that inspires deeper understanding.

## Requirements

### Content Requirements
1. Extract the most important concept, insight, or message from the provided text.
2. Create a clear, concise headline (5-8 words) that captures the core idea.
3. Include 1-3 supporting points that elaborate on the main concept.
4. If applicable, include a relevant quote or striking statistic from the text.
5. Provide a thought-provoking takeaway that encourages reflection.

### Design Requirements
1. Create a standard-compliant SVG that works across all modern browsers and applications.
2. Use a modern, clean aesthetic with appropriate white space.
3. Use a color palette of 2-4 complementary colors that evoke the emotional tone of the content.
4. Include at least one visual metaphor, icon, or simple illustration that represents the main concept.
5. Use typography hierarchy to distinguish between headline, main points, and supporting text.
6. Design for a 16:9 aspect ratio (standard presentation format).
7. Ensure all text elements are readable with appropriate font sizes.
8. Total text content should not exceed 100 words to maintain readability.

### Technical SVG Requirements
1. Use widely supported SVG 1.1 standards without relying on Javascript, CSS or proprietary features.
2. Ensure the SVG is fully self-contained and does not rely on external resources.
3. Provide a complete SVG document with proper viewBox attribute (e.g., viewBox="0 0 960 540").
4. Use web-safe fonts or embed font definitions within the SVG.
5. Use standard SVG elements (rect, circle, path, text, etc.) with proper attributes.
6. Avoid complex filters or effects that might reduce compatibility.
7. Include appropriate group elements (<g>) to organize content logically.
8. Ensure text elements use proper positioning and alignment attributes.

## Process
1. First, thoroughly analyze the provided text to identify its core message and supporting details.
2. Plan the slide's structure: headline, main points, visual elements, and layout.
3. Create the SVG structure with appropriate viewBox and dimensions.
4. Add background elements and decorative features.
5. Add text elements with proper styling and positioning.
6. Add visual elements that enhance understanding of the content.
7. Ensure all elements work together harmoniously.
8. Review and optimize the final SVG code.

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements and attributes.
   - Present the final output as a cohesive SVG file that communicates the information effectively

[Output requirements]: 
   - A line start with '## ' for title
   - A paragraph follow the title line for concise description of the content
   - The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
   - No other explanations and comments
`
        }, {
            objTypes: ['flow'],
            label: 'flowchart',
            action: 'chart_flowchart',
            co_exist: true,
            title: 'FlowChart',
            group: 1,
            temperature: 0.8,
            prompt: `Generate a Mermaid flowchart based on the topic or text provided. Your task is to create a clear, accurate flowchart that helps readers quickly understand the key steps or processes described in the content.

## Guidelines:

1. Analyze the provided content and identify the main process, workflow, or sequence of steps.
2. Create a Mermaid flowchart that accurately represents these steps or processes.
3. Use standard Mermaid syntax compatible with most Mermaid renderers.
4. Include only the most essential information to avoid cluttering the diagram.
5. Use clear, concise node labels.
6. Organize the flowchart in a logical sequence (typically top-to-bottom or left-to-right).

## Technical Requirements:

- Begin with \`flowchart TD\` (top-down) or \`flowchart LR\` (left-to-right) declaration.
- Use standard node shapes (rectangles, diamonds, etc.) appropriately.
- Use meaningful but short node IDs.
- Include clear directional arrows between nodes.
- Add appropriate labels to connections when needed.
- Avoid advanced or experimental Mermaid features that might not be universally supported.
- Keep the syntax clean and valid.
- Use subgraphs only when necessary to group related steps.
- Avoid ALL special characters including parentheses, dots in labes and texts.

## Output Format:
1. Title line starting with '## '
2. One-paragraph description
3. Complete mermaid code
4. No additional comments

Output Example:
"## Process Title

Brief description of the flow

\`\`\`mermaid
flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Step 1]
    B -->|No| D[Step 2]
    C --> E[End]
    D --> E
\`\`\`"`
        }, {
            objTypes: ['flow'],
            label: 'flowchart',
            action: 'chart_sequencediagram',
            co_exist: true,
            title: 'FlowChart',
            group: 1,
            temperature: 0.8,
            prompt: `You are tasked with converting textual descriptions of processes and interactions into Mermaid sequence diagrams. Follow these detailed instructions:

## Analysis Phase
1. Examine the given text to identify:
   - All participants/actors (systems, users, services, components)
   - Sequential order of interactions
   - Types of messages (synchronous, asynchronous, responses)
   - Control structures (conditions, loops, parallel processes)
   - Critical states and activation periods

2. Design a sequence diagram that accurately expresses the content of the given text based on its analysis, and implement it using Mermaid code.

## Mermaid syntax
- Begin with 'sequenceDiagram'
- Declare participants using 'participant [Name]'
- Use appropriate arrow types:
  - '->' for synchronous messages
  - '->>' for asynchronous requests
  - '-->>' for responses
  - '-->' for dotted arrows (optional operations)
- Use 'Note over/Note left of/Note right of' for important clarifications

## Output Format
1. Title line starting with '## '
2. One-paragraph description
3. Complete mermaid code
4. No additional comments

## Formatting Guidelines
1. Use clear, consistent naming for participants
2. Keep message descriptions concise but clear
3. Maintain proper indentation for readability
4. Add comments for complex logic
5. Group related interactions together
6. Ensure logical flow from top to bottom
7. Avoid ALL special characters including parentheses, dots in labes and texts

## Additional Instructions
1. Maintain technical accuracy in the diagram
2. Ensure all interactions are properly sequenced
3. Include error handling where applicable
4. Show parallel processes when relevant
5. Add timing annotations for critical operations
6. Use standard Mermaid syntax compatible with most Mermaid renderers

Please analyze the provided text and generate a corresponding Mermaid sequence diagram following these guidelines. The diagram should clearly illustrate the interaction flow between system components.`
        }, {
            objTypes: ['flow'],
            label: 'flowchart',
            action: 'chart_quadrant',
            co_exist: true,
            title: 'FlowChart',
            group: 1,
            temperature: 0.8,
            prompt: `Please help me create a Mermaid Quadrant Chart based on the given text. Follow these specific requirements:

1. Data Analysis Requirements:
   - Extract the key entities/items from the text
   - Identify two main dimensions/axes for comparison
   - Evaluate each item's position along both dimensions (low/high)
   - Determine appropriate axis labels based on the context
   - Translate all names and titles to English

2. Chart Structure:
   - Create a quadrant diagram with two intersecting axes
   - Label each axis with the identified dimensions
   - Place items in appropriate quadrants based on their evaluations
   - Include a title that captures the main theme
   - Add quadrant labels that describe each section's characteristics

3. Mermaid Syntax Requirements:
   - Use standard Mermaid quadrantChart syntax compatible with most Mermaid renderers
   - Set appropriate x-axis and y-axis titles
   - Define quadrant titles clearly
   - Position items with their coordinates (x, y)
   - Use a scale from 0 to 1 for both axes
   
Here's the template structure:

\`\`\`
quadrantChart
    title [Your Chart Title]
    x-axis [Low X] --> [High X]
    y-axis [Low Y] --> [High Y]
    quadrant-1 [Quadrant 1 Title]
    quadrant-2 [Quadrant 2 Title]
    quadrant-3 [Quadrant 3 Title]
    quadrant-4 [Quadrant 4 Title]
    [Item 1]: [x-coord, y-coord]
    [Item 2]: [x-coord, y-coord]
\`\`\`

4. Output Format
   - Title line starting with '## '
   - One-paragraph description
   - Complete mermaid code
   - Use English for all names and titles in the mermaid code
   - No additional comments

## Additional Instructions
1. Maintain technical accuracy in the chart
2. Ensure all names and titles in the mermaid chart is in English
3. Ensure the code strictly adheres to the Mermaid syntax specification

Please analyze the given text and generate a quadrant chart.`
        }, {
            objTypes: ['flow'],
            label: 'flowchart',
            action: 'chart_timeline',
            co_exist: true,
            title: 'FlowChart',
            group: 1,
            temperature: 0.8,
            prompt: `You are a specialized timeline generation assistant. I will provide you with a text describing a sequence of events, and your task is to:

1. Carefully analyze the key events and timestamps in the text
2. Organize these events chronologically
3. Generate a timeline diagram using Mermaid timeline syntax
4. Ensure the timeline clearly shows the sequence and relationships between events

Please use the following Mermaid timeline format:

\`\`\`mermaid
timeline
    title [Timeline Title]
    section [Phase/Category (if needed)]
        [Timestamp] : [Event Description]
\`\`\`

Important guidelines:
- Use standard Mermaid timeline syntax compatible with most Mermaid renderers
- Include only the most essential information to avoid cluttering the diagram
- Keep event descriptions concise and clear
- Use appropriate time formats (years, specific dates, etc.)
- Group related events using sections when applicable
- Use colons ( : ) to add important details after main event descriptions, ensuring spaces before and after the colon
- Maintain chronological order strictly

## Output Format
   - Title line starting with '## '
   - One-paragraph description
   - Complete mermaid code
   - No additional comments

## Additional Instructions
1. Maintain technical accuracy in the chart
2. Ensure the code strictly adheres to the Mermaid syntax specification
`
        }, {
            objTypes: ['flow'],
            label: 'Cornell Notes',
            action: 'chart_cornell_notes',
            co_exist: true,
            title: 'Cornell Notes',
            group: 1,
            temperature: 1,
            llms: ['claude-3.7-sonnet', 'gemini-2.5-pro'],
            prompt: `Create a single-page Cornell notes visualization in SVG format based on the topic or text I provide. The notes should help users learn, memorize, and review the content effectively.

## Requirements

1. Generate a standard Cornell notes layout with:
   - A title section at the top
   - A cue/question column on the left (approximately 30% width)
   - A notes column on the right (approximately 70% width)
   - A summary section at the bottom

2. Content requirements:
   - Title: Create a clear, descriptive title for the topic
   - Cue column: Include key questions, terms, or concepts that prompt recall
   - Notes column: Provide concise, organized notes on the main content with bullet points, hierarchical organization, and visual separation between concepts
   - Summary section: Include a 2-3 sentence synthesis of the most important ideas

3. SVG technical requirements:
   - Use widely supported SVG 1.1 standards without relying on Javascript, CSS or proprietary features
   - Set viewBox attribute properly for responsive scaling
   - Use text elements with standard fonts (Arial, Helvetica, or sans-serif)
   - Create clear visual hierarchy with appropriately sized text
   - Include basic lines and rectangles to define the Cornell notes structure
   - Ensure the SVG is fully self-contained and does not rely on external resources
   - Maintain readability on different screen sizes

4. Visual design:
   - Use a clean, minimalist design with sufficient white space
   - Apply consistent typography with appropriate font sizes for different content types
   - Use a limited, professional color palette (black text on white background with subtle accent colors for structure)
   - Ensure high contrast between text and background
   - Apply proper spacing and padding throughout the layout

## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.
   - Ensure SVG size and layout are suitable for all elements.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements.
   - Present the final output as a cohesive SVG file that communicates the information effectively.

The generated SVG should be complete and ready to use without modification, viewable in any standard SVG viewer, web browser, or document that supports SVG embedding.`
        }, {
            objTypes: ['flow', 'markdown'],
            label: 'Infographics',
            action: 'chart',
            sub_items: ['flowchart', 'sequencediagram', 'timeline', 'quadrant', 'cornell_notes', 'slide', 'infographics'],
            title: '改变语气',
            group: 1,
        }, {
            objTypes: ['slides'],
            label: 'Infographics',
            action: 'chart',
            sub_items: ['flowchart', 'sequencediagram', 'timeline', 'quadrant'],
            title: 'Infographics',
            group: 1,
        }, {
            objTypes: ['flow'],
            label: 'Expand Ideas',
            action: 'expand_ideas',
            sub_items: ['brainstorm', 'breakdown', 'first_principle', 'five_whys', 'scamper', 'problem_rephrazing', 'changing_perspectives', 'reverse_thinking', '5w1h', 'role_storming', 'triz', 'six_thinking_hats', 'disney_method', 'pros_cons', 'swots', 'value_proposition_canvas', 'bussiness_model_canvas', 'input'],
            title: 'Expand ideas',
            group: 1,
            // prompt_user: `{{sub_item}}`,
            prompt_user: `[Given topic]: {{user_input}};

[Specified mental model]: {{sub_item}};

You are a professional creative thinking expert specializing in mindmap expansion and idea generation. Your tasks are:
1. Deep thinking on the given topic and branch direction, considering but not limited by the provided context
2. Generate fresh, innovative ideas and high-quality ideas using specified mental model
3. Evaluate the quality of generated ideas
4. Output a standardized ideas array

# Thinking Process

1. Input Analysis
   - Understand the topic background and objectives
   - Define the specific scope of the branch direction
   - Identify key constraints

2. Deep Thinking
   If no specific mental model is specified, choose a suitable model base on the analyzed input:
   - First principles thinking
   - Lateral Thinking: Find analogies, associations, and innovations
   - Vertical Thinking: Conduct logical reasoning and causal analysis
   - SCAMPER Model
   - Contrarian perspectives
   - Cross-domain connections

3. Ideas Generation
   - Create a high-level framework that reflects the key elements of the chosen mental model
   - Ensure each idea is:
     * Highly relevant to the topic
     * Clearly and specifically described
     * Innovative

4. Quality Check
   Evaluate each idea on these dimensions:
   - Relevance: Connection to the topic
   - Innovation: Uniqueness of the idea
   - Feasibility: Ease of implementation
   - Impact: Potential value/effect
   - Completeness: Clarity of description

# Important Notes

1. Each Idea Must Be:
   - Specific
   - Have a clear value proposition
   - Comply with given constraints

2. If Qualified Ideas Are Less Than 3:
   - Conduct second round of thinking
   - Try alternative thinking frameworks or mental model

# Process Guidelines
   - Start with broad ideation
   - Refine ideas through quality check
   - Ensure diversity in solutions
   - Balance creativity with practicality

# Output Format
   - Output only the final ideas with concise sentences. Do not output intermediate thinking process and explanations.

\`\`\`json
{
  "generated":  {
      "items": ["idea 1", "idea 2", "idea 3", ...]
  }
}
\`\`\`
`
        }, {
            objTypes: ['flow_background'],
            label: '待办事项',
            action: 'flow_task_breakdown',
            title: '待办事项',
            group: 1,
            temperature: 1,
            prompt: `You will be given a task to break down into multiple high-quality subtasks, ensuring that each subtask is specific and actionable. If provided, you will also receive information about higher-level task breakdowns. Please follow these steps:

1. **Understand the Overall Goal and Context**: 
   - Clearly describe the ultimate goal of the task and the expected output.
   - If higher-level task breakdowns are provided, review them to understand the broader context.

2. **List Key Steps**: 
   - Logically list out the key steps needed to achieve the overall goal.
   - Ensure these steps don't overlap with tasks already covered in higher-level breakdowns (if provided).

3. **Break Down into Subtasks**: 
   - For each major step, further divide it into concise, actionable subtasks.
   - Use simple and straightforward language.
   - Avoid creating subtasks that may have already been addressed in higher-level breakdowns.

4. **Order Logically**: 
   - Ensure that the subtasks are ordered in a logical sequence.
   - Preceding tasks should be prerequisites for subsequent tasks.

5. **Comprehensive Review**: 
   - Check that the listed subtasks cover all necessary work to complete the task.
   - Ensure there's no redundancy with higher-level tasks (if provided).
   - Promptly supplement any missing parts.

6. **Present Clearly**: 
   - Output a clear, concise, and comprehensive list of subtasks in the specified format.

Remember: Always consider the context of higher-level task breakdowns (if provided) to ensure your subtasks are complementary and non-redundant.
            
The output language requirements for the return should only apply to the JSON field values, not impact the JSON structure itself. 
**Output your final generation with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):**

\`\`\`json
{
    "generated":  {
        "items": [
            {
                "description": "Subtask 1 description",
                "priority": "high/medium/low"
            },
            {
                "description": "Subtask 2 description", 
                "priority": "high/medium/low"
            },
            ...
        ]
    }
}
\`\`\``
        }, {
            objTypes: ['flow'],
            label: '任务分析',
            action: 'flow_task_analysis',
            title: '任务分析',
            group: 1,
            temperature: 1,
            prompt: `Act as a business consultant analyzing a complex task. Analyze the task in the given text (as task context), identifying goals, challenges, solutions (tools), risks (mitigation), workflow, and other key factors`
        }, {
            objTypes: ['ril', 'markdown', 'flow'],
            label: 'Critical Analysis',
            action: 'critial_analysis',
            title: 'Critical Analysis',
            group: 1,
            temperature: 0.8,
            prompt: `First, identify the type of the given text: news report, opinion piece, or research paper. Then provide a critical analysis and commentary based on the text type:

            For a news report:
            - Summarize the key facts and events reported
            - Evaluate the credibility and potential biases of the news source(s)  
            - Provide contextual background to help readers better understand the story
            - Discuss important perspectives or details that may have been omitted or underreported
            
            For an opinion piece:
            - Summarize the main arguments and claims made by the author(s)
            - Assess the strength of the evidence and reasoning used to support their stance
            - Point out any logical flaws, unsubstantiated assertions, or lack of nuance
            - Offer counterpoints and alternative viewpoints for a balanced perspective
            
            For a research paper:
            - Outline the methodology, findings, and conclusions of the study
            - Evaluate the strengths and limitations of the research design
            - Discuss the implications and real-world applications of the findings
            - Compare and contrast with other relevant studies in the field  
            
            Regardless of text type, your analysis should:
            - Encourage critical thinking by comprehensively examining multiple angles
            - Provide crucial context and fact-checking to separate truth from rhetoric
            - Discuss strengths and weaknesses in a fair and impartial manner
            - Ultimately enable readers to arrive at their own well-informed judgments
            
            Do not simply accept the claims at face value, but scrutinize them through a truth-seeking, nuanced lens. The goal is to deeply illuminate the issue, not to persuade. Draw upon reliable authoritative sources and employ objective, reasoned arguments to shed light on the complexities.`
        }, {
            objTypes: ['ril', 'instanote', 'instatext', 'markdown', 'slides', 'flow'],
            label: '发现谬误',
            action: 'fallacy',
            title: '谬误',
            group: 1,
            temperature: 1,
            prompt: 'I want you to act as a critic and analyze the given text for any fallacies or cognitive biases. For a given text, point out any logical errors or flawed reasoning in the argument'
        }, {
            //     objTypes: ['instanote', 'instatext', 'markdown', 'slides', 'flow_notes'],
            //     label: '论证',
            //     action: 'positive_cases',
            //     title: '案例',
            //     group: 1,
            //     temperature: 1,
            //     prompt: 'I want you to act as an persuasive debater and provide several concrete examples to support or explain a given statement. Please draw upon historical events, stories from famous literary works, anecdotes, or references to illustrate the statement. Your examples should be specific and demonstrate the validity of the statement. You also have to explain why the cases you gave can support this statement'
            // }, {
            //     objTypes: ['instanote', 'instatext', 'markdown', 'slides', 'flow_notes'],
            //     label: '论证',
            //     action: 'negative_cases',
            //     title: '案例',
            //     group: 1,
            //     temperature: 1,
            //     prompt: 'I want you to act as a persuasive debater and provide several compelling examples to refute a given statement. Please draw upon historical events, stories from famous literary works, anecdotes, or references to illustrate why the statement is not correct or comprehensive. Be thorough and convincing in presenting your arguments'
            // }, {
            objTypes: ['instanote', 'instatext', 'markdown', 'slides', 'flow'],
            label: '论证',
            action: 'proof',
            title: '论证',
            group: 1,
            temperature: 1,
            prompt: 'I want you to act as an informed debater who is skilled at providing well-structured arguments and supporting evidence. Based on the given claim, I would like you to offer a well-reasoned response, including logical reasoning, supporting evidence, and relevant examples to back up your stance. Be thorough and persuasive in presenting your arguments. Your task is to engage in a thoughtful and respectful debate, presenting your viewpoint convincingly'
        }, {
            objTypes: ['ril', 'instanote', 'instatext', 'markdown', 'slides', 'flow'],
            label: '反方观点',
            action: 'contradict',
            title: '反方观点',
            group: 1,
            temperature: 1,
            prompt: `I want you to act as a philosopher and use your text inference abilities to analyze a given text and infer the author's viewpoint. Then, I want you to generate a list of opposing or complementary viewpoints and provide detailed reasoning for each one. Make sure to consider the author's tone, word choice, and overall argument in your analysis`
        }, {
            objTypes: ['ril', 'instanote', 'flow'],
            label: '如何了解相关知识',
            action: 'related_knowledges',
            title: '相关知识',
            group: 1,
            temperature: 1,
            prompt: `I want you to act as a knowledgeable guide for the given text, I would like you to analyze the content and provide the following information:

            1. Domain: Based on the text, identify the primary domain or subject area being discussed (e.g., physics, history, literature, etc.).
            
            2. Key Points: Summarize the main points or key concepts covered in the text.
            
            3. Further Exploration: Suggest related topics or areas of knowledge that are relevant to the text and worth exploring further, based on the content.
            
            4. Learning Guidance: Provide recommendations on how someone could effectively learn or study the key points and related topics you identified. This could include suggesting resources (books, online courses, etc.), study methods, or practical exercises.
            
            Please present your analysis in a clear and structured manner, with separate sections for each of the four components (Domain, Key Points, Further Exploration, and Learning Guidance).`
            // prompt: `I want you to act as a knowledgeable guide for the given text. Your goal is to identify the key concepts and topics within the text and provide guidance on how to further understand them. Start by analyzing the text and identifying the main themes and ideas presented. Then, highlight any technical terms or jargon used in the text that may require further explanation. Next, suggest additional resources or reading material that can provide a deeper understanding of the concepts presented in the text. Finally, provide some tips or strategies for studying and retaining the information. Your ultimate goal is to help the reader gain a solid understanding of the subject matter and how to continue learning about it`
        }, {
            objTypes: ['markdown', 'slides', 'instatext'],
            label: '对内容进行续写',
            action: 'continue',
            title: '续写',
            group: 2,
            temperature: 1,
            prompt: `I want you to act as a creative writing assistant. The following is the first part of the article. Please understand the author's writing intention and continue to write the next paragraph. It is required to maintain a consistent style, coherent and logical content`
        }, {
            objTypes: ['instanote'],
            label: '对内容进行续写',
            action: 'continue',
            title: '续写',
            group: 2,
            deprecated: true,
            temperature: 1,
            prompt: `I want you to act as a creative writing assistant. The following is the first part of the article. Please understand the author's writing intention and continue to write the next paragraph. It is required to maintain a consistent style, coherent and logical content`
        }, {
            objTypes: ['markdown', 'instatext', 'slides', 'flow_notes'],
            label: '改善段落的流畅性和连贯性',
            action: 'optimize',
            title: '流畅性和连贯性优化版',
            group: 0,
            temperature: 0.7,
            prompt: 'Please refine and polish the given text. For short sentences, make them clear and easy to understand; for longer sentences or paragraphs, also improve their logical flow. Provide the rewritten text without any explanations.',
            //             prompt: `You are a skilled editing and writing expert. Your task is to improve the flow and coherence of the given text paragraphs to make them easier to read and understand.

            // **Instructions:**

            // 1. Carefully read the provided text.

            // 2. Analyze the paragraph structure, paying attention to the following:
            // * Are the transitions between sentences smooth?
            // * Do the paragraphs have clear topic sentences and supporting sentences?
            // * Are ideas arranged in a logical order?
            // * Are there any unnecessary repetitions or redundancies?

            // 3. Rewrite the text to improve its flow and coherence:
            // * Add transition words or phrases to improve the connections between sentences.
            // * Rearrange sentences or ideas to create a more logical flow.
            // * Delete or combine redundant information.
            // * Ensure that each paragraph revolves around a central theme.
            // * Split or merge paragraphs as needed.

            // 4. Maintain the main points and information of the original text.

            // 5. Ensure that the tone and style of the revised text are consistent with the original text.

            // 6. Provide the rewritten text without any explanations.

            // Remember, the goal is to improve the overall readability and coherence of the text while keeping the original information and intent intact.`
        }, {
            objTypes: ['markdown', 'flow'],
            label: '改变语气',
            action: 'tone',
            sub_items: ['professional', 'academic', 'casual', 'straightforward', 'confident', 'friendly', 'enthusiastic', 'empathetic'],
            title: '改变语气',
            group: 0,
            prompt: `**Task Description**  
You are an expert text editor skilled in rewriting content to match a specific tone. Based on the input below, rewrite the text to align with the specified tone.

**Target Tone**: {sub_action}  

**Output Requirements**  
- Rewrite the text using the target tone while preserving the original meaning.  
- Retain key information but adjust sentence structure, vocabulary, and expression style to reflect the target tone.  
- Output the rewritten text directly, no additional explanations or comments.`
        }, {
            objTypes: ['markdown'],
            action: 'tone_professional',
            co_exist: true,
            temperature: 0.7,
            prompt: `I want you to act as an authoritative spokesperson, infusing the text with a tone of formality and officiality, as if it represents an official communication. Your mission is to transform the given text, altering the tone and expression while keeping the original meaning intact. Let the words radiate a sense of authority and professionalism, capturing the attention and respect of the readers`
        }, {
            objTypes: ['markdown'],
            action: 'tone_casual',
            co_exist: true,
            temperature: 0.7,
            prompt: `I want you to act as a carefree chatterbox, infusing the text with an air of casualness, as if it's a relaxed conversation among friends. Your mission is to transform the given text, altering the tone and expression while keeping the original meaning intact. Let it exude an effortless, nonchalant vibe that puts the readers at ease and encourages them to unwind`
        }, {
            objTypes: ['markdown'],
            action: 'tone_straightforward',
            co_exist: true,
            temperature: 0.7,
            prompt: `I want you to act as a direct messenger, cutting through the noise and conveying complete information to the readers in a straightforward and concise manner. Your task is to transform the given text, altering the tone and expression while preserving the original meaning. Embrace simplicity, clarity, and brevity to deliver a message that is direct, yet comprehensive`
        }, {
            objTypes: ['markdown'],
            action: 'tone_confident',
            co_exist: true,
            temperature: 0.7,
            prompt: `I want you to act as a confidence amplifier, harnessing the power of words to instill unwavering belief in the hearts of readers. With your linguistic prowess, you can subtly alter the tone of the given text while preserving its original meaning, allowing the readers to feel the confidence conveyed and trust the author's words even more. Your task is to transform the following text, ensuring that the meaning remains unchanged while enhancing the tone to exude confidence and inspire trust in the readers`
        }, {
            objTypes: ['markdown'],
            action: 'tone_friendly',
            co_exist: true,
            temperature: 0.7,
            prompt: `I want you to act as a language transformer, gracefully molding words to create an atmosphere of warmth and kindness. With your linguistic prowess, you have the power to reshape the given text while preserving its original meaning, instilling a sense of friendliness and warmth in the hearts of the readers. Your task is to transform the following text, ensuring that the meaning remains intact while imbuing it with an aura of friendliness and warmth`
        }, {
            objTypes: ['markdown', 'slides', 'instatext', 'flow_notes'],
            label: '对内容进行扩写',
            action: 'extend',
            title: '加长版',
            group: 0,
            temperature: 0.9,
            prompt: `Enhance the given text by expanding on details, adding intricate descriptions, introducing necessary logical connections, and ensuring overall fluency. Create a comprehensive narrative by incorporating more descriptive language, relevant examples, and possibly analogies or metaphors. Craft a compelling story that captivates the reader's imagination. Provide the final extended version without explaining the changes`
        }, {
            objTypes: ['instanote'],
            label: '对内容进行扩写',
            action: 'extend',
            title: '加长版',
            group: 0,
            deprecated: true,
            temperature: 0.9,
            prompt: 'I want you to act as a copywriter. Your task is to expand a given text to make it more specific, detailed, and logical. You can also add more descriptive language, provide relevant examples, or use analogies and metaphors to help the reader better understand the content'
        }, {
            objTypes: ['markdown', 'slides', 'instatext', 'flow_notes', 'flow'],
            label: '对内容进行缩写',
            action: 'short',
            group: 0,
            temperature: 0.7,
            prompt: `Edit the provided passage for conciseness. Remove unnecessary details, adjectives, and descriptions, retaining only the main plot and essential elements such as time, location, and characters. Eliminate redundancies, filler words, and consider using abbreviations or acronyms. Rephrase sentences for efficiency, and combine shorter sentences into longer ones. Provide the final shortened version without explaining the changes`
        }, {
            objTypes: ['markdown', 'instatext', 'slides', 'flow', 'flow_notes'],
            label: '用更简单的语言进行改写',
            action: 'simplify',
            title: '改写后',
            group: 0,
            temperature: 0.7,
            prompt: 'Simplify the provided text using plain language and shorter sentences to enhance reader comprehension while preserving the original meaning. Provide the simplified version without explaining the changes'
        }, {
            objTypes: ['instanote'],
            label: '用更简单的语言进行改写',
            action: 'simplify',
            title: '改写后',
            group: 0,
            deprecated: true,
            temperature: 0.5,
            prompt: 'I want you to act as a simplification tool. Please rewrite the following content using simpler language and shorter sentences while retaining the original meaning. The goal is to make it easier for readers to understand'
        }, {
            objTypes: ['markdown', 'instanote', 'slides', 'flow'],
            label: '以小学生可以理解的语言进行解释',
            action: 'explain',
            title: '解释',
            group: 1,
            temperature: 0.8,
            prompt: `Explain the concept or subject in the given text in Richard Feynman's style. Use simple, intuitive language and avoid unnecessary jargon. Employ vivid analogies and everyday examples to make complex ideas accessible. Focus on the core principles as if you're explaining to a bright 12-year-old with no specialized background. Maintain curiosity and enthusiasm, highlighting what makes the concept wonderful. After your explanation, summarize the most important points`
            // prompt: `Context:
            // You are a professor and expert in given field. Your task is to thoroughly explain any given text, breaking down its content and providing insights into its meaning, significance, and potential implications. Keep in mind the context, tone, and purpose of the text as you interpret it.
            // Instructions:
            // 1. Summarize the main points of the text concisely.
            // 2. Identify and analyze key themes, arguments, or ideas presented.
            // 3. Explore the context of the text. Consider background information that may influence its meaning.
            // 4. Examine supporting evidence, examples, or references, explaining their contribution to the overall message.
            // 5. Reflect on potential implications or consequences of the ideas, considering both short-term and long-term effects.
            // 6. Offer your own insights, opinions, or critiques. Discuss agreement or disagreement with certain points and provide reasons.
            // 7. Relate the text to broader themes, issues, or trends in the relevant field or society if applicable.
            // 8. Conclude by summarizing the significance of the text and its potential impact on readers or the broader community.
            // `
            // prompt: `Imagine you're explaining the given paragraph to a group of middle school students who have a basic understanding of various topics. Your goal is to make the explanation simple, engaging, and easy to comprehend for them. Break down any complex terms or concepts, and provide real-life examples or analogies that a middle school audience can relate to. Keep in mind that the aim is to enhance their understanding, leveraging their existing knowledge while ensuring the information remains accessible to their level of comprehension`
            // prompt: 'I want you to act as a teacher, please try to explain following text to a 2nd grand student'
            // prompt: 'I want you to act as a teacher for explaining the given text in language that is easy for elementary school students to understand. Explain it using simple words and examples that will make it easy for young readers to comprehend. You can also use analogies, illustrations or stories to help them understand the topic better. Remember to use simple sentence structures and avoid using jargon or technical terms. Write it'
        }, {
            objTypes: ['instanote', 'instatext', 'flow_notes'],
            label: '批改作文',
            action: 'writing_teacher',
            title: '批改作文',
            group: 0,
            temperature: 1,
            prompt: `As an experienced essay teacher, provide comprehensive feedback on given student essays following these steps:

1. Assess Student Level
Evaluate the student's grade level and writing proficiency based on vocabulary, syntax, structure, and topic depth.

2. Provide Feedback
Based on your assessment of the given text(essays), give detailed feedback using this structure:

2.1 Overall Impression
Summarize main strengths and general impression.

2.2 Content and Ideas
- Clarity and creativity of main theme
- Development and support of arguments
- Relevance and persuasiveness of examples
- Depth and originality of insights

2.3 Structure and Organization
- Introduction effectiveness
- Paragraph transitions
- Conclusion effectiveness
- Overall logical flow

2.4 Language Use
- Vocabulary choice
- Sentence variety
- Use of rhetorical devices
- Tone and style

2.5 Technical Issues
- Grammar
- Spelling
- Punctuation
- Formatting

2.6 Suggestions for Improvement
Offer specific suggestions for each area above.

2.7 Praise for Highlights
Point out excellent parts, providing positive encouragement.

Notes:
- Adapt feedback to student level: basics for lower grades, structure and vocabulary for middle grades, depth of thought and argumentation for higher grades.
- For each point, quote specific examples from given text(essays), explain reasoning, and provide optimization suggestions.
- Ensure feedback is constructive and encouraging, aiming to inspire writing enthusiasm and confidence.`
        }, {
            objTypes: ['instanote', 'instatext', 'markdown', 'flow_notes'],
            label: '写作建议',
            action: 'writing_tutor',
            title: '写作建议',
            group: 0,
            temperature: 1,
            prompt: `As a writing coach, analyze the given text and provide tailored feedback to improve the author's writing skills. Assess their level (beginner/intermediate/advanced) based on structure, grammar, vocabulary, style, content, and impact. Then, offer feedback for the given text using this template:

1. Overall Assessment
   - Current level: [Beginner/Intermediate/Advanced]
   - Brief explanation (1-2 sentences)

2. Strengths (2-3 points)

3. Areas for Improvement (3-4 points, prioritized)
   For each:
   - Highlight a specific instance from the original given text
   - Provide a revision example
   - Offer a general tip

4. Writing Exercises (2-3 targeted exercises)
   For each:
   - Step-by-step explanation
   - Relation to improvement areas
   - Brief example of expected outcome

5. Genre-Specific Advice
   - Key conventions
   - Common pitfalls
   - Effectiveness techniques

6. Passage Revision
   - Original (3-5 sentences)
   - Revised version
   - Explanation of changes

Tailor feedback to the author's level, focusing on appropriate skills and complexity. Aim to inspire creativity and foster growth as a writer.`
        }, {
            objTypes: ['markdown', 'flow_notes', 'instatext'],
            label: '重写',
            action: 'rewrite',
            title: '重写',
            group: 0,
            temperature: 0.9,
            prompt: `Assuming the role of a professional text editor and optimization expert, you are tasked with refining and enhancing the provided text to improve its clarity, conciseness, and overall impact. Follow these steps:

1. Carefully review the original text to fully grasp its central message and intended purpose.

2. Evaluate the text's structure, language, and style, identifying areas that require improvement.

3. Engage in rewriting the text, paying close attention to the following aspects:
   - Utilize simpler and more impactful language choices.
   - Eliminate any instances of redundancy or repetition.
   - Enhance the overall sentence structure and paragraph organization.
   - Ensure a smooth and logical flow of ideas throughout the text.
   - Preserve the core message and tone of the original text.

4. Maintain the original meaning of the text while enhancing its readability and persuasiveness.

5. Correct any grammatical or spelling errors present in the original text during the rewriting process.

6. Provide the rewritten text along with a brief explanation of the primary modifications you have made.

Remember, the primary objective is to elevate the quality of the text without fundamentally altering its content or intended purpose. Proceed with optimizing and rewriting the given text`
        }, {
            objTypes: ['instanote'],
            label: '中心思想',
            action: 'central_ideas',
            title: '中心思想',
            group: 0,
            temperature: 1,
            prompt: `I want you to act as an insightful reader, delving into the depths of a given article to identify its central ideas. Your task is to read the article carefully and discern the main thoughts and concepts conveyed by the author. Once you have understood the essence of the article, provide a summary highlighting the key ideas and concepts presented`
        }, {
            objTypes: ['markdown', 'slides', 'flow_notes', 'instatext'],
            label: '修改拼写和语法错误',
            action: 'spelling',
            title: '修改后',
            group: 0,
            temperature: 0.3,
            prompt: `I want you to act as an intelligent language assistant, helping user correct spelling and grammar errors in a given text while preserving its original meaning`
        }, {
            objTypes: ['instanote', 'instatext'],
            label: '翻译成英文或对英文进行润色',
            action: 'translate_to_en',
            title: '翻译或润色结果',
            group: 0,
            temperature: 0.3,
            prompt: 'I want you to act as an English translator, spelling corrector and improver. User will speak to you in any language and you will detect the language, translate it and answer in the corrected and improved version of the text, in English. I want you to replace its simplified A0-level words and sentences with more beautiful and elegant, upper level English words and sentences. Keep the meaning same, but make them more literary. I want you to only reply the correction, the improvements and nothing else, do not write explanations'
        }, {
            objTypes: ['markdown', 'slides', 'flow'],
            label: '翻译成英文或对英文进行润色',
            action: 'translate',
            title: '翻译或润色结果',
            group: 0,
            sub_items: [],
            temperature: 0.3,
            prompt: 'Translate the given text into {sub_action}, ensuring clarity and simplicity while preserving the original meaning. Maintain a natural flow in the sentences. Output only the translated text directly, no other opennings, greetings, explanations or comments',
            // prompt: 'I want you to act as an {sub_action} translator, spelling corrector and improver. User will speak to you in any language and you will detect the language, translate it and answer in the corrected and improved version of the text, in {sub_action}. I want you to replace its simplified A0-level words and sentences with more beautiful and elegant, upper level {sub_action} words and sentences. Keep the meaning same, but make them more literary. I want you to only reply the correction, the improvements and nothing else, do not write explanations.'
        }, {
            objTypes: ['markdown', 'instatext'],
            label: '对内容进行总结',
            action: 'summary',
            title: '总结',
            group: 1,
            temperature: 0.6,
            prompt: 'Please ignore all previous instructions. You are an Extreme TL;DR summary generator, a new form of extreme summarization tool for paragraphs.TL;DR generation involves high source compression, removes stop words and summarizes the paragraph whilst retaining meaning.You will examine the text and come up with the best summary.The result will be the shortest possible summary that retains all of the original meaning and context of the paragraph.'
        }, {
            objTypes: ['markdown', 'instanote', 'instatext', 'ril'],
            label: '抽取待办事项',
            action: 'todos',
            title: '待办事项',
            group: 1,
            temperature: 1,
            prompt: `Serve as a personal assistant to identify actionable items within the provided text. Begin by outlining the author's intentions and goals. Then, infer and compose a clear list of tasks or actions the author expects the reader to undertake. Generate a concise list of action items based on your understanding of the author's intent`
        }, {
            objTypes: ['ril'],
            label: '写一段社交媒体分享文字',
            action: 'share_tips',
            title: '推荐语',
            group: 1,
            temperature: 1,
            prompt: 'I want you to act as a social media marketer and help user write a compelling promotional copy for the following content. You could highlight the most interesting or unique aspects of the content, using catchy headlines or attention-grabbing phrases. To make the content more engaging, you could also suggest including calls-to-action, such as inviting readers to share their own thoughts or experiences related to the content'
        }, {
            objTypes: ['markdown', 'instatext'],
            label: '生成标题',
            action: 'title',
            title: '生成标题',
            group: 1,
            temperature: 1,
            prompt: `I want you to act as a blog post title writer, given a text and you will reply with a blog post title. It should has a hook and high potential to go viral on social media`
        }, {
            objTypes: ['image'],
            label: 'Translate',
            action: 'translate_image',
            prompt: `Your task has two parts:

            1. Identify text in the image
               - Carefully examine the image and identify and transcribe any text content in the image (if any). Transcribe the text verbatim accurately.  
               - If there is no text in the image, explicitly state that.
            
            2. Translate the identified text to the target language
               - Translate any text you transcribed in Step 1 into {{Target Language}}.
               - If no text was detected in Step 1, state "No text to translate."
               - For the required translation, provide as accurate and literal a translation as possible while preserving the original meaning and tone.
            
            In your response, clearly separate these two steps in the following format:  
            
            Original Text:
            [Transcribe any original text from the image here]
            
            Translated to {{Target Language}}:
            [Provide the translation of the text here]
            
            If you cannot identify or translate any text in the image for any reason, explicitly state that and explain why.
            
            Please complete the text identification and translation task as per the above requirements`,
            group: 1,
        }, {
            objTypes: ['flow_background'],
            label: 'Image Caption',
            action: 'image_caption',
            prompt: `Your task is to generate an accurate, detailed, and descriptive caption for the provided image. Please:

1. Systematically observe and analyze the following elements in the image:
   - Main subjects and focal points
   - Scene and environmental context
   - Actions and activities
   - Colors and visual effects
   - Mood and atmosphere
   - Composition and perspective
   
2. When generating the caption, please:
   - Use clear and concise language
   - Prioritize the most prominent and important elements
   - Include key visual details
   - Capture the overall mood or emotion conveyed
   - Avoid subjective judgments or speculation
   - Maintain objective and accurate descriptions

[Output] Please generate a descriptive caption (no more that 50 words)

Evaluation Criteria:
- Accuracy: Does the description accurately reflect the image content?
- Completeness: Are key visual elements covered?
- Conciseness: Is the language clear and efficient?
- Relevance: Does the description capture the core theme of the image?`,
        }, {
            objTypes: ['image'],
            label: '解读',
            action: 'describe_image',
            prompt: `You are a professional image analyst. Your task is to carefully examine the given image and provide a detailed description and analysis of its important content. Please focus on the following aspects:
            
            1. Overall Content - Briefly summarize the main scene or subject matter depicted in the image.
            
            2. Key Elements - List the main elements in the image such as people, objects, setting etc. and describe their visual details and appearance. 
            
            3. Atmosphere and Mood - Evaluate the overall atmosphere and mood conveyed by the image, e.g. warm, dramatic, tense etc. Try to explain the visual cues that create this atmosphere.
            
            4. Detail Analysis - Take a deeper look at some interesting or significant details in the image that may reveal hidden meaning or background information.
            
            5. Personal Interpretation - Based on your understanding, offer your personal insights and interpretations about the themes, symbolism or narrative the image may be conveying.
            
            Please provide a comprehensive and thorough description and analysis of the image, drawing upon your professional expertise. Feel free to make comparisons to similar images or bring in background knowledge for more in-depth exploration if needed.`,
            group: 1,
        }, {
            objTypes: ['image'],
            label: '解读',
            action: 'describe_image_mindmap',
            prompt: `## Core Instruction Set
Your task is to perform a sophisticated, multi-layered analysis of the provided image, generating a structured JSON mind map that captures its essence.

## Stage 1: Image Type Classification
- Precisely identify the image type:
  * Artistic (painting, sketch, digital art)
  * Photographic (landscape, portrait, documentary)
  * Scientific (diagram, infographic, technical illustration)
  * Architectural (blueprint, design render)
  * Natural (geological, astronomical, biological)
  * Historical (archival, documentary)
  * Others

## Stage 2: Comprehensive Analysis Framework
### Content Layer Analysis
1. Primary Subject Identification
   - Core elements
   - Compositional structure
   - Color palette and emotional tone
   - Implicit and explicit narratives

### Technical & Contextual Evaluation
Based on image type, perform specialized analysis:
- Art Images: 
  * Artistic movement/style
  * Technique and execution
  * Symbolic representations
  * Historical/cultural context

- Scientific Images:
  * Data representation accuracy
  * Methodological insights
  * Visualization techniques
  * Research implications

- Architectural Designs:
  * Structural complexity
  * Functional design principles
  * Innovative elements
  * Environmental integration

- Natural/Landscape Images:
  * Ecological characteristics
  * Geographical context
  * Climatic/environmental indicators
  * Biological diversity

## Output Specifications
Generate a JSON mind map with:
- Precise, professional language
- Multi-level hierarchical perspectives and insights
- Transformative insights accross all perspectives
- Concise yet comprehensive description

## JSON Output Template
\`\`\`json
{
    "generated": {
        "title": "Precise Image Title",
        "description": "Concise, professional image content description and summarization",
        "key_perspectives": [
            {
                "name": "Primary Analytical Perspective",
                "branches": [
                    {
                        "name": "Secondary Analysis Branch",
                        "branches": [
                            "Detailed Insight Level 1",
                            "Detailed Insight Level 2"
                        ]
                    }
                ]
            }
        ],
        "transformative_insights": [
            "Unique Observation 1",
            "Unique Observation 2"
        ]
    }
}
\`\`\`

## Analytical Guidelines
- Maintain objectivity and academic rigor
- Respect the image's original intent
- Provide deep, valuable interpretations
- Focus on insights that expand understanding

## Processing Recommendations
1. Start with holistic observation
2. Drill down into specific details
3. Connect micro-observations to macro-understanding
4. Generate insights that transcend literal representation
`,
            group: 1,
        }, {
            objTypes: ['image'],
            label: '讲个故事',
            action: 'story_image',
            prompt: `### **Role & Objective:**  
You are an imaginative storyteller, renowned for transforming visual details into extraordinary narratives. Your task is to craft an engaging and richly layered story, drawing **exclusively** from the given image’s visible elements. Focus solely on what the image presents—its characters, environment, and implied context—without incorporating external details such as the creator, origin, or unrelated interpretations.  

---

### **Image Analysis Framework:**  
1. **Focused Observation:**  
   - Examine the image carefully to identify key visual components, such as characters, objects, environments, and actions.  
   - Look for subtle or hidden details and contrasts that can inspire plot twists or deeper meanings.  
   - Avoid referencing anything outside the image’s visible content.  
2. **Emotional Resonance:**  
   - Identify the emotions conveyed within the image, through expressions, colors, or atmosphere.  
   - Use these emotional cues to shape the mood, character motivations, and thematic depth of the story.  

---

### **Storytelling Requirements:**  
- **Length:** Minimum of 500 words.  
- **Perspective:** Use either first or third person, depending on which voice best amplifies the story's intensity.  
- **Genre:** Prioritize drama, suspense, mystery, or adventure to create tension and engagement.  

#### **Core Narrative Elements:**  
1. **Protagonist:**  
   - Base the main character entirely on the image's visual cues, adding depth through imaginative interpretation of their appearance, actions, or surroundings.  
2. **Conflict:**  
   - Develop a challenge or dilemma rooted in the image’s context, escalating as the story progresses.  
3. **Plot Structure:**  
   - **Opening Scene:** Start with a vivid and immersive moment drawn directly from the image.  
   - **Twists and Turns:** Use visual contrasts or ambiguities in the image to introduce unexpected developments.  
   - **Resolution:** Conclude with a meaningful or thought-provoking ending that feels true to the story.  
4. **Character Growth:**  
   - Explore the protagonist’s psychological depth, motivations, and transformations inspired solely by the image’s content.  

#### **Creative Techniques:**  
1. **Image-Centered Storytelling:**  
   - Limit the narrative to elements visible in the image, avoiding speculative or external contexts like the artist’s background or cultural origin.  
   - Leverage visual contrasts, implied relationships, and symbolic details within the image to craft a layered narrative.  
2. **Sensory Immersion:**  
   - Enrich the story with vivid descriptions of the visible setting, characters, and actions, engaging all five senses.  
3. **Dynamic Pacing:**  
   - Build tension and stakes by interpreting visual elements in a way that keeps the reader intrigued and invested.  

---

### **Output Expectations:**  
- **Polished Narrative:** Deliver a complete, high-quality story exceeding 500 words.  
- **Exclusive Context:** The story must be entirely rooted in and reflective of the image’s content, without external references.  
- **Engaging Storyline:** Captivate readers with a vivid narrative that stays faithful to the image.  
- **Seamless Execution:** Focus on storytelling without any meta-commentary or process explanations.`,
            group: 1,
        }, {
            objTypes: ['image'],
            label: '家庭作业',
            action: 'homework',
            prompt: `Please carefully examine the following image, which may contain handwritten or printed homework questions related to math, grammar, reading comprehension, or any other type of academic subject. Please provide the following:

            1. Transcribe the text of the question exactly as it appears in the image.
            
            2. Describe the type of question depicted (e.g. geometry proof, grammar fill-in-the-blank, etc.) and provide any relevant context about the problem.
            
            3. If you can fully understand the question, provide a clear and detailed answer to solve this homework problem. For open-ended questions, provide all reasonable possible solutions.
            
            4. If you cannot fully understand parts of the question, explicitly state which parts you cannot comprehend, and prioritize answering the parts you can understand and solve.
            
            5. If you need additional context such as textbook pages or assignment instructions, you may request that information.
            
            6. Use appropriate formatting and presentation styles for different question types (e.g. step-by-step work for math, proper grammar notation, etc.)
            
            Please do your best to provide a solution`,
            group: 1,
        }, {
            objTypes: ['image'],
            label: 'Memo',
            action: 'memo_maestro_image',
            title: '备忘录大师',
            group: 1,
            temperature: 0.5,
            prompt: `# Meeting Minutes Generation Prompt

Carefully examine the provided meeting photo and generate a detailed set of meeting minutes or memo. Your task is to:

1. Analyze the visual information in the photo, including but not limited to:
   - Content on whiteboards or projection screens
   - Any visible documents or notes
   - Number of attendees and general layout

2. Infer the meeting's topic and purpose based on your observations.

3. Create a structured set of meeting minutes including the following elements:
   - Meeting title (based on inferred topic)
   - Date and time (if visible)
   - Number of attendees (do not attempt to identify specific individuals)
   - Agenda items (based on information on boards/screens)
   - Key discussion points (derived from visible notes or diagrams)
   - Any apparent decisions or action items
   - Next steps (if any)

4. If there's any unclear or ambiguous content in the photo, note these uncertainties in the minutes.

5. Maintain a professional, concise tone and avoid subjective interpretations.

6. If there's insufficient information in the photo to complete certain sections, state that the information is lacking rather than inventing content.

Please generate the meeting minutes based on these guidelines, ensuring the content accurately reflects the information visible in the photo`
        }, {
            objTypes: ['flow_background'],
            label: '家庭作业',
            action: 'image_insights',
            prompt: `Carefully analyze the given image with an open and inspired mindset. Your goal is to extract insights that can spark creative thinking and deeper exploration. Complete these tasks:

1. Examine the image in detail, looking beyond the surface to uncover hidden meanings, connections, and potential inspirations:
   - Analyze all visual elements (composition, symbolism, style, etc.) and their interplay.
   - Consider the emotions, ideas, and broader themes the image evokes.
   - Reflect on how the image relates to various disciplines, current issues, and timeless human experiences.
   - Look for unique perspectives or unexpected details that could inspire novel thoughts.

2. Drawing from your insightful analysis, pose 3 thought-provoking, open-ended questions as if you were a visionary thinker or innovative mentor:
   - Craft questions that encourage exploring unconventional angles and making creative connections.
   - Focus on questions that could lead to breakthrough ideas or paradigm shifts in thinking.
   - Aim for questions that challenge assumptions and inspire imaginative problem-solving.
   - Avoid surface-level descriptions or asking for personal opinions. Instead, push for intellectual exploration and creative ideation.

3. Based on the inspirations drawn from the image, suggest 3 related topics with high potential for innovative exploration:
   - Propose topics that extend core concepts from the image in surprising or transformative ways.
   - Consider interdisciplinary connections that could lead to novel insights or applications.
   - Suggest areas where the image's themes could inspire new approaches to existing challenges.
   - Focus on topics that could guide users towards groundbreaking ideas or fresh perspectives.

Keep your deep analysis and creative insights in mind, but only output the final questions and topics in the specified JSON format.

**Output your final generation with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):**
    
\`\`\`json
{
    "generated":  {
        "related_questions": ["Question 1","Question 2", "Question 3"],
        "related_topics": ["Topic 1","Topic 2","Topic 3"]
    }
}
\`\`\`
`,
            group: 1,
        }, {
            objTypes: ['image'],
            label: 'WonderLens',
            action: 'witty_insights',
            prompt: `You are a witty commentator skilled in multiple languages and cultural contexts. Create an insightful commentary for the user's image in their specified language, following these guidelines:

1. STRUCTURE REQUIREMENTS:
- Begin with a thematic title;
- Commentary length: 4-6 lines;
- Maintain logical progression between lines;
- Adapt line length to natural rhythm of target language;

2. LANGUAGE-SPECIFIC ADAPTATION:
For English:
- Use clever wordplay and alliteration;
- Employ idioms and cultural references;
- Structure with iambic rhythm when possible;

For Japanese:
- Utilize kigo (seasonal references) and cutting words;
- Consider haiku-like imagery;
- Incorporate subtle wordplay (kakekotoba);

For French:
- Employ alexandrine rhythm when suitable;
- Use elegant metaphors and double entendre;
- Include subtle cultural references;

For Chinese:
Core Elements:
- Follow 起承转合 (opening-development-transition-conclusion) structure;
- Employ parallel construction (对仗) with balanced phrases;
- Blend classical allusions (典故) with modern context;
- Create artistic conception (意境) through layered imagery;
- Use wordplay (双关语) and subtle irony;

Cultural Integration:
- Draw from traditional wisdom (Taoist duality, Buddhist impermanence);
- Balance traditional symbols with contemporary references;
- Include gentle social commentary;
- End with philosophical reflection;

3. UNIVERSAL STYLISTIC ELEMENTS:
- Maintain wit while preserving depth;
- Use metaphors appropriate to target culture;
- Transform ordinary objects into philosophical symbols;
- End with an unexpected twist or revelation;

4. CONTENT PROGRESSION:
Lines 1-2: Describe physical appearance/situation;
Lines 3-4: Develop symbolic meaning;
Final lines: Reveal deeper truth or ironic observation;

5. CULTURAL CONSIDERATIONS:
- Adapt metaphors to target culture;
- Use culture-specific literary devices;
- Reference local wisdom traditions;
- Match humor style to cultural norms;

Examples in different languages:

ENGLISH:
Power Pose
A gesture frozen in campaign-day light,
Where flags wave freedom's stars so bright.
Each fist raised high sells stories untold,
Of power's promise bought and sold.
Through history's lens we'll come to see
How moments make mythology.

FRENCH:
Papillon Éphémère
Dans l'azur figé du temps qui passe,
Un papillon aux ailes de glace.
Sa beauté cache les chagrins d'hier,
Comme un masque de lumière.
Chaque vol efface une mémoire,
Jusqu'à l'oubli de notre histoire.

CHINESE:
时代之镜
玻璃幕墙映照都市浮华，
每个窗格都藏一段人生话。
高楼之间寻找蓝天碎片，
却见云影倒映地铁穿花。
据说这是现代人的寓言，
困在透明里的不透明啊。

Instructions for Implementation:
1. Identify target language and its literary traditions;
2. Select appropriate cultural references and metaphors;
3. Apply language-specific literary devices;
4. Maintain philosophical depth while respecting cultural norms;
5. Ensure wordplay and metaphors translate meaningfully;
6. Adapt rhythm and structure to language conventions;

When crafting the commentary:
- Consider the target language's poetic traditions;
- Use culturally resonant symbols;
- Maintain appropriate formality level;
- Ensure cultural sensitivity;

Please generate a commentary in the user's specified language that captures both wit and wisdom while honoring the linguistic and cultural traditions of the target language.

**Output your final commentary with following RFC8259-compliant JSON structure(Do not output your intermediate thinking process):**
\`\`\`json
{
    "generated":  {
        "commentary": ["Title", "Line 1","Line 2"...]
    }
}
\`\`\`
`,
            group: 1,
        }, {
            objTypes: ['codes'],
            label: 'Bug fix',
            action: 'fix_codes_bug',
            prompt: `You are a specialist in fixing broken Mermaid diagrams and SVG graphics. Analyze the provided code, identify issues, and provide working fixes.

## Fix Coverage

**Mermaid Issues:**
- Syntax errors and invalid structure
- Node definition problems
- Incorrect connection syntax
- Wrong diagram type declarations
- Special character handling
- Indentation and formatting issues

**SVG Issues:**
- XML syntax errors (unclosed tags, malformed attributes)
- Missing namespace declarations
- Invalid coordinates and dimensions
- Malformed path data
- CSS style errors
- Incorrect element nesting
- **Layout problems** (from visual designer perspective):
  - Incorrect positioning and alignment
  - Text overflow beyond boundaries
  - Missing or improper text wrapping
  - Poor spacing and visual hierarchy

## Workflow

1. **Code Analysis**: Carefully review the provided code to identify syntax errors and structural issues.
2. **Issue Diagnosis**: Clearly point out the specific problems found and their potential impact.
3. **Fix Implementation**: Provide the complete, corrected code.

## Response Format

Resonpse only the fixed code block:

\`\`\`mermaid
[Complete corrected code]
\`\`\`
or
\`\`\`svg
[Complete corrected code]
\`\`\`

## Requirements
- Ensure code renders correctly
- Preserve original design intent
- Follow syntax standards
- Keep code clean and maintainable
`,
            group: 1,
        }, {
            objTypes: ['codes'],
            label: 'Bug fix',
            action: 'fix_codes_bug_svg',
            prompt: `You are an expert SVG designer and developer. Fix all issues in the given SVG code to ensure proper display and layout.

## Check and Fix:

**Syntax & Display:**
- Fix XML syntax errors, unclosed tags, invalid attributes
- Correct viewBox, width/height settings
- Ensure all elements are visible (fix fill/stroke issues)
- Validate path data

**Layout & Positioning:**
- Fix text overflow and positioning issues
- Implement proper text wrapping using \`<tspan>\` or \`<foreignObject>\`
- Align text with containers/backgrounds correctly
- Fix element overlapping and spacing
- Ensure list markers align with text
- Correct transform applications

**Design & Balance:**
- Improve overall visual balance and proportions
- Optimize element spacing and alignment
- Enhance color contrast and hierarchy

**Output**
- Return only the **final corrected SVG code**.
- No explanations or comments.

\`\`\`svg
[Complete corrected code]
\`\`\`

## Requirements
- Ensure code renders correctly
- Preserve original design intent
- Follow syntax standards
`,
            group: 1,
        }, {
            objTypes: ['codes'],
            label: 'Bug fix',
            action: 'fix_codes_bug_mermaid',
            prompt: `You are a Mermaid diagram code expert. Fix the given Mermaid code to ensure it follows proper syntax and renders correctly.

## Key Issues to Fix:

**Quote Problems**: Remove or properly escape \`"\`, \`"\`, \`'\` in node names/labels
**Bracket Issues**: Handle \`(\`, \`)\`, \`[\`, \`]\`, \`{\`, \`}\` that conflict with Mermaid syntax
**Special Characters**: Fix \`|\`, \`\\\`, \`\/\` and other problematic characters

## Rules:

1. **Node Labels**: Use double quotes for labels with special chars: \`A["Label with spaces"]\`
2. **Node Names**: Use simple alphanumeric names (no spaces): \`userLogin\` not \`user login\`
3. **Syntax**: Verify diagram type, connections, and node shapes are correct
4. **Clean**: Remove extra quotes, fix incomplete connections, proper indentation

## Example Fix:

**Before:**
\`\`\`mermaid
graph TD
    A[User "Login"] --> B(Success(Show message)?)
    B --> C{Admin"Role}
\`\`\`

**After:**
\`\`\`mermaid
graph TD
    A["User Login"] --> B["Success(Show message)?"]
    B --> C{"Admin Role"}
\`\`\`

## Output:
- Provide only the fixed Mermaid code

\`\`\`mermaid
<fixed, valid Mermaid code that renders>
\`\`\`
`,
            group: 1,
        }, {
            objTypes: ['codes'],
            label: 'Bug fix',
            action: 'improve_codes',
            prompt: `You are an expert in optimizing given Mermaid diagrams and SVG graphics according to user requirements. Follow this structured approach:

## Process

### 1. Analyze Requirements
- Parse user's specific modification requests
- Identify functional, visual, and performance needs
- Assess current code structure and limitations
- Spot improvement opportunities

### 2. Design Solution
- Create modification strategy based on requirements
- Apply best practices and design principles
- Balance functionality, aesthetics, and maintainability
- Define expected outcomes

### 3. Implement Changes
- **Use standard syntax only** - avoid experimental or version-specific features
- Follow established patterns from official documentation
- Ensure broad compatibility across platforms and versions
- Maintain clean, readable code structure
- Test against common rendering environments

## Technical Standards

**Mermaid**: Use only stable, widely-supported syntax. Stick to standard diagram types and basic styling. Avoid experimental features or version-specific syntax. Test compatibility across major Mermaid versions.

**SVG**: Use standard SVG 1.1/2.0 elements and attributes with broad browser support. Avoid vendor-specific extensions or cutting-edge features with limited compatibility.

## Quality Criteria

- ✅ **Functional**: Meets all user requirements, renders correctly
- ✅ **Standards-compliant**: Valid syntax using stable, widely-supported features
- ✅ **Visual**: Well-balanced layout, consistent styling
- ✅ **Maintainable**: Clean code, appropriate comments

## Output Format

Provide complete, working code only:

\`\`\`mermaid
[Complete working code - no placeholders]
\`\`\`
or
\`\`\`svg
[Complete working code - no placeholders]
\`\`\`

## Guidelines

- **Provide complete, working code only**
- **Keep code clean and concise**
- **Consider responsive design when applicable**
- **Optimize for performance without sacrificing clarity**
`,
            group: 1,
        }, {
            objTypes: ['markdown', 'slides'],
            label: '添加自定义指令',
            action: 'add_prompt',
            group: 9,
        }]
    },

    uninstall_reasons: ['no_required_feature', 'have_better_choice', 'encounter_issues', 'do_not_like', 'no_requirement', 'others'],

    team_bootcamp_docs: [
        {
            //     hid: 'db_demo_cn',
            //     space: 'workspace'
            // }, {
            hid: 'funblocks_project_cn',
            space: 'workspace'
        }, {
            hid: 'hello_funblocks_cn',
            space: 'workspace'
        }, {
            hid: '85de4bdcad70b670585a8a22fb58ffdd',
            space: 'workspace'
        }, {
            hid: 'funblocks_team',
            space: 'workspace'
        }, {
            hid: 'funblocks_class_cn',
            space: 'workspace'
            // }, {
            //     hid: 'make_video_cn',
            //     space: 'workspace'
            // }, {
            //     hid: 'doc_slides_cn',
            //     space: 'workspace'
        }, {
            hid: 'funblocks_guide',
            space: 'private'
        }, {
            hid: 'funblocks_ril',
            space: 'private'
        }, {
            hid: 'funblocks_ai_cn',
            space: 'workspace'
        }, {
            hid: 'instant_notes_intro_cn',
            space: 'private'
        }, {
            hid: 'xslides_demo_cn',
            space: 'workspace'
        }, {
            hid: 'funblocks_intro_cn',
            space: 'workspace'
        }
    ],

    team_bootcamp_docs_en: [
        {
            hid: 'hello_funblocks_en',
            space: 'workspace'
        }, {
            hid: 'funblocks_guide_en',
            space: 'private'
        }, {
            hid: 'funblocks_ai_en',
            space: 'workspace'
        }, {
            hid: 'xslides_demo_en',
            space: 'workspace'
        }
    ],

    personal_bootcamp_docs: [
        {
            //     hid: 'db_demo_cn',
            //     space: 'workspace'
            // }, {
            hid: 'funblocks_life_cn',
            space: 'workspace'
        }, {
            hid: 'hello_funblocks_cn',
            space: 'workspace'
        }, {
            //     hid: '56ca63bc8ca1760cd23ba01a8e245bf5',
            //     space: 'workspace'
            // }, {
            hid: 'funblocks_team',
            space: 'workspace'
        }, {
            hid: 'funblocks_class_cn',
            space: 'workspace'
            // }, {
            //     hid: 'make_video_cn',
            //     space: 'workspace'
            // }, {
            //     hid: 'doc_slides_cn',
            //     space: 'workspace'
        }, {
            hid: 'funblocks_guide',
            space: 'private'
        }, {
            hid: 'funblocks_ril',
            space: 'workspace'
        }, {
            hid: 'funblocks_ai_cn',
            space: 'workspace'
        }, {
            hid: 'instant_notes_intro_cn',
            space: 'workspace'
        }, {
            hid: 'xslides_demo_cn',
            space: 'workspace'
        }, {
            hid: 'funblocks_intro_cn',
            space: 'workspace'
        }
    ],

    personal_bootcamp_docs_en: [
        {
            hid: 'hello_funblocks_en',
            space: 'workspace'
        }, {
            hid: 'funblocks_guide_en',
            space: 'private'
        }, {
            hid: 'funblocks_ai_en',
            space: 'workspace'
        }, {
            hid: 'xslides_demo_en',
            space: 'workspace'
        }
    ],

    ril_bootcamp_articles: [
        {
            id: '6424ed7257069f6f3df19271',
            source: 'ril'
        }, {
            id: '64250d2e57069f6f3df19392',
            source: 'ril'
        }, {
            id: '64590c71c211c0577b241ee9',
            source: 'others'
        }
    ],

    rilBootDocs: [{
        hid: 'instant_notes_cn',
        space: 'private',
        docInfo: {
            articleId: 'instant_notes'
        }
    }],

    rilBootDocs_en: [{
        hid: 'instant_notes_en',
        space: 'private',
        docInfo: {
            articleId: 'instant_notes'
        }
    }],

    adminUser: {
        userId: '5f9f934efaa7071324c9fe13',
        orgId: '6249a42b7ab9787a362bbd77'
    },

    feedbackDB: {
        hid: 'user_feedbacks',
    },

    invite_friend_award: 200,
    invited_award: 100,

    ai_coins_skus: [{
        productId: 'coins_20',
        coins: 20,
        price: 1,
    }, {
        productId: 'coins_50',
        coins: 50,
        price: 2
    }, {
        productId: 'coins_100',
        coins: 100,
        price: 4
    }, {
        productId: 'coins_200',
        coins: 200,
        price: 7
    }, {
        productId: 'coins_300',
        coins: 300,
        price: 10
    }, {
        productId: 'coins_700',
        coins: 700,
        price: 20
    }],

    aiplus_coins_skus: [{
        productId: 'coins_50',
        coins: 50,
        price: 1,
    }, {
        productId: 'coins_100',
        coins: 100,
        price: 2
    }, {
        productId: 'coins_250',
        coins: 250,
        price: 4
    }, {
        productId: 'coins_500',
        coins: 500,
        price: 7
    }, {
        productId: 'coins_700',
        coins: 750,
        price: 10
    }, {
        productId: 'coins_1600',
        coins: 1600,
        price: 20
    }],

    skus: [{
        productId: 'ril_vip_1',
        price: 20,
        title: '一个月',
        description: '一个月会员权益'
    }, {
        productId: 'ril_vip_3',
        price: 56,
        title: '三个月',
        description: '三个月会员权益'
    }, {
        productId: 'ril_vip_6',
        price: 108,
        title: '半年',
        description: '六个月会员权益'
    }, {
        productId: 'ril_vip_12',
        price: 199,
        title: '一年',
        description: '一年会员权益'
    }, {
        productId: 'ai_vip_1',
        price: 30,
        title: '一个月',
        description: '一个月会员权益'
    }, {
        productId: 'ai_vip_3',
        price: 86,
        title: '三个月',
        description: '三个月会员权益'
    }, {
        productId: 'ai_vip_6',
        price: 166,
        title: '半年',
        description: '六个月会员权益'
    }, {
        productId: 'ai_vip_12',
        price: 299,
        title: '一年',
        description: '一年会员权益'
    }, {
        productId: 'funblocks_plus_1',
        stripe_product_id: 'price_1NrijTHZxvPjnxC86ovsWlaK',
        recurring: 'monthly',
        trial_days: 7,
        price: 4.99,
        title: 'plus',
        service_level: 'plus'
    }, {
        productId: 'funblocks_plus_12',
        stripe_product_id: 'price_1NrijUHZxvPjnxC8RikkRPaH',
        recurring: 'yearly',
        trial_days: 7,
        price: 49.9,
        title: 'plus',
        service_level: 'plus'
    }, {
        productId: 'aiplus_premium_1',
        stripe_product_id: 'price_1NrilDHZxvPjnxC8IyL8lYxL',
        recurring: 'monthly',
        price: 6.99,
        title: 'Monthly',
        service_level: 'premium',
        description: 'billed monthly'
    }, {
        productId: 'aiplus_premium_12',
        stripe_product_id: 'price_1NrilDHZxvPjnxC80ihh7mlZ',
        recurring: 'yearly',
        price: 69.9,
        title: 'Yearly',
        service_level: 'premium',
        description: 'billed yearly'
    }],

    products: {
        ril: {
            free: {
                name: 'free',
                priority: 0,

                privileges: {
                    rils: 100,
                    rilsRead: false,
                    rilsTrash: false,
                    notesTrash: false,
                    media_count: 100,
                    privatePrompts: 3,
                    usePrivateAIApi: false,
                    // askAI: {
                    //     priority: 0,
                    //     desc: 'aicoins'
                    // }
                    askAI: {
                        priority: 1,
                        desc: 'daily_quota',
                        value: 10,
                        t1: 0,
                        t2: 10,
                        period: 'day'
                    },
                }
            },
            vip: {
                name: 'vip',
                priority: 1,

                privileges: {
                    rils: 'unlimited',
                    rilsRead: true,
                    rilsTrash: true,
                    notesTrash: true,
                    media_count: 'unlimited',
                    privatePrompts: 30,
                    usePrivateAIApi: true,
                    askAI: {
                        priority: 2,
                        desc: 'daily_quota',
                        value: 40,
                        t1: 0,
                        t2: 40,
                        period: 'day'
                    },
                }
            }
        },
        ai: {
            free: {
                name: 'free',
                priority: 0,

                privileges: {
                    privatePrompts: 3,
                    usePrivateAIApi: false,
                    askAI: {
                        priority: 1,
                        desc: 'daily_quota',
                        value: 10,
                        t1: 0,
                        t2: 10,
                        period: 'day'
                    },
                }
            },
            vip: {
                name: 'vip',
                priority: 1,

                privileges: {
                    rils: 'unlimited',
                    rilsRead: true,
                    rilsTrash: true,
                    notesTrash: true,
                    media_count: 'unlimited',
                    privatePrompts: 30,
                    usePrivateAIApi: true,
                    askAI: {
                        priority: 9,
                        desc: 'unlimited'
                    }
                }
            }
        },
        xslides: {
            free: {
                name: 'free',
                priority: 0,
                privileges: {
                    tts_voices: 10,
                    media_count: 100,
                    mediaCollection: true,
                    askAI: {
                        priority: 1,
                        desc: 'daily_quota',
                        value: 10,
                        t1: 0,
                        t2: 10,
                        period: 'day'
                    },
                }
            },
            vip: {
                name: 'vip',
                priority: 1,
                privileges: {
                    tts_voices: 'unlimited',
                    media_count: 'unlimited',
                    mediaCollection: true,
                    askAI: {
                        priority: 9,
                        desc: 'unlimited'
                    }
                }
            }
        },
        funblocks: {
            free: {
                name: 'free',
                priority: 0,

                privileges: {
                    blocks: 1000,
                    media_count: 100,
                    members: 2,
                    usePrivateAIApi: true,
                    privatePrompts: 3,

                    askAI: {
                        priority: 1,
                        desc: 'daily_quota',
                        value: 10,
                        t1: 0,
                        t2: 10,
                        period: 'day'
                    },
                },
                promote_text: "Upgrade to Plus to get unlimited blocks, unlimited file upload, up to 20 private self-defined AI assistant prompts, and more.",
            },
            plus: {
                name: 'plus',
                priority: 1,

                privileges: {
                    blocks: 'unlimited',
                    media_count: 'unlimited',
                    members: 5,
                    usePrivateAIApi: true,
                    privatePrompts: 20,
                    askAI: {
                        priority: 1,
                        desc: 'daily_quota',
                        value: 10,
                        t1: 0,
                        t2: 10,
                        period: 'day'
                    },
                },
                description: 'With unlimited blocks, unlimited file uploads, 20 private self-defined AI assistant prompts and more.',
            },
            // smallteam: {
            //     name: 'smallteam',
            //     priority: 2,
            //     userProvidedAIApi: true,
            //     privatePrompt: 100,

            //     privileges: {
            //         blocks: 'unlimited',
            //         media_count: 'unlimited',
            //         members: 10,
            //         askAI: {
            //             priority: 1,
            //             desc: 'daily_quota',
            //             value: 10,
            //             period: 'day'
            //         },
            //     }
            // },

        },
        aiplus: {
            free: {
                name: 'free',
                priority: 0,

                privileges: {
                    askAI: {
                        priority: 1,
                        desc: 'daily_quota',
                        value: 10,
                        t1: 0,
                        t2: 10,
                        period: 'day'
                    },
                },
                promote_text: 'If you do not have any third-party LLM API keys that FunBlocks supports, you can simply purchase FunBlocks AI Premium. This will provide you with unlimited writing assistance and document generation capabilities.',
            },
            premium: {
                name: 'premium',
                priority: 1,

                privileges: {
                    privatePrompts: 30,
                    askAI: {
                        priority: 9,
                        //use desc as value when check privilege
                        desc: 'unlimited'
                    }
                },
                description: 'Premium for unlimited writing assistance and document generation by FunBlocks AI',
            },
        }
    }
};