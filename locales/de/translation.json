{"phone": "Telefon", "email": "E-Mail", "somebody_workspace": "Workspace von {{nickname}}", "somebody_writingspace": "<PERSON><PERSON> von {{nickname}}", "powered_by": "Bereitgestellt von", "db_new_property_name": "Name", "db_new_property_status": "Status", "db_new_property_createdAt": "Erstellt am", "db_new_property_url": "URL", "db_new_property_pin": "<PERSON><PERSON>", "default_status_name": "Status", "default_date_name": "Datum", "db_view_table": "<PERSON><PERSON><PERSON>", "db_view_board": "Ta<PERSON><PERSON>", "db_view_list": "Liste", "db_view_gallery": "Galerie", "db_view_timeline": "Zeitleiste", "db_view_chart": "Diagramm", "db_new_link_to_view": "<PERSON><PERSON> anzeigen", "db_property_default_label_prefix": "Eigenschaft ", "untitled_new_db": "Neue Datenbank", "untitled_new_slides": "Neue Folien", "untitled_new_flow": "Neuer AIFlow", "untitled_new_doc": "Neue Seite", "untitled_db": "Unbenannte Datenbank", "untitled_slides": "Unbenannte Folien", "untitled_flow": "Unbenannter AIFlow", "untitled_doc": "Unbenannte Seite", "todo": "Aufgabe", "doing": "In Bearbeitung", "done": "<PERSON><PERSON><PERSON>", "seminar_powered_by": "<PERSON><PERSON> wird von <PERSON> un<PERSON>tützt", "present_powered_by": "Diese Folien werden von <PERSON> unterstützt", "seminar_slides_slogan": "@<PERSON><PERSON><PERSON><PERSON>, Co-Pilot mit KI-Assistent", "present_slides_slogan": "@<PERSON><PERSON><PERSON><PERSON>, Co-Pilot mit KI-Assistent", "api_level_1": "Fortgeschrittenes KI-Modell", "api_level_2": "Standard KI-Modell", "message": {"user_not_exist": "Benutzer existiert nicht oder falsches Passwort", "user_existed": "Benutzer existiert bereits", "verification_code_error": "Fehler beim Verifizierungscode oder abgelaufen", "oauth_user_unmatched": "Benutzerinformationen stimmen während der Authentifizierung nicht überein", "too_frequent": "Operation zu häufig", "verification_code_sent": "Verifizierungscode an Ihre ", "sms_daily_limit": "Tägliches SMS-Limit erreicht", "sms_service_error": "Fehler im SMS-Dienst", "activation_email_sent": "Aktivierungsnachricht an Ihre E-Mail-Adresse gesendet, Sie können sie später überprüfen und aktivieren", "keep_permission_to_operator": "Alle Berechtigungen bei sich behalten", "same_version": "Gleiche Version", "version_not_found": "Version nicht gefunden", "org_not_exist_or_no_access": "Organisation existiert nicht oder Si<PERSON> haben keinen Zugriff", "content_no_access": "Keine Berechtigung für den Zugriff auf diesen Inhalt, bitte melden Si<PERSON> sich bei FunBlocks an oder kontaktieren Sie den Inhaltsbesitzer", "operation_failed": "Operation fehlgeschlagen, bitte versuchen Sie es später erneut", "operation_success": "Operation erfolgreich", "server_error": "<PERSON><PERSON><PERSON>", "recaptcha_verification_failed": "ReCAPTCHA-Überprüfung fehlgeschlagen"}, "notification": {"verification_email_subject": "Bestätigen Sie Ihre E-Mail bei FunBlocks", "verification_email_text": "<PERSON><PERSON>, \n\n{{account}} wurde gerade bei FunBlocks registriert. Klicken Sie auf den folgenden Link, um Ihre E-Mail bei FunBlocks zu aktivieren/zu bestätigen, {{activate_url}}, oder kopieren Sie den Link und öffnen Si<PERSON> ihn im Browser. \n<PERSON><PERSON> dies nicht Ihre Absicht war, ignorieren Sie bitte diese E-Mail. \n\nDanke,\nFunBlocks-Team", "verification_email_html": "<PERSON><PERSON>, <br/><br/>{{account}} wurde gerade bei FunBlocks registriert. Klicken Sie auf den folgenden Link, um Ihre E-Mail bei FunBlocks zu aktivieren/zu bestätigen, <a href='{{activate_url}}'>aktivieren</a>, <br/>Oder kopieren Sie den folgenden Link und öffnen Sie ihn im Browser: {{activate_url}} <br/><PERSON><PERSON> dies nicht Ihre Absicht war, ignorieren Sie bitte diese E-Mail. <br/><br/><PERSON><PERSON>,<br/>FunBlocks-Team", "verification_vcode_subject": "Verifizierungscode von <PERSON>s", "verification_vcode_text": "<PERSON>ber {{account}},\n<PERSON><PERSON><PERSON>, dass Sie unseren Service gewählt haben. Um die Sicherheit Ihres Kontos zu gewährleisten, müssen wir Ihre Identität überprüfen.\nIhr Verifizierungscode lautet: {{token}}. \nBitte geben Sie den Code innerhalb von 24 Stunden ein, um den Verifizierungsprozess abzuschließen. Bitte beachten Sie, dass dieser Code nur für diesen Verifizierungsprozess verwendet werden kann und nicht wiederverwendet werden kann.\nWenn Sie diese E-Mail erhalten haben, ohne eine Aktion durchzuführen, ignorieren Sie sie bitte. Wenn Sie Fragen haben oder Unterstützung benötigen, wenden Sie sich bitte an unser Kundenserviceteam.\nVielen Dank, dass Sie sich erneut für unseren Service entschieden haben.\n\nMi<PERSON> freundlichen Grüßen,\nFunBlocks-Team", "verified_title": "Willkommen bei FunBlocks", "failed_verify_title": "Überprüfung Ihres FunBlocks-Kontos fehlgeschlagen", "callback": "<PERSON><PERSON><PERSON> zu {{app}}, viel <PERSON>!", "token_not_exists": "Der Verifizierungscode/Link existiert nicht oder ist abgelaufen, bitte gehen Sie zu FunBlock, registrieren Si<PERSON> sich erneut oder senden Sie einen Verifizierungslink", "doc_invitation_email_subject": "{{username}} hat <PERSON>hnen eine FunBlocks-<PERSON>ite von {{org}} geteilt", "doc_invitation_email_text": "<PERSON><PERSON>, \n<PERSON><PERSON> Freund {{username}} hat Ihnen {{pageTitle}} von {{org}} geteilt. Klicken Sie auf den folgenden Link, um die Seite anzuzeigen, {{url}}, oder kopieren Si<PERSON> den Link und öffnen Sie ihn im Browser. \n\nDanke,\nFunBlocks-Team", "doc_invitation_email_html": "<PERSON><PERSON>, <br/><PERSON><PERSON> Freund {{username}} hat Ihnen {{pageTitle}} von {{org}} geteilt. Klicken Sie auf den folgenden Link, um die Seite anzuzeigen, <a href='{{url}}'>anzeigen</a>, <br/>Oder kopieren Sie den folgenden Link und öffnen Sie ihn im Browser: {{url}} <br/><br/><PERSON><PERSON>,<br/>FunBlocks-Team", "doc_invitation_sms_text": "<PERSON>hr Freund {{username}} hat Ihnen {{pageTitle}} von {{org}} geteilt. Öffnen Sie es: ", "org_invitation_email_subject": "{{username}} l<PERSON><PERSON>, dem FunBlocks-Raum {{org}} beizutreten", "org_invitation_email_text": "<PERSON><PERSON>, \n<PERSON><PERSON> Freund {{username}} l<PERSON><PERSON>, dem FunBlocks-<PERSON><PERSON> {{org}} beizutreten. Klicken Sie auf den folgenden Link, um beizutreten, {{url}}, oder kopieren Sie den Link und öffnen Sie ihn im Browser. \n\nDanke,\nFunBlocks-Team", "org_invitation_email_html": "<PERSON><PERSON>, <br/><PERSON><PERSON> Freund {{username}} lädt <PERSON>, dem FunBlocks-Raum {{org}} beizutreten. Klicken Sie auf den folgenden Link, um beizutreten, <a href='{{url}}'>{{org}}</a>, <br/>Oder kopieren Sie den folgenden Link und öffnen Sie ihn im Browser: {{url}} <br/><br/><PERSON><PERSON>,<br/>FunBlocks-Team", "org_invitation_sms_text": "<PERSON>hr Freund {{username}} lä<PERSON>, dem FunBlocks-Raum {{org}} beizutreten. Öffnen Sie es: ", "invite_friends_desc": "Te<PERSON><PERSON> Sie Freude mit Freunden. Laden Sie Ihre Freunde e<PERSON>, FunBlocks AI zu nutzen, und genießen Sie gemeinsam die effiziente Unterstützung von FunBlocks AI! \n\n<PERSON><PERSON><PERSON> jeden <PERSON>, den <PERSON>, erhalten Sie {{coins}} kostenlose FunBlocks AI-Token! Ihr AI-Token-Guthaben beträgt {{balance}}", "invite_friends_msg_extension": "Ich habe gerade eine super coole Browsererweiterung namens FunBlocks AI gefunden. <PERSON><PERSON> kann <PERSON> he<PERSON>, <PERSON><PERSON><PERSON><PERSON> zu<PERSON>mme<PERSON>uf<PERSON>, <PERSON><PERSON> zu optimieren, Grammatikfehler zu korrigieren, E-Mails mit einem Klick zu beantworten und verschiedene Arten von Inhalten zu schreiben! Definitiv einen Versuch wert!\nKlicken Sie jetzt auf den folgenden Link, um {{coins}} kostenlose AI-Abfragen zu erhalten", "invite_friends_msg_flow": "Verabschieden Sie sich von langweiligem konversationalem KI! FunBlocks AIFlow macht die Interaktion mit KI intuitiver mit Fluss-Denkblasen! 🧠\n\n💡 Entwickeln Sie Ihre Ideen frei und verbinden Sie Ihre Gedanken mühelos. \n💪 Angetrieben von führenden LLM-Modellen, bewältigt es Informationsabruf, Inhaltserstellung und Problemlösung mit Leichtigkeit. \n🚀 <PERSON><PERSON> den Effizienzschub, egal wie komplex das Projekt ist.\n\nTreten Sie jetzt bei, um {{coins}} kostenlose AI-Abfragen zu erhalten! 😉", "invite_friends_msg_app": "Ich habe gerade ein super cooles <PERSON><PERSON>, FunBlocks AI, das Dokumente und PPTs mit einem Klick erstellen, Kompositionen ändern und optimieren, Geschichten fortsetzen, Gliederungen erstellen und Brainstorming durchführen kann! Es ist einen Versuch wert! \nTreten Sie jetzt bei, um {{coins}} kostenlose AI-Abfragen zu erhalten"}, "ai_items": {"review_group": "Auswahl bearbeiten oder überprüfen", "generate_group": "Aus gegebenem Text generieren", "write_group": "Mit KI schreiben", "draft_group": "Mit KI entwerfen", "slides_group": "Aus gegebenem Text für Folien generieren", "workspace_prompts_group": "@Workspace-Aufforderungen", "pinned_prompts_group": "Fixierte Aufforderungen", "user_prompts_group": "Selbst definierte Aufforderungen", "add_prompt": "KI-Aufforderung hinzufügen", "writing_tutor": "Schreibmentor", "writing_teacher": "Bewertung und Feedback zu Aufsätzen", "reflect": "Reflexion", "memo_maestro": "<PERSON><PERSON> s<PERSON>re<PERSON>n", "memo_maestro_image": "<PERSON><PERSON> s<PERSON>re<PERSON>n", "story_image": "Eine Geschichte erzählen", "image_caption": "Bildunterschrift generieren", "central_ideas": "Zentrale Ideen", "query": "Als Beratungsinhalt an den KI-Assistenten senden", "optimize": "Lesbarkeit verbessern", "rewrite": "Aufsatz umschreiben", "spelling": "Rechtschreibung und Grammatik korrigieren", "translate_to_en": "Ins Englische übersetzen oder polieren", "continue": "<PERSON><PERSON> schreiben", "extend": "<PERSON><PERSON><PERSON> machen", "short": "<PERSON><PERSON><PERSON><PERSON> machen", "tone": "<PERSON><PERSON>", "summary_text": "Zusammenfassen", "summary_keypoints": "Zusammenfassung und Schlüsselpunkte", "title": "Titel", "slide": "Einseitige Folie", "slideshow": "Mehrseitige Präsentation", "speechscript": "Rede-Skript", "todos": "Aktionspunkte finden", "flow_todolist": "To-Do-Liste generieren", "flow_infograph": "Infografik generieren", "flow_flowchart": "Flussdiagramm generieren", "flow_task_analysis": "Aufgabenanalyse", "cornell_notes": "Cornell-Notizen generieren", "flow_image_prompt": "<PERSON><PERSON><PERSON> gene<PERSON>", "generate_image": "<PERSON><PERSON><PERSON> gene<PERSON>", "summary": "Zusammenfassen", "bullet": "Gliederung", "keypoints": "Schlüsselpunkte", "speakernotes": "Sprechernotizen", "speech_suggestions": "Präsentationstipps", "slides_optimize": "Folienoptimierungen", "highlights": "Höhepunkte", "share_tips": "Text für soziale Medien teilen", "ask_questions": "Relevante Fragen stellen", "related_questions_topics": "Relevante Fragen und Themen", "image_insights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breakdown": "Themen/<PERSON><PERSON><PERSON> au<PERSON>n", "proof": "<PERSON><PERSON><PERSON>", "positive_cases": "Positive Fälle", "negative_cases": "Negative Fälle", "contradict": "Widersprechen", "fallacy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "related_knowledges": "Wie man verwandtes Wissen erlernt", "simplify": "In einfacher Sprache umschreiben", "topicspeech": "Vorlesung", "explain": "Erklären", "draft": "Schreiben Sie ein ...", "brainstorming": "Brainstorming", "flow_brainstorming": "Brainstorming", "flow_mentalmodel": "<PERSON><PERSON>", "flow_subtopic_brainstorming": "<PERSON><PERSON><PERSON> erwei<PERSON>", "flow_mindmap": "Mindmap generieren", "flow_book_mindmap": "Mindmap generieren", "flow_movie_mindmap": "Mindmap generieren", "flow_decision_analysis": "Entscheidungsanalyse", "flow_read_mindmap": "<PERSON>uch lesen", "flow_dream_analysis": "Traumanalyse", "flow_art_insight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flow_art_image_insight": "Kunstwertschätzung", "flow_photography_coach": "Fotografie-Coach", "draft_prompt": "Schreiben Sie ein {content_type} über: ", "reply": "Antwort schreiben", "email_outline": "Senderabsicht", "message_outline": "Nachrichtenabsicht", "reply_email": "Antwort-E-Mail verfassen", "quora_reply": "Quora-<PERSON><PERSON>", "twitter_reply": "Antwort verfassen", "social_post_reply": "Kommentar verfassen", "producthunt_voter_comment": "Kommentar verfassen", "producthunt_maker_reply": "Antwort verfassen", "read_email": "E-Mail lesen", "outline": "Gliederung", "reply_outline": "Antwortgliederung oder Schlüsselpunkte", "refine_question": "<PERSON><PERSON><PERSON><PERSON><PERSON> verfassen", "explain_codes": "Erklären", "critial_analysis": "Kritische Analyse", "describe_image": "Bildinhalt beschreiben", "describe_image_mindmap": "Bild mit Mindmap erkunden", "translate_image": "Übersetzen", "homework": "<PERSON><PERSON>", "image_empathetic_reply": "Empathische Antwort generieren", "witty_insights": "WonderLens - Witziger Kommentar", "image_avatar": "Stiltransfer", "xSlides": "Folien erstellen", "fix_codes_bug": "Codeprobleme automatisch beheben", "improve_codes": "Mit KI ändern oder verbessern", "lesson_plans": "Lektionenplan generieren", "dok_assessment": "DOK-Assessment generieren", "teaching_slides": "Lehrfolien generieren", "tone_professional": "Professionell", "tone_casual": "Lässig", "tone_straightforward": "Direkt", "tone_confident": "Selbstbewusst", "tone_friendly": "<PERSON><PERSON><PERSON><PERSON>", "tone_academic": "Akademisch", "tone_enthusiastic": "Begeistert", "tone_empathetic": "Empathisch", "chart": "Flussdiagramm, Folie, Infografiken ...", "chart_infographics": "Infografiken", "chart_flowchart": "Flussdiagramm", "chart_sequencediagram": "Sequenzdiagramm", "chart_quadrant": "Quadrantendiagramm", "chart_timeline": "Zeitleiste", "chart_cornell_notes": "Cornell-Notizen", "chart_slide": "Einseitige Folie", "expand_ideas": "<PERSON><PERSON><PERSON> erwei<PERSON>", "expand_ideas_brainstorm": "Brainstorming", "expand_ideas_breakdown": "Aufschlüsselung", "expand_ideas_first_principle": "<PERSON><PERSON><PERSON>", "expand_ideas_five_whys": "Ursachenanalyse (5 Warum)", "expand_ideas_scamper": "Kreatives Denken (SCAMPER)", "expand_ideas_problem_rephrazing": "Problemumformulierung", "expand_ideas_changing_perspectives": "Perspektivwechsel", "expand_ideas_reverse_thinking": "Umgekehrtes Denken", "expand_ideas_pros_cons": "Vor- und Nachteile", "expand_ideas_5w1h": "5W1H", "expand_ideas_role_storming": "Rollen-Sturm", "expand_ideas_triz": "Erfinderisches Problemlösen (TRIZ)", "expand_ideas_six_thinking_hats": "Sechs Denkhüte", "expand_ideas_disney_method": "Disney-<PERSON><PERSON>", "expand_ideas_swots": "SWOT-Analyse", "expand_ideas_value_proposition_canvas": "Value Proposition Canvas", "expand_ideas_bussiness_model_canvas": "Business Model Canvas", "translate": "Übersetzen nach ...", "translate_english": "<PERSON><PERSON><PERSON>", "translate_japanese": "Japanisch", "translate_french": "Franzö<PERSON><PERSON>", "translate_spanish": "Spanisch", "translate_latin": "Latein", "translate_italian": "Italienisch", "translate_russian": "<PERSON><PERSON>", "translate_portuguese": "Portugiesisch", "translate_korean": "Koreanisch", "translate_arabic": "Arabisch", "translate_hebrew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "translate_dutch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translate_german": "De<PERSON>ch", "translate_indonesian": "Indonesisch", "translate_danish": "D<PERSON><PERSON><PERSON>", "translate_swedish": "Schwedisch", "translate_finnish": "Finnisch", "translate_turkish": "Türkisch", "translate_polish": "Polnisch", "translate_hungarian": "Ungarisch", "translate_czech": "Tschechisch", "translate_romanian": "Rumänisch", "translate_bulgarian": "Bulgarisch", "translate_greek": "Griechisch", "translate_thai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "translate_vietnamese": "Vietnamesisch", "translate_malay": "Malaiisch", "translate_traditional_chinese": "Traditionelles Chinesisch", "translate_mandarin_chinese": "Vereinfacht Chinesisch", "draft_brainstorming": "Brainstorming", "draft_outline": "Gliederung", "draft_article": "Artikel", "draft_blog": "Blog", "draft_question": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draft_twitter": "Tweet", "draft_twitter_reply": "Tweet-Antwort", "draft_weibo": "Weibo", "draft_xiaohongshu": "Xiaohongshu", "draft_zhihu": "Quora-Antwort", "draft_wechat_post": "WeChat Moments Sharing Caption", "draft_facebook": "Facebook Timeline Post", "draft_press_release": "Pressemitteilung", "draft_script": "Drehbuch", "draft_creative_story": "Kreative Geschichte", "draft_presentation": "Rede-Skript", "draft_essay": "Aufsatz", "draft_poem": "<PERSON><PERSON><PERSON>", "draft_love_letter": "Liebesbrief", "draft_swot": "SWOT-Analyse", "draft_weekly_report": "Wöchentlicher Bericht", "draft_cons_pros": "Vor- und Nachteile", "draft_job_description": "Stellenbeschreibung", "draft_sales_email": "Verkaufs-E-Mail", "draft_more": "...", "input": "..."}, "sub_task": {"label": "<PERSON><PERSON><PERSON> {{action_label}} mit KI"}, "sub_task_titles": {"email_outline": "E-Mail-Inhalt", "message_outline": "Nachrichteninhalt"}, "label": {"topic": "<PERSON>a", "outline": "Schlüsselpunkte oder Gliederung", "email_outline": "Senderabsicht", "message_outline": "Senderabsicht", "reply_outline": "Antwortgliederung oder Schlüsselpunkte", "other_reqs": "Andere Anforderungen", "target_learners": "Zielgruppe", "duration": "<PERSON><PERSON>", "educational_model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "art_style": "Kunststil"}, "hint": {"topic": "<PERSON>a", "outline": "Schlüsselpunkte oder Gliederung", "reply_outline": "Gliederung oder Schlüsselpunkte für die Antwort", "other_reqs": "<PERSON>ere Anforderungen, wie <PERSON><PERSON>til, <PERSON><PERSON><PERSON><PERSON> usw.", "target_learners": "Z.<PERSON><PERSON> <PERSON>. <PERSON><PERSON><PERSON>, <PERSON>, Berufstätige usw.", "duration": "Z.B. 45 Minuten, 90 Minuten, 120 Minuten usw."}, "ai_roles": {"doctor": "Arz<PERSON>", "professor": "Professor", "philosopher": "<PERSON><PERSON><PERSON>", "psychologist": "Psychologe", "relationship_coach": "Beziehungscoach", "scientist": "Wissenschaftler", "historian": "Historike<PERSON>", "motivational_coach": "Motivationscoach", "fortune_teller": "Hell<PERSON>her", "astrologer": "Astrologe", "legal_advisor": "Rechtsberater", "fallacy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debater": "<PERSON><PERSON><PERSON><PERSON>", "storyteller": "Geschichtenerzähler", "novelist": "<PERSON><PERSON><PERSON>", "poet": "<PERSON><PERSON><PERSON>", "essay_writer": "Aufsatzschreiber", "social_writer": "Social Media Texter", "question_answer": "Quora-Antwort", "question_writer": "Frage-Autor", "motivational_speaker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elocutionist": "<PERSON><PERSON>", "business_analyst": "Business-Analyst", "it_expert": "IT-Experte", "spoken_english_teacher": "<PERSON><PERSON>sch<PERSON><PERSON><PERSON>", "screen_writer": "Drehbuchautor", "career_counselor": "Karriereberater", "personal_trainer": "Personal Trainer", "mental_health_adviser": "Berater für psychische Gesundheit", "academician": "Akademiker", "time_travel_guide": "Zeitreiseleiter", "confucius": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lao_tzu": "<PERSON><PERSON>", "zhuangzi": "<PERSON><PERSON><PERSON>", "sun_tzu": "<PERSON><PERSON>", "simaqian": "<PERSON><PERSON>", "sushi": "<PERSON>", "libai": "<PERSON>", "wangyangming": "<PERSON>", "buddha": "<PERSON>", "jesus": "<PERSON>", "socrates": "Sokrates", "aristotle": "Aristoteles", "plato": "Platon", "nietzsche": "<PERSON>", "sartre": "<PERSON><PERSON><PERSON>", "freud": "<PERSON><PERSON><PERSON>"}, "ai": {"exceed_msg_limit": "<PERSON>s sieht so aus, als hätten Sie Ihr tägliches Kontingent an kostenlosen Anfragen beim KI-Assistenten aufgebraucht. Um unsere Dienste weiterhin nutzen zu können, können Si<PERSON>:\n\n1. Eine FunBlocks AI-Mitgliedschaft für unbegrenzten Zugriff erwerben.\n2. Ihre eigenen LLM-APIs einrichten, um Drittanbieter-LLM-Dienste zu nutzen.\n3. <PERSON><PERSON> wiederkommen für 20 weitere kostenlose Anfragen.\n\nVielen Dank für Ihre Unterstützung!", "text_too_long": "Text überschreitet das Längenlimit", "no_reply": "Fehler beim Abrufen der Ergebnisse, bitte versuchen Sie es später erneut!", "exceed_askai_speed_limit": "Die Antwortgeschwindigkeit des KI-Assistenten wurde überschritten, bitte versuchen Sie es später erneut", "ai_request_timeout": "Keine Antwort vom KI-Assistenten, bitte versuchen Sie es später erneut", "content_filter": "Inhalt enthält sensible Informationen, gefiltert", "llm_service_failed": "KI-LLM-<PERSON>nst feh<PERSON>lagen, bitte versuchen Sie es später erneut", "welcome_msg": "Willkommen bei unser<PERSON> Cha<PERSON>, der von einem großen Sprachmodell unterstützt wird! Wir freuen uns, dass Sie hier sind. Als Sprachmodell sind wir auf Aufgaben der natürlichen Sprachverarbeitung spezialisiert, einschließlich Sprachgenerierung, Übersetzung und Beantwortung von Fragen. Wir können Ihre Fragen zu einer Vielzahl von Themen beant<PERSON>, einschließlich Geschichte, Kultur, Wissenschaft, Technologie und Literatur. Es ist jedoch wichtig zu beachten, dass unsere Antworten möglicherweise nicht immer genau sind und wir keine Echtzeit-Nachrichtenaktualisierungen bereitstellen können.\n\nZum Beispiel könnten Sie uns fragen: 'Was ist die Hauptstadt von Frankreich?' und wir können unser Sprachverständnis und unsere Wissensbasis nutzen, um eine Antwort zu geben. Abe<PERSON> z<PERSON>gern <PERSON> nicht, uns spezifischere Fragen zu stellen oder Ihre Frage umzuformulieren, wenn unsere Antwort nicht hilfreich ist. Wir sind hier, um Ihnen bestmöglich zu helfen!", "coins": "AI-Token können verwendet werden, um auf KI-Assistenten zuzugreifen, und ein AI-Token wird pro KI-Aufgabe verbraucht. Sie können AI-Token kaufen oder Sie können AI-Token kostenlos durch Aktivitäten wie 'Freunde einladen' erhalten.", "coin": "AI-Token", "balance": "Sie haben derzeit {{balance}} AI-Token.", "buy_coin_title": "<PERSON><PERSON> können Sie AI-Token kaufen:", "earn_coin_desc": "Oder Sie können AI-Token-Belohnungen erhalten, indem Sie Freunde einladen. Eine erfolgreiche Registrierung belohnt {{coins}} AI-Token:", "exceed_daily_quota": "Sie sind FunBlocks AI {{level}} Mitglied, und Ihr {{model_level}} Anfragekontingent für heute wurde aufgebraucht. Bitte upgraden Sie Ihren Plan oder wählen Sie ein anderes Modell, um fortzufahren."}, "insights": {"exceed_daily_quota": "Sie haben Ihr tägliches Generierungskontingent aufgebraucht. Bitte kommen Sie morgen wieder oder upgraden Sie Ihren Plan, um fortzufahren.", "exceed_free_trial_quota": "Ihr kostenloses Testkontingent wurde aufgebraucht. Bitte upgraden Sie auf ein Mitglied, um fortzufahren.", "ai_reponse_nothing": "Die KI hat keine vollständige Antwort gegeben, bitte versuchen Sie es später erneut", "failed_load_image": "Bild konnte nicht geladen werden", "invalid_input": "Ungültige Eingabe"}, "llm": {"gemini-2.5-pro": "Das neueste und leistungsstärkste Modell von Google, mit starken Argumentations- und multimodalen Fähigkeiten, das in der Lage ist, Bilder zu analysieren.", "gemini-2.0-pro": "Das Modell von Google mit starken Argumentations- und multimodalen Fähigkeiten, fähig zur Bildanalyse.", "gemini-1.5-pro": "Das Modell von Google mit starken Argumentations- und multimodalen Fähigkeiten, fähig zur Bildanalyse.", "gpt-4o": "Das GPT-4.1-<PERSON><PERSON> von OpenAI, das über eine umfangreiche Wissensbasis und hervorragendes Sprachverständnis verfügt.", "llama-3.1-70b-versatile": "Das Open-Source-<PERSON><PERSON>, bekannt für seine schnelle Generierungsgeschwindigkeit.", "gemini-thinking": "Google Gemini Thinking Model, das sich hervorragend für das Argumentieren durch komplexe Probleme und deren Lösung eignet.", "gemini-2.5-flash": "Neueste experimentelle schnelle Argumentationsversion von Google Gemini 2.5, bietet schnellere Generierungsgeschwindigkeit und Bildanalyse, unterstützt die Websuche", "gemini-2.0-flash": "Neueste schnelle Argumentationsversion von Google Gemini 2.0, bietet schnellere Generierungsgeschwindigkeit und Bildanalyse, unterstützt die Websuche", "gemini-1.5-flash": "Eine schnelle Argumentationsversion von Google Gemini, die schnellere Generierungsgeschwindigkeit und Bildanalyse bietet.", "gpt-4o-mini": "Eine leichte Version von GPT-4.1, die Geschwindigkeit und Leistung ausbalanciert.", "claude-sonnet": "Das Claude Sonnet 4-<PERSON><PERSON>, das in der Lage ist, komplexe Probleme zu lösen und Bilder zu analysieren.", "claude-3-haiku": "Das Claude 3 Haiku-<PERSON><PERSON>, geeignet für allgemeine komplexe Probleme, schnelle Generierungsgeschwindigkeit, Unterstützung für langen Kontext und Bildanalyse.", "llama3-70b-8192": "Das Llama 3-<PERSON><PERSON>, das schnelle Generierungsgeschwindigkeit bietet.", "llama-3.2-90b-vision-preview": "Das Open-Source-<PERSON><PERSON>, das schnelle Generierungsgeschwindigkeit und Bildanalyse bietet.", "llama-3.2-11b-vision-preview": "Das Open-Source-<PERSON><PERSON>, das schnelle Generierungsgeschwindigkeit und Bildanalyse bietet.", "deepseek-v3": "Open-Source DeepSeek-V3-<PERSON><PERSON>, schnelle Generierungsgeschwindigkeit, mit starken generativen Fähigkeiten", "deepseek-r1": "Open-Source DeepSeek-R1-<PERSON><PERSON><PERSON><PERSON><PERSON>dell, hervorragend im Argumentieren und Lösen komplexer Probleme"}, "privileges": {"rils": "Anzahl der für ReadItLater gespeicherten Artikel", "rilsRead": "Gelesene ReadItLater-Artikel verwalten", "rilsTrash": "Gelöschte ReadItLater-Artikel verwalten", "notesTrash": "Gelöschte Sofortnotizen verwalten", "media_count": "Anzahl der hochgeladenen Bilder", "tts_voices": "Premium Text-to-Speech-Stimmen", "mediaCollection": "Hochgeladene Medienkollektion verwalten", "blocks": "An<PERSON><PERSON> der Blöcke", "members": "Maximale Anzahl von Benutzern im Workspace", "askAI": "Anzahl der Anfragen an den FunBlocks AI-Assistenten", "usePrivateAIApi": "Unterstützung für den direkten Zugriff auf LLM-APIs", "privatePrompts": "Anzahl der privaten Aufforderungen", "limited_ai_features": "Eingeschränkte KI-Funktionen", "generateCard": "<PERSON><PERSON> gene<PERSON>", "memos": "Memos", "free_trial": "Kostenlose Testversion", "true": "<PERSON>a", "false": "<PERSON><PERSON>", "unlimited": "Unbegrenzt", "aicoins": "30 <PERSON> testen", "daily_quota": "Tägliches Kontingent", "notes": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> von den Blocknotizen und KI-Funktionen sind die Hauptfunktionen von FunBlocks derzeit kostenlos zu nutzen;\n2. <PERSON><PERSON> <PERSON>hnen die KI-Funktionen gefallen, wird em<PERSON><PERSON>, die KI-VIP-Mitgliedschaft direkt zu erwerben."}, "uninstall_reasons": {"no_required_feature": "Die Erweiterung hat bestimmte Funktionen, die ich benötige, nicht.", "have_better_choice": "Ich habe eine andere App oder einen Dienst gefunden, der besser zu meinen Bedürfnissen passt.", "encounter_issues": "Ich hatte Probleme oder Schwierigkeiten bei der Nutzung der Erweiterung.", "do_not_like": "Ich bin mit der Benutzererfahrung oder dem Interface-Design der Erweiterung nicht zufrieden.", "no_requirement": "Nachdem ich es ausprobiert habe, habe ich keine Funktionen gefunden, die mich dazu bringen würden, es weiter zu nutzen.", "others": "<PERSON>ine der oben genannten Optionen deckt meinen Deinstallationsgrund ab."}, "uninstall_reasons_detail_hint": {"no_required_feature": "<PERSON>te beschreiben Sie die Funktionen, die Sie am meisten gewünscht haben.", "have_better_choice": "Bitte listen Sie die alternativen Produkte oder Lösungen auf, die Sie derzeit verwenden.", "encounter_issues": "Bitte beschreiben Sie die Probleme oder Schwierigkeiten, die Sie bei der Nutzung der Erweiterung hatten.", "do_not_like": "Bitte listen Sie Bereiche auf, in denen Sie die Benutzererfahrung oder das Interface-Design als unzureichend empfinden.", "no_requirement": "<PERSON><PERSON><PERSON>ten Si<PERSON> bitte die Funktionen beschreiben, die Si<PERSON> weiterhin nutzen würden?", "others": "Bitte listen Sie andere spezifische Gründe für Ihre Deinstallation auf."}}