{"phone": "phone", "email": "email", "somebody_workspace": "{{nickname}}'s workspace", "somebody_writingspace": "{{nickname}}'s space", "powered_by": "Powered by", "db_new_property_name": "Name", "db_new_property_status": "Status", "db_new_property_createdAt": "Created at", "db_new_property_url": "URL", "db_new_property_pin": "Top", "default_status_name": "Status", "default_date_name": "Date", "db_view_table": "Table", "db_view_board": "Board", "db_view_list": "List", "db_view_gallery": "Gallery", "db_view_timeline": "Timeline", "db_view_chart": "Chart", "db_new_link_to_view": "View links", "db_property_default_label_prefix": "Property ", "untitled_new_db": "New Database", "untitled_new_slides": "New Slides", "untitled_new_flow": "New AIFlow", "untitled_new_doc": "New Page", "untitled_db": "Untitled Database", "untitled_slides": "Untitled Slides", "untitled_flow": "Untitled AIFlow", "untitled_doc": "Untitled Page", "todo": "Todo", "doing": "Doing", "done": "Done", "seminar_powered_by": "This seminar is powered by FunBlocks", "present_powered_by": "This slides is powered by FunBlocks AI", "seminar_slides_slogan": "@FunBlocks — AI-Enhanced Slide Creation, Markdown-Smooth Editing", "present_slides_slogan": "@FunBlocks — AI-Enhanced Slide Creation, Markdown-Smooth Editing", "api_level_1": "Advanced AI model", "api_level_2": "Standard AI model", "quota_model_level_1": "Daily access limit for advanced AI model", "quota_model_level_2": "Daily access limit for standard AI model", "quota_whiteboards": "Number of whiteboards that can be created", "quota_flow_nodes": "Number of nodes that can be saved in each whiteboard", "message": {"user_not_exist": "User does not exist or wrong password", "user_existed": "User already exist", "verification_code_error": "Verification code error or expired", "oauth_user_unmatched": "User information mismatch during authentication", "too_frequent": "operation too frequent", "verification_code_sent": "Verification code sent to your ", "sms_daily_limit": "SMS daily limit reached", "sms_service_error": "SMS service error", "activation_email_sent": "Sending activation message to your email address, you can check and activate it later", "keep_permission_to_operator": "Keep all permissions to yourself", "same_version": "Same version", "version_not_found": "Version not found", "org_not_exist_or_no_access": "Organization does not exist or you have no access", "content_no_access": "No permission to access this content, please log in to FunBlocks or contact the content owner", "operation_failed": "Operation failed, please try again later", "operation_success": "Operation Success", "server_error": "Server error", "recaptcha_verification_failed": "ReCAPTCHA verification failed"}, "notification": {"verification_email_subject": "Verify your email to FunBlocks", "verification_email_text": "Hello, \n\n{{account}} has just been registered to FunBlocks. Click following link to activate/verify your email to FunBlocks, {{activate_url}} , or copy the link and open it in browser. \nIf it's not your intention, please ignore this email. \n\nThanks,\nFunBlocks Team", "verification_email_html": "Hello, <br/><br/>{{account}} has just been registered to FunBlocks. Click following link to activate/verify your email to FunBlocks, <a href='{{activate_url}}'>activate</a>, <br/>Or copy following link and open in browser: {{activate_url}} <br/>If it's not your intention, please ignore this email. <br/><br/>Thanks,<br/>FunBlocks Team", "verification_vcode_subject": "Verification code from FunBlocks", "verification_vcode_text": "Dear {{account}},\nThank you for choosing our service. To ensure the security of your account, we need to verify your identity.\nYour verification code is: {{token}}. \nPlease enter the code within 24 hours to complete the verification process. Please note that this code can only be used for this verification process and cannot be reused.\nIf you have received this email without taking any action, please disregard it. If you have any questions or need any assistance, please feel free to contact our customer service team.\nThank you again for choosing our service.\n\nBest regards,\nFunBlocks team", "verified_title": "Welcome to FunBlocks", "failed_verify_title": "Failed to verify your FunBlocks account", "callback": "Go to {{app}}, have fun！", "token_not_exists": "The verification code/link does not exist or has expired, please go to FunBlock, register again or send a verification link", "doc_invitation_email_subject": "{{username}} shared you a FunBlocks page from {{org}}", "doc_invitation_email_text": "Hello, \nYour friend {{username}} shared you to {{pageTitle}} from {{org}}. Click following link to view the page, {{url}} , or copy the link and open it in browser. \n\nThanks,\nFunBlocks Team", "doc_invitation_email_html": "Hello, <br/>Your friend {{username}} shared you to {{pageTitle}} from {{org}}. Click following link to view the page, <a href='{{url}}'>view</a>, <br/>Or copy following link and open in browser: {{url}} <br/><br/>Thanks,<br/>FunBlocks Team", "doc_invitation_sms_text": "Your friend {{username}} shared you to {{pageTitle}} from {{org}}. open it: ", "org_invitation_email_subject": "{{username}} invite you to join FunBlocks space {{org}}", "org_invitation_email_text": "Hello, \nYour friend {{username}} invite you to join FunBlocks space {{org}}. Click following link to join, {{url}}, or copy the link and open it in browser. \n\nThanks,\nFunBlocks Team", "org_invitation_email_html": "Hello, <br/>Your friend {{username}} invite you to join FunBlocks space {{org}}. Click following link to join, <a href='{{url}}'>{{org}}</a>, <br/>Or copy following link and open in browser: {{url}} <br/><br/>Thanks,<br/>FunBlocks Team", "org_invitation_sms_text": "Your friend {{username}} invite you to join FunBlocks space {{org}}. open it: ", "invite_friends_desc": "Share joy with friends. Invite your friends to use FunBlocks AI, and enjoy the efficient assistance brought by FunBlocks AI together! \n\nFor each friend you invite, you'll get {{coins}} free FunBlocks AI tokens! Your AI tokens balance is {{balance}}", "invite_friends_msg_extension": "I just found a super cool browser extension called FunBlocks AI. It can help you summarize web content, polish text, correct grammar errors, reply to emails with one click, and write various types of content! Definitely worth a try!\nClick following link now to get {{coins}} free AI queries", "invite_friends_msg_flow": "Say goodbye to boring conversational AI! FunBlocks AIFlow makes interacting with AI more intuitive with flow mind maps! 🧠\n\n💡 Freely expand your ideas and effortlessly connect your thoughts. \n💪 Powered by leading LLM models, it handles information retrieval, content generation, and problem-solving with ease. \n🚀 See the boost in efficiency, no matter how complex the project.\n\nJoin now to have {{coins}} free AI queries! 😉", "invite_friends_msg_app": "I just discovered a super cool tool, FunBlocks AI, which can generate documents and PPTs with one click, modify and optimize compositions, continue writing stories, generate outlines and conduct brainstorming, etc.! Worth a try! \nJoin now to have {{coins}} free AI queries"}, "ai_items": {"review_group": "Edit or review selection", "generate_group": "Generate from given text", "write_group": "Write with AI", "draft_group": "Draft with AI", "slides_group": "Generate from given text for Slides", "workspace_prompts_group": "@workspace Prompts", "pinned_prompts_group": "Pinned prompts", "user_prompts_group": "Self defined prompts", "add_prompt": "Add AI prompt", "writing_tutor": "Writing mentor", "writing_teacher": "Grading and feedback on essays", "reflect": "Reflection", "memo_maestro": "Write a Memo", "memo_maestro_image": "Write a Memo", "story_image": "Tell a story", "image_caption": "Generate image caption", "central_ideas": "Central ideas", "query": "Send as a consulting content to AI assistant", "optimize": "Improve readability", "rewrite": "Rewrite essay", "spelling": "Fix spelling & grammar", "translate_to_en": "Translate or polish to English", "continue": "Continue writing", "extend": "Make longer", "short": "Make shorter", "tone": "Change tone", "summary_text": "Summarize", "summary_keypoints": "Summary and keypoints", "title": "Title", "slide": "One page slide", "slideshow": "Multi-page slideshow", "speechscript": "Speech script", "todos": "Find action items", "flow_todolist": "Generate To-Do List", "flow_infograph": "Generate Infograph", "flow_flowchart": "Generate FlowChart", "flow_task_analysis": "TaskMaster analysis", "cornell_notes": "Generate Cornell Notes", "summary": "Summarize", "flow_image_prompt": "Generate image", "generate_image": "Generate image", "bullet": "Outline", "keypoints": "Key points", "speakernotes": "Speaker notes", "speech_suggestions": "Presentation suggestions", "slides_optimize": "Slides optimizations", "highlights": "Highlights", "share_tips": "Write a social media sharing text", "ask_questions": "Ask relevant questions", "related_questions_topics": "Relevant questions and topics", "image_insights": "Image insights", "breakdown": "Breakdown topics/ideas", "proof": "Proof", "positive_cases": "Positive cases", "negative_cases": "Negative cases", "contradict": "Contradict", "fallacy": "Fallacy", "related_knowledges": "How to learn related knowledge", "simplify": "Rewrite in simpler language", "topicspeech": "Lecture", "explain": "Explain", "draft": "Write an ...", "brainstorming": "Brainstorming", "flow_brainstorming": "Brainstorming", "flow_mentalmodel": "Mental model", "flow_subtopic_brainstorming": "Expand ideas", "flow_mindmap": "Generate a mind map", "flow_book_mindmap": "Generate a mind map", "flow_movie_mindmap": "Generate a mind map", "flow_decision_analysis": "Decision analysis", "flow_read_mindmap": "Book reading", "flow_dream_analysis": "Dream analysis", "flow_art_insight": "Art insights", "flow_art_image_insight": "Art appreciation", "flow_photography_coach": "Photography coach", "draft_prompt": "Write a {content_type} about: ", "reply": "Write a reply", "email_outline": "Sender intention", "message_outline": "Message intention", "reply_email": "Compose a reply email", "quora_reply": "Answer quora question", "twitter_reply": "Compose a reply", "social_post_reply": "Compose a comment", "producthunt_voter_comment": "Compose a comment", "producthunt_maker_reply": "Compose a reply", "read_email": "Read Email", "outline": "Outline", "reply_outline": "Reply outline or key points", "refine_question": "Write a help inquiry post", "explain_codes": "Explain", "critial_analysis": "Critical analysis", "describe_image": "Describe image content", "describe_image_mindmap": "Exploare image with mind map", "translate_image": "Translate", "homework": "Solve this homework problem", "image_empathetic_reply": "Generate empathetic reply", "witty_insights": "WonderLens - Witty Commentary", "image_avatar": "Style Transfer", "xSlides": "Generate slides", "fix_codes_bug": "Auto-fix Code Issues", "improve_codes": "Modify or Improve with AI", "lesson_plans": "Generate lesson plans", "dok_assessment": "Generate DOK assessment", "teaching_slides": "Generate teaching slides", "tone_professional": "Professional", "tone_casual": "Casual", "tone_straightforward": "Straightforward", "tone_confident": "Confident", "tone_friendly": "Friendly", "tone_academic": "Academic", "tone_enthusiastic": "Enthusiastic", "tone_empathetic": "Empathetic", "chart": "<PERSON><PERSON>hart, Slide, Infographics ...", "chart_infographics": "Infographics", "chart_flowchart": "Flowchart", "chart_sequencediagram": "Sequence diagram", "chart_quadrant": "Quadrant chart", "chart_timeline": "Timeline", "chart_cornell_notes": "Cornell notes", "chart_slide": "One page slide", "expand_ideas": "Expand Ideas", "expand_ideas_brainstorm": "Brainstorm", "expand_ideas_breakdown": "Breakdown", "expand_ideas_first_principle": "First Principle", "expand_ideas_five_whys": "Root Cause Analysis (5 Whys)", "expand_ideas_scamper": "Creative Thinking (SCAMPER)", "expand_ideas_problem_rephrazing": "Problem Rephrazing", "expand_ideas_changing_perspectives": "Changing Perspectives", "expand_ideas_reverse_thinking": "Reverse Thinking", "expand_ideas_pros_cons": "Pros & Cons", "expand_ideas_5w1h": "5W1H", "expand_ideas_role_storming": "Role Storming", "expand_ideas_triz": "Inventive Problem Solving (TRIZ)", "expand_ideas_six_thinking_hats": "Six Thinking Hats", "expand_ideas_disney_method": "Disney Method", "expand_ideas_swots": "SWOT Analysis", "expand_ideas_value_proposition_canvas": "Value Proposition Canvas", "expand_ideas_bussiness_model_canvas": "Business Model Canvas", "translate": "Translate to ...", "translate_english": "English", "translate_japanese": "Japanese", "translate_french": "French", "translate_spanish": "Spanish", "translate_latin": "Latin", "translate_italian": "Italian", "translate_russian": "Russian", "translate_portuguese": "Portuguese", "translate_korean": "Korean", "translate_arabic": "Arabic", "translate_hebrew": "Hebrew", "translate_dutch": "Dutch", "translate_german": "German", "translate_indonesian": "Indonesian", "translate_danish": "Danish", "translate_swedish": "Swedish", "translate_finnish": "Finnish", "translate_turkish": "Turkish", "translate_polish": "Polish", "translate_hungarian": "Hungarian", "translate_czech": "Czech", "translate_romanian": "Romanian", "translate_bulgarian": "Bulgarian", "translate_greek": "Greek", "translate_thai": "Thai", "translate_vietnamese": "Vietnamese", "translate_malay": "Malay", "translate_traditional_chinese": "Traditional Chinese", "translate_mandarin_chinese": "Simplified Chinese", "draft_brainstorming": "Brainstorming", "draft_outline": "Outline", "draft_article": "Article", "draft_blog": "Blog", "draft_question": "Help inquiry post", "draft_twitter": "Tweet", "draft_twitter_reply": "Tweet Reply", "draft_weibo": "Weibo", "draft_xiaohongshu": "Xiaohongshu", "draft_zhihu": "<PERSON><PERSON><PERSON> answer", "draft_wechat_post": "WeChat Moments Sharing Caption", "draft_facebook": "Facebook Timeline Post", "draft_press_release": "Press Release", "draft_script": "<PERSON><PERSON><PERSON>", "draft_creative_story": "Creative Story", "draft_presentation": "Speech script", "draft_essay": "Essay", "draft_poem": "Poem", "draft_love_letter": "Love letter", "draft_swot": "SWOT analysis", "draft_weekly_report": "Weekly Report", "draft_cons_pros": "Pros and Cons", "draft_job_description": "Job Description", "draft_sales_email": "Sales Email", "draft_more": "...", "input": "..."}, "sub_task": {"label": "Generate {{action_label}} with AI"}, "sub_task_titles": {"email_outline": "Email content", "message_outline": "Message content"}, "label": {"topic": "Topic", "outline": "Key points or outlines", "email_outline": "Sender intention", "message_outline": "Sender intention", "reply_outline": "Reply outline or key points", "other_reqs": "Other requirements", "target_learners": "Target learners", "duration": "Duration", "educational_model": "Educational model", "art_style": "Art style"}, "hint": {"topic": "Topic", "outline": "Key points or outline", "reply_outline": "Outline or key points for the reply", "other_reqs": "Other requirements, such as language style, word count, etc.", "target_learners": "E.g., 9th grade, college students, working professionals, etc.", "duration": "E.g., 45 mins, 90 mins, 120 mins, etc."}, "ai_roles": {"doctor": "doctor", "professor": "professor", "philosopher": "philosopher", "psychologist": "psychologist", "relationship_coach": "Relationship Coach", "scientist": "Scientist", "historian": "<PERSON><PERSON>an", "motivational_coach": "Motivational Coach", "fortune_teller": "Fortune Teller", "astrologer": "Astrologer", "legal_advisor": "Legal Advisor", "fallacy": "Fallacy", "debater": "<PERSON><PERSON><PERSON>", "storyteller": "Story Teller", "novelist": "Novelist", "poet": "Poet", "essay_writer": "Essay Writer", "social_writer": "Social Media Copywriter", "question_answer": "Quora Answer", "question_writer": "Question Writer", "motivational_speaker": "Motivational Speaker", "elocutionist": "Elocutionist", "business_analyst": "Business Analyst", "it_expert": "It Expert", "spoken_english_teacher": "Spoken English Teacher", "screen_writer": "Screen Writer", "career_counselor": "Career Counselor", "personal_trainer": "Personal Trainer", "mental_health_adviser": "Mental Health Adviser", "academician": "Academician", "time_travel_guide": "Time Travel Guide", "confucius": "<PERSON><PERSON>cius", "lao_tzu": "Lao Tzu", "zhuangzi": "<PERSON><PERSON><PERSON>", "sun_tzu": "Sun Tzu", "simaqian": "<PERSON><PERSON>", "sushi": "<PERSON>", "libai": "<PERSON>", "wangyangming": "<PERSON>", "buddha": "<PERSON>", "jesus": "<PERSON>", "socrates": "Socrates", "aristotle": "<PERSON>", "plato": "<PERSON>", "nietzsche": "<PERSON>", "sartre": "<PERSON><PERSON><PERSON>", "freud": "<PERSON><PERSON><PERSON>"}, "ai": {"exceed_msg_limit": "It looks like you've used up your daily free queries with the AI assistant. To keep using our services, you can:\n\n1. Get a FunBlocks AI membership for unlimited access.\n2. Set up your own LLM APIs to use third-party LLM services.\n3. Come back tomorrow for 20 more free queries.\n\nThank you for your support!", "text_too_long": "Text exceeds length limit", "no_reply": "Failed to get results, please try again later!", "exceed_askai_speed_limit": "Exceeded the answering speed of the AI assistant, please try again later", "ai_request_timeout": "No response from the AI assistant, please try again later", "content_filter": "Content contains sensitive information, filtered", "llm_service_failed": "AI LLM service failed, please try again later", "welcome_msg": "Welcome to our chatbot powered by a large language model! We're glad you're here. As a language model, we excel at natural language processing tasks, including language generation, translation, and question answering. We can answer your questions about a wide range of topics, including history, culture, science, technology, and literature. However, it's important to keep in mind that our responses may not always be accurate, and we cannot provide real-time news updates.\n\nFor example, you could ask us, 'What is the capital of France?' and we can use our language understanding and knowledge base to provide an answer. But please don't hesitate to ask us more specific questions or rephrase your question if our answer is not helpful. We're here to assist you to the best of our abilities!", "coins": "AI tokens can be used to pay for access to AI assistants, and one AI token is consumed per AI task. You can buy AI tokens, or you can get AI tokens for free through activities such as 'Invite friends'.", "coin": "AI tokens", "balance": "You currently have {{balance}} AI tokens.", "buy_coin_title": "You can buy AI tokens here:", "earn_coin_desc": "Or you can get AI tokens awards by inviting friends. One successful register will reward {{coins}} AI tokens:", "exceed_daily_quota": "You are FunBlocks AI {{level}} member, and your {{model_level}} request quota for today has been used up. Please upgrade your plan or choose another model to continue."}, "insights": {"exceed_daily_quota": "You have used up your daily generation quota. Please come back tomorrow or upgrade your plan to continue.", "exceed_free_trial_quota": "Your free trial quota has been used up. Please upgrade to a member to continue.", "ai_reponse_nothing": "AI did not provide a complete response, please try again later", "failed_load_image": "Failed to load image", "invalid_input": "Invalid input"}, "llm": {"gemini-2.5-pro": "Google's latest and most powerful model, with strong reasoning and multi-modal capabilities, capable of analyzing images.", "gemini-2.0-pro": "Google's model with strong reasoning and multi-modal capabilities, capable of image analysis.", "gemini-1.5-pro": "Google's model with strong reasoning and multi-modal capabilities, capable of image analysis.", "gpt-4o": "OpenAI's GPT-4.1 model, possessing a vast knowledge base and excellent language understanding.", "llama-3.1-70b-versatile": "Meta's open-source model, known for its fast generation speed.", "gemini-thinking": "Google Gemini Thinking Model, excels at reasoning through complex problems and solving them.", "gemini-2.5-flash": "Latest experimental fast reasoning version of Google Gemini 2.5, offering faster generation speed and image analysis, support search the web", "gemini-2.0-flash": "Latest fast reasoning version of Google Gemini 2.0, offering faster generation speed and image analysis, support search the web", "gemini-1.5-flash": "A fast reasoning version of Google Gemini, offering faster generation speed and image analysis.", "gpt-4o-mini": "A lightweight version of GPT-4.1, balancing speed and performance.", "claude-sonnet": "Anthrop<PERSON>'s Claude <PERSON> 4 model, capable of solving complex problems and analyzing images.", "claude-3-haiku": "Anthrop<PERSON>'s Claude 3 Haiku model, suitable for general complex problems, fast generation speed, long context support, and image analysis.", "llama3-70b-8192": "Meta's Llama 3 model, offering fast generation speed.", "llama-3.2-90b-vision-preview": "Meta's open-source model, offering fast generation speed and image analysis.", "llama-3.2-11b-vision-preview": "Meta's open-source model, offering fast generation speed and image analysis.", "deepseek-v3": "Open-sourced DeepSeek-V3 model, fast generation speed, with strong generative capabilities", "deepseek-r1": "Open-sourced DeepSeek-R1 reasoning model, excels at complex problem reasoning and solving"}, "privileges": {"rils": "Number of articles saved for ReadItLater", "rilsRead": "Manage read ReadItLater articles", "rilsTrash": "Manage deleted ReadItLater articles", "notesTrash": "Manage deleted instant notes", "media_count": "Number of uploaded images", "tts_voices": "Premium text-to-speech voices", "mediaCollection": "Manage uploaded media collection", "blocks": "Number of blocks", "members": "Maximum number of users in the workspace", "askAI": "Number of FunBlocks AI assistant requests", "usePrivateAIApi": "Support directly LLM API access", "privatePrompts": "Number of private prompts", "limited_ai_features": "Limit AI features", "generateCard": "Generate cards", "memos": "Memos", "free_trial": "Free trial", "true": "Yes", "false": "No", "unlimited": "Unlimited", "aicoins": "30 times trials", "daily_quota": "Daily quota", "notes": "1. Except for the block notes and AI functions, the main functions of FunBlocks are currently free to use;\n2. If you like the AI functions, it is recommended to purchase the AI VIP membership directly."}, "uninstall_reasons": {"no_required_feature": "The extension lacks certain features I need.", "have_better_choice": "I have found another app or service that better suits your needs.", "encounter_issues": "I encountered problems or difficulties while using the extension.", "do_not_like": "I am not satisfied with the user experience or interface design of the extension.", "no_requirement": "After trying it out, I didn't find features that would keep me using it.", "others": "None of the above options cover my uninstallation reason."}, "uninstall_reasons_detail_hint": {"no_required_feature": "Please describe the features you wanted the most.", "have_better_choice": "Please list the alternative products or solutions you are currently using.", "encounter_issues": "Please describe the issues or difficulties you encountered while using the extension.", "do_not_like": "Please list areas where you feel the user experience or interface design is unsatisfactory.", "no_requirement": "Could you please describe the features that you would continue to use?", "others": "Please list any other specific reasons for your uninstallation."}}