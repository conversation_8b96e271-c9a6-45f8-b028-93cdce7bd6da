{"phone": "téléphone", "email": "email", "somebody_workspace": "l'espace de {{nickname}}", "somebody_writingspace": "l'espace de {{nickname}}", "powered_by": "Propulsé par", "db_new_property_name": "Nom", "db_new_property_status": "Statut", "db_new_property_createdAt": "<PERSON><PERSON><PERSON>", "db_new_property_url": "URL", "db_new_property_pin": "<PERSON><PERSON><PERSON>", "default_status_name": "Statut", "default_date_name": "Date", "db_view_table": "Table", "db_view_board": "<PERSON><PERSON>", "db_view_list": "Liste", "db_view_gallery": "Galerie", "db_view_timeline": "Chronologie", "db_view_chart": "Graphique", "db_new_link_to_view": "Voir les liens", "db_property_default_label_prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "untitled_new_db": "Nouvelle base de données", "untitled_new_slides": "Nouvelles diapositives", "untitled_new_flow": "Nouveau AIFlow", "untitled_new_doc": "Nouvelle page", "untitled_db": "Base de données sans titre", "untitled_slides": "Diapositives sans titre", "untitled_flow": "AIFlow sans titre", "untitled_doc": "Page sans titre", "todo": "À faire", "doing": "En cours", "done": "Fait", "seminar_powered_by": "Ce séminaire est propulsé par FunBlocks", "present_powered_by": "Cette présentation est propulsée par FunBlocks", "seminar_slides_slogan": "@<PERSON><PERSON><PERSON><PERSON>, copilote avec assistant I<PERSON>", "present_slides_slogan": "@<PERSON><PERSON><PERSON><PERSON>, copilote avec assistant I<PERSON>", "api_level_1": "Modèle IA avancé", "api_level_2": "Modèle IA standard", "quota_model_level_1": "Limite d'accès quotidien pour le modèle IA avancé", "quota_model_level_2": "Limite d'accès quotidien pour le modèle IA standard", "quota_whiteboards": "Nombre de tableaux blancs pouvant être créés", "quota_flow_nodes": "Nombre de nœuds pouvant être sauvegardés dans chaque tableau blanc", "message": {"user_not_exist": "L'utilisateur n'existe pas ou le mot de passe est incorrect", "user_existed": "L'utilisateur existe déjà", "verification_code_error": "Erreur de code de vérification ou expiré", "oauth_user_unmatched": "Incohérence des informations utilisateur lors de l'authentification", "too_frequent": "Opération trop fréquente", "verification_code_sent": "Code de vérification envoyé à votre ", "sms_daily_limit": "Limite quotidienne de SMS atteinte", "sms_service_error": "Erreur de service SMS", "activation_email_sent": "Envoi d'un message d'activation à votre adresse e-mail, vous pouvez le vérifier et l'activer plus tard", "keep_permission_to_operator": "Conservez toutes les autorisations pour vous-même", "same_version": "Même version", "version_not_found": "Version non trouvée", "org_not_exist_or_no_access": "L'organisation n'existe pas ou vous n'avez pas accès", "content_no_access": "Aucune autorisation d'accéder à ce contenu, veuillez vous connecter à FunBlocks ou contacter le propriétaire du contenu", "operation_failed": "Échec de l'opération, veuillez réessayer plus tard", "operation_success": "Opération réussie", "server_error": "<PERSON><PERSON><PERSON> du <PERSON>", "recaptcha_verification_failed": "Échec de la vérification ReCAPTCHA"}, "notification": {"verification_email_subject": "Vérifiez votre e-mail pour FunBlocks", "verification_email_text": "Bonjour, \n\n{{account}} vient d'être enregistré sur FunBlocks. Cliquez sur le lien suivant pour activer/vérifier votre e-mail pour FunBlocks, {{activate_url}}, ou copiez le lien et ouvrez-le dans votre navigateur. \nSi ce n'est pas votre intention, veuillez ignorer cet e-mail. \n\n<PERSON><PERSON><PERSON>,\nL'équipe FunBlocks", "verification_email_html": "Bonjour, <br/><br/>{{account}} vient d'être enregistré sur FunBlocks. Cliquez sur le lien suivant pour activer/vérifier votre e-mail pour FunBlocks, <a href='{{activate_url}}'>activer</a>, <br/>Ou copiez le lien suivant et ouvrez-le dans votre navigateur : {{activate_url}} <br/>Si ce n'est pas votre intention, veuillez ignorer cet e-mail. <br/><br/><PERSON><PERSON><PERSON>,<br/>L'équipe <PERSON>locks", "verification_vcode_subject": "Code de vérification de FunBlocks", "verification_vcode_text": "Cher {{account}},\nMerci d'avoir choisi notre service. <PERSON>ur garantir la sécurité de votre compte, nous devons vérifier votre identité.\nVotre code de vérification est : {{token}}. \nVeuillez entrer le code dans les 24 heures pour compléter le processus de vérification. Veuillez noter que ce code ne peut être utilisé que pour ce processus de vérification et ne peut pas être réutilisé.\nSi vous avez reçu cet e-mail sans avoir pris d'action, veuillez l'ignorer. Si vous avez des questions ou avez besoin d'assistance, n'hésitez pas à contacter notre équipe de service client.\nMerci encore d'avoir choisi notre service.\n\nCordialement,\nL'équipe FunBlocks", "verified_title": "Bienvenue sur FunBlocks", "failed_verify_title": "Échec de la vérification de votre compte FunBlocks", "callback": "Allez à {{app}}, amusez-vous !", "token_not_exists": "Le code de vérification/le lien n'existe pas ou a expiré, veuil<PERSON><PERSON> aller sur FunBlock, vous inscrire à nouveau ou envoyer un lien de vérification", "doc_invitation_email_subject": "{{username}} vous a partagé une page FunBlocks de {{org}}", "doc_invitation_email_text": "<PERSON><PERSON><PERSON>, \nVotre ami {{username}} vous a partagé {{pageTitle}} de {{org}}. Cliquez sur le lien suivant pour voir la page, {{url}}, ou copiez le lien et ouvrez-le dans votre navigateur. \n\n<PERSON><PERSON><PERSON>,\nL'équipe <PERSON>s", "doc_invitation_email_html": "Bonjour, <br/>Votre ami {{username}} vous a partagé {{pageTitle}} de {{org}}. Cliquez sur le lien suivant pour voir la page, <a href='{{url}}'>voir</a>, <br/>Ou copiez le lien suivant et ouvrez-le dans votre navigateur : {{url}} <br/><br/><PERSON><PERSON><PERSON>,<br/>L'équipe <PERSON>locks", "doc_invitation_sms_text": "Votre ami {{username}} vous a partagé {{pageTitle}} de {{org}}. ouvrez-le : ", "org_invitation_email_subject": "{{username}} vous invite à rejoindre l'espace FunBlocks {{org}}", "org_invitation_email_text": "<PERSON><PERSON><PERSON>, \nVotre ami {{username}} vous invite à rejoindre l'espace FunBlocks {{org}}. Cliquez sur le lien suivant pour rejoindre, {{url}}, ou copiez le lien et ouvrez-le dans votre navigateur. \n\n<PERSON><PERSON><PERSON>,\nL'équipe <PERSON>locks", "org_invitation_email_html": "Bonjour, <br/>Votre ami {{username}} vous invite à rejoindre l'espace FunBlocks {{org}}. Cliquez sur le lien suivant pour rejoindre, <a href='{{url}}'>{{org}}</a>, <br/>Ou copiez le lien suivant et ouvrez-le dans votre navigateur : {{url}} <br/><br/><PERSON><PERSON><PERSON>,<br/>L'équipe FunBlocks", "org_invitation_sms_text": "Votre ami {{username}} vous invite à rejoindre l'espace FunBlocks {{org}}. ouvrez-le : ", "invite_friends_desc": "Partagez la joie avec vos amis. Invitez vos amis à utiliser FunBlocks AI et profitez ensemble de l'assistance efficace apportée par FunBlocks AI ! \n\nPour chaque ami que vous invitez, vous recevrez {{coins}} jetons FunBlocks AI gratuits ! Votre solde de jetons AI est {{balance}}", "invite_friends_msg_extension": "Je viens de trouver une super extension de navigateur appelée FunBlocks AI. Elle peut vous aider à résumer le contenu web, à peaufiner des textes, à corriger des erreurs grammaticales, à répondre à des e-mails d'un clic et à rédiger divers types de contenu ! Ça vaut vraiment le coup d'essayer !\nCliquez sur le lien suivant maintenant pour obtenir {{coins}} requêtes AI gratuites", "invite_friends_msg_flow": "Dites adieu à l'IA conversationnelle ennuyeuse ! FunBlocks AIFlow rend l'interaction avec l'IA plus intuitive avec des cartes mentales ! 🧠\n\n💡 Développez librement vos idées et connectez facilement vos pensées. \n💪 Propulsé par des modèles LLM de pointe, il gère la récupération d'informations, la génération de contenu et la résolution de problèmes avec aisance. \n🚀 Observez l'augmentation de l'efficacité, peu importe la complexité du projet.\n\nRejoignez maintenant pour avoir {{coins}} requêtes AI gratuites ! 😉", "invite_friends_msg_app": "Je viens de découvrir un outil super cool, FunBlocks AI, qui peut générer des documents et des PPT d'un clic, modifier et optimiser des compositions, continuer à écrire des histoires, générer des plans et réaliser des séances de brainstorming, etc. ! Ça vaut le coup d'essayer ! \nRejoignez maintenant pour avoir {{coins}} requêtes AI gratuites"}, "ai_items": {"review_group": "Modifier ou réviser la sélection", "generate_group": "Générer à partir du texte donné", "write_group": "Écrire avec l'IA", "draft_group": "Rédiger avec l'IA", "slides_group": "Générer à partir du texte donné pour les diapositives", "workspace_prompts_group": "Invites @workspace", "pinned_prompts_group": "<PERSON><PERSON><PERSON>", "user_prompts_group": "Invites définies par l'utilisateur", "add_prompt": "Ajouter une invite IA", "writing_tutor": "Mentor d'écriture", "writing_teacher": "Correction et retour sur les essais", "reflect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "memo_maestro": "Rédiger un mémo", "memo_maestro_image": "Rédiger un mémo", "story_image": "Raconter une histoire", "image_caption": "Générer une légende d'image", "central_ideas": "Idées centrales", "query": "Envoyer comme contenu de consultation à l'assistant IA", "optimize": "Améliorer la lisibilité", "rewrite": "Réécrire l'essai", "spelling": "Corriger l'orthographe et la grammaire", "translate_to_en": "Traduire ou peaufiner en anglais", "continue": "Continuer à écrire", "extend": "Allonger", "short": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tone": "Changer le ton", "summary_text": "Résumer", "summary_keypoints": "Résumé et points clés", "title": "Titre", "slide": "Diapositive d'une page", "slideshow": "Diaporama multi-pages", "speechscript": "Script de discours", "todos": "Trouver des éléments d'action", "flow_todolist": "Générer une liste de tâches", "flow_infograph": "Générer une infographie", "flow_flowchart": "Générer un organigramme", "flow_task_analysis": "<PERSON><PERSON><PERSON>", "cornell_notes": "Générer des notes Cornell", "flow_image_prompt": "<PERSON><PERSON><PERSON>rer une image", "generate_image": "<PERSON><PERSON><PERSON>rer une image", "summary": "Résumer", "bullet": "Plan", "keypoints": "Points clés", "speakernotes": "Notes du présentateur", "speech_suggestions": "Suggestions de présentation", "slides_optimize": "Optimisations des diapositives", "highlights": "Points forts", "share_tips": "Rédiger un texte de partage sur les réseaux sociaux", "ask_questions": "Poser des questions pertinentes", "related_questions_topics": "Questions et sujets connexes", "image_insights": "Aperçus d'image", "breakdown": "Décomposer les sujets/idées", "proof": "<PERSON><PERSON>", "positive_cases": "Cas positifs", "negative_cases": "Cas négatifs", "contradict": "Contredire", "fallacy": "Sophisme", "related_knowledges": "Comment apprendre des connaissances connexes", "simplify": "Réécrire en langage plus simple", "topicspeech": "Confé<PERSON>ce", "explain": "Expliquer", "draft": "<PERSON><PERSON><PERSON><PERSON> un ...", "brainstorming": "Brainstorming", "flow_brainstorming": "Brainstorming", "flow_mentalmodel": "<PERSON><PERSON><PERSON><PERSON> mental", "flow_subtopic_brainstorming": "Développer des idées", "flow_mindmap": "Générer une carte mentale", "flow_book_mindmap": "Générer une carte mentale", "flow_movie_mindmap": "Générer une carte mentale", "flow_decision_analysis": "Ana<PERSON><PERSON> de <PERSON>", "flow_read_mindmap": "Lecture de livre", "flow_dream_analysis": "<PERSON><PERSON><PERSON>", "flow_art_insight": "Aperçus artistiques", "flow_art_image_insight": "Appréciation de l'art", "flow_photography_coach": "Coach en photographie", "draft_prompt": "<PERSON><PERSON><PERSON><PERSON> un {content_type} sur : ", "reply": "R<PERSON><PERSON>ger une réponse", "email_outline": "Intention de l'expéditeur", "message_outline": "Intention du message", "reply_email": "Rédiger un e-mail de réponse", "quora_reply": "Répondre à une question quora", "twitter_reply": "R<PERSON><PERSON>ger une réponse", "social_post_reply": "Rédiger un commentaire", "producthunt_voter_comment": "Rédiger un commentaire", "producthunt_maker_reply": "R<PERSON><PERSON>ger une réponse", "read_email": "Lire l'e-mail", "outline": "Plan", "reply_outline": "Plan de réponse ou points clés", "refine_question": "<PERSON><PERSON><PERSON><PERSON> un post d'inquiry d'aide", "explain_codes": "Expliquer", "critial_analysis": "Analyse critique", "describe_image": "<PERSON><PERSON><PERSON><PERSON><PERSON> le contenu de l'image", "describe_image_mindmap": "Explorer l'image avec une carte mentale", "translate_image": "<PERSON><PERSON><PERSON><PERSON>", "homework": "Résoudre ce problème de devoir", "image_empathetic_reply": "Générer une réponse empathique", "witty_insights": "WonderLens - Commentaire spirituel", "image_avatar": "Transfert de style", "xSlides": "Générer des diapositives", "fix_codes_bug": "Corriger automatiquement les problèmes de code", "improve_codes": "Modifier ou améliorer avec l’IA", "lesson_plans": "Générer un plan de cours", "dok_assessment": "Générer un évaluation DOK", "teaching_slides": "Générer des diapositives de cours", "tone_professional": "Professionnel", "tone_casual": "Décontracté", "tone_straightforward": "Direct", "tone_confident": "Confiant", "tone_friendly": "Amical", "tone_academic": "Académique", "tone_enthusiastic": "Enthousias<PERSON>", "tone_empathetic": "Empathique", "chart": "Organigramme, Diapositive, Infographies ...", "chart_infographics": "Infographies", "chart_flowchart": "Organigramme", "chart_sequencediagram": "Diagramme de séquence", "chart_quadrant": "Graphique quadrants", "chart_timeline": "Chronologie", "chart_cornell_notes": "Notes Cornell", "chart_slide": "Diapositive d'une page", "expand_ideas": "Développer des idées", "expand_ideas_brainstorm": "Brainstorming", "expand_ideas_breakdown": "Décomposer", "expand_ideas_first_principle": "Premier principe", "expand_ideas_five_whys": "Analyse des causes profondes (5 Pourquoi)", "expand_ideas_scamper": "Pensée créative (SCAMPER)", "expand_ideas_problem_rephrazing": "Reformulation du problème", "expand_ideas_changing_perspectives": "Changer de perspective", "expand_ideas_reverse_thinking": "Pensée inversée", "expand_ideas_pros_cons": "Avantages et inconvénients", "expand_ideas_5w1h": "5W1H", "expand_ideas_role_storming": "<PERSON><PERSON><PERSON> de brainstorming", "expand_ideas_triz": "Résolution inventive de problèmes (TRIZ)", "expand_ideas_six_thinking_hats": "Six chapeaux de la pensée", "expand_ideas_disney_method": "Méthode Disney", "expand_ideas_swots": "Analyse SWOT", "expand_ideas_value_proposition_canvas": "Canvas de proposition de valeur", "expand_ideas_bussiness_model_canvas": "Canvas de modèle d'affaires", "translate": "Traduire en ...", "translate_english": "<PERSON><PERSON><PERSON>", "translate_japanese": "Japonais", "translate_french": "Français", "translate_spanish": "Espagnol", "translate_latin": "Latin", "translate_italian": "Italien", "translate_russian": "<PERSON><PERSON>", "translate_portuguese": "Portugais", "translate_korean": "<PERSON><PERSON><PERSON>", "translate_arabic": "<PERSON><PERSON>", "translate_hebrew": "<PERSON><PERSON><PERSON><PERSON>", "translate_dutch": "Néerlandais", "translate_german": "Allemand", "translate_indonesian": "Indonésien", "translate_danish": "<PERSON><PERSON>", "translate_swedish": "<PERSON><PERSON><PERSON><PERSON>", "translate_finnish": "<PERSON><PERSON>", "translate_turkish": "<PERSON><PERSON>", "translate_polish": "Polonais", "translate_hungarian": "Hongrois", "translate_czech": "Tchèque", "translate_romanian": "<PERSON><PERSON><PERSON><PERSON>", "translate_bulgarian": "Bulgare", "translate_greek": "Grec", "translate_thai": "<PERSON><PERSON><PERSON>", "translate_vietnamese": "<PERSON><PERSON>", "translate_malay": "<PERSON><PERSON>", "translate_traditional_chinese": "Chinois traditionnel", "translate_mandarin_chinese": "<PERSON><PERSON> simplifié", "draft_brainstorming": "Brainstorming", "draft_outline": "Plan", "draft_article": "Article", "draft_blog": "Blog", "draft_question": "Post d'inquiry d'aide", "draft_twitter": "Tweet", "draft_twitter_reply": "Réponse Tweet", "draft_weibo": "Weibo", "draft_xiaohongshu": "Xiaohongshu", "draft_zhihu": "Réponse Quora", "draft_wechat_post": "Légende de partage Moments WeChat", "draft_facebook": "Post sur la timeline Facebook", "draft_press_release": "Communiqué de presse", "draft_script": "<PERSON><PERSON><PERSON>", "draft_creative_story": "Histoire créative", "draft_presentation": "Script de discours", "draft_essay": "<PERSON><PERSON><PERSON>", "draft_poem": "Poème", "draft_love_letter": "Lettre d'amour", "draft_swot": "Analyse SWOT", "draft_weekly_report": "Rapport hebdomadaire", "draft_cons_pros": "Avantages et inconvénients", "draft_job_description": "Description de poste", "draft_sales_email": "E-mail de vente", "draft_more": "...", "input": "..."}, "sub_task": {"label": "Générer {{action_label}} avec l'IA"}, "sub_task_titles": {"email_outline": "Contenu de l'e-mail", "message_outline": "Contenu du message"}, "label": {"topic": "Sujet", "outline": "Points clés ou plans", "email_outline": "Intention de l'expéditeur", "message_outline": "Intention de l'expéditeur", "reply_outline": "Plan de réponse ou points clés", "other_reqs": "Autres exigences", "target_learners": "Public cible", "duration": "<PERSON><PERSON><PERSON>", "educational_model": "Modèle éducatif", "art_style": "Style artistique"}, "hint": {"topic": "Sujet", "outline": "Points clés ou plan", "reply_outline": "Plan ou points clés pour la réponse", "other_reqs": "Autres exigences, telles que le style de langue, le nombre de mots, etc.", "target_learners": "Par exemple, élèves de 9ème année, étudiants universitaires, professionnels, etc.", "duration": "Par exemple, 45 minutes, 90 minutes, 120 minutes, etc."}, "ai_roles": {"doctor": "médecin", "professor": "professeur", "philosopher": "philosophe", "psychologist": "psychologue", "relationship_coach": "Coach de relations", "scientist": "Scientifique", "historian": "Historien", "motivational_coach": "Coach de motivation", "fortune_teller": "Voyant", "astrologer": "Astrologue", "legal_advisor": "Conseiller juridique", "fallacy": "Sophisme", "debater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storyteller": "<PERSON><PERSON><PERSON><PERSON>", "novelist": "Romancier", "poet": "Poète", "essay_writer": "<PERSON><PERSON><PERSON>ct<PERSON> d'essais", "social_writer": "Rédacteur de contenu pour les réseaux sociaux", "question_answer": "Réponse Quora", "question_writer": "Rédacteur de questions", "motivational_speaker": "Conférencier motivateur", "elocutionist": "Élocutionniste", "business_analyst": "Analyste commercial", "it_expert": "Expert en informatique", "spoken_english_teacher": "Professeur d'an<PERSON><PERSON><PERSON> par<PERSON>", "screen_writer": "<PERSON><PERSON><PERSON><PERSON>", "career_counselor": "Conseiller de carrière", "personal_trainer": "Entraîneur personnel", "mental_health_adviser": "Conseiller en santé mentale", "academician": "Académicien", "time_travel_guide": "Guide de voyage dans le temps", "confucius": "<PERSON><PERSON>cius", "lao_tzu": "Lao Tzu", "zhuangzi": "<PERSON><PERSON><PERSON>", "sun_tzu": "Sun Tzu", "simaqian": "<PERSON><PERSON>", "sushi": "<PERSON>", "libai": "<PERSON>", "wangyangming": "<PERSON>", "buddha": "<PERSON><PERSON><PERSON>", "jesus": "<PERSON><PERSON><PERSON>", "socrates": "Socrate", "aristotle": "Aristote", "plato": "Platon", "nietzsche": "<PERSON>", "sartre": "<PERSON><PERSON><PERSON>", "freud": "<PERSON><PERSON><PERSON>"}, "ai": {"exceed_msg_limit": "Il semble que vous ayez épuisé vos requêtes gratuites quotidiennes avec l'assistant IA. Pour continuer à utiliser nos services, vous pouvez :\n\n1. Obtenir un abonnement FunBlocks AI pour un accès illimité.\n2. Configurer vos propres API LLM pour utiliser des services LLM tiers.\n3. Revenez demain pour 20 requêtes gratuites supplémentaires.\n\nMerci pour votre soutien !", "text_too_long": "Le texte dépasse la limite de longueur", "no_reply": "Échec d'obtention des résultats, veuillez réessayer plus tard !", "exceed_askai_speed_limit": "Dépassement de la vitesse de réponse de l'assistant IA, ve<PERSON><PERSON><PERSON> réessayer plus tard", "ai_request_timeout": "Aucune réponse de l'assistant IA, ve<PERSON><PERSON><PERSON> réessayer plus tard", "content_filter": "Le contenu contient des informations sensibles, filtré", "llm_service_failed": "Le service LLM IA a échoué, veuillez réessayer plus tard", "welcome_msg": "Bienvenue dans notre chatbot propulsé par un grand modèle de langage ! Nous sommes heureux que vous soyez ici. En tant que modèle de langage, nous excellons dans les tâches de traitement du langage naturel, y compris la génération de langage, la traduction et la réponse aux questions. Nous pouvons répondre à vos questions sur un large éventail de sujets, y compris l'histoire, la culture, la science, la technologie et la littérature. Cependant, il est important de garder à l'esprit que nos réponses peuvent ne pas toujours être précises, et nous ne pouvons pas fournir des mises à jour d'actualités en temps réel.\n\nPar exemple, vous pourriez nous demander : 'Quelle est la capitale de la France ?' et nous pouvons utiliser notre compréhension du langage et notre base de connaissances pour fournir une réponse. Mais n'hésitez pas à nous poser des questions plus spécifiques ou à reformuler votre question si notre réponse n'est pas utile. Nous sommes ici pour vous aider du mieux que nous pouvons !", "coins": "Les jetons IA peuvent être utilisés pour payer l'accès aux assistants IA, et un jeton IA est consommé par tâche IA. Vous pouvez acheter des jetons IA, ou vous pouvez obtenir des jetons IA gratuitement grâce à des activités telles que 'Inviter des amis'.", "coin": "Jetons IA", "balance": "Vous avez actuellement {{balance}} jetons IA.", "buy_coin_title": "Vous pouvez acheter des jetons IA ici :", "earn_coin_desc": "Ou vous pouvez obtenir des récompenses de jetons IA en invitant des amis. Une inscription réussie vous récompensera avec {{coins}} jetons IA :", "exceed_daily_quota": "Vous êtes membre FunBlocks AI {{level}}, et votre quota de requêtes {{model_level}} pour aujourd'hui a été épuisé. Veuillez mettre à niveau votre plan ou choisir un autre modèle pour continuer."}, "insights": {"exceed_daily_quota": "Vous avez épuisé votre quota de génération quotidien. Veuillez revenir demain ou mettre à niveau votre plan pour continuer.", "exceed_free_trial_quota": "Votre quota d'essai gratuit a été épuisé. Veuillez passer à un membre pour continuer.", "ai_reponse_nothing": "L'IA n'a pas fourni de réponse complète, ve<PERSON><PERSON>z réessayer plus tard", "failed_load_image": "Échec du chargement de l'image", "invalid_input": "En<PERSON><PERSON> invalide"}, "llm": {"gemini-2.5-pro": "Le modèle le plus récent et le plus puissant de Google, avec de fortes capacités de raisonnement et multimodales, capable d'analyser des images.", "gemini-2.0-pro": "Modèle de Google avec de fortes capacités de raisonnement et multimodales, capable d'analyser des images.", "gemini-1.5-pro": "Modèle de Google avec de fortes capacités de raisonnement et multimodales, capable d'analyser des images.", "gpt-4o": "Modèle GPT-4.1 d'OpenAI, possédant une vaste base de connaissances et une excellente compréhension du langage.", "llama-3.1-70b-versatile": "Modèle open-source de Meta, connu pour sa rapidité de génération.", "gemini-thinking": "Mod<PERSON>le de pensée Google Gemini, excelle dans le raisonnement à travers des problèmes complexes et leur résolution.", "gemini-2.5-flash": "Dernière version expérimentale de raisonnement rapide de Google Gemini 2.5, offrant une vitesse de génération plus rapide et une analyse d'image, supporte la recherche sur le web", "gemini-2.0-flash": "Dernière version de raisonnement rapide de Google Gemini 2.0, offrant une vitesse de génération plus rapide et une analyse d'image, supporte la recherche sur le web", "gemini-1.5-flash": "Une version de raisonnement rapide de Google Gemini, offrant une vitesse de génération plus rapide et une analyse d'image.", "gpt-4o-mini": "Une version légère de GPT-4.1, équilibrant vitesse et performance.", "claude-sonnet": "<PERSON><PERSON><PERSON><PERSON> 4 d'Anthropic, capable de résoudre des problèmes complexes et d'analyser des images.", "claude-3-haiku": "Modèle Claude 3 Haiku d'Anthropic, adapté aux problèmes complexes généraux, vitesse de génération rapide, support de long contexte et analyse d'image.", "llama3-70b-8192": "Modèle Llama 3 de Meta, offrant une vitesse de génération rapide.", "llama-3.2-90b-vision-preview": "Modèle open-source de Meta, offrant une vitesse de génération rapide et une analyse d'image.", "llama-3.2-11b-vision-preview": "Modèle open-source de Meta, offrant une vitesse de génération rapide et une analyse d'image.", "deepseek-v3": "Modèle open-source DeepSeek-V3, vitesse de génération rapide, avec de fortes capacités génératives", "deepseek-r1": "Modèle de raisonnement DeepSeek-R1 open-source, excelle dans le raisonnement et la résolution de problèmes complexes"}, "privileges": {"rils": "Nombre d'articles sauvegardés pour ReadItLater", "rilsRead": "<PERSON><PERSON><PERSON> les articles ReadItLater lus", "rilsTrash": "Gérer les articles ReadItLater supprimés", "notesTrash": "G<PERSON>rer les notes instantanées supprimées", "media_count": "Nombre d'images téléchargées", "tts_voices": "Voix premium de synthèse vocale", "mediaCollection": "Gérer la collection de médias téléchargés", "blocks": "Nombre de blocs", "members": "Nombre maximum d'utilisateurs dans l'espace de travail", "askAI": "Nombre de requêtes à l'assistant FunBlocks AI", "usePrivateAIApi": "Support d'accès direct à l'API LLM", "privatePrompts": "Nombre d'invites privées", "limited_ai_features": "Limiter les fonctionnalités IA", "generateCard": "Générer des cartes", "memos": "Mémos", "free_trial": "<PERSON><PERSON><PERSON> gratuit", "true": "O<PERSON>", "false": "Non", "unlimited": "Illimité", "aicoins": "30 essais", "daily_quota": "Quota quotidien", "notes": "1. <PERSON><PERSON> pour les notes de bloc et les fonctions IA, les principales fonctions de FunBlocks sont actuellement gratuites à utiliser ;\n2. <PERSON> vous aimez les fonctions IA, il est recommandé d'acheter directement l'abonnement VIP IA."}, "uninstall_reasons": {"no_required_feature": "L'extension manque de certaines fonctionnalités dont j'ai besoin.", "have_better_choice": "J'ai trouvé une autre application ou service qui répond mieux à mes besoins.", "encounter_issues": "J'ai rencontré des problèmes ou des difficultés lors de l'utilisation de l'extension.", "do_not_like": "Je ne suis pas satisfait de l'expérience utilisateur ou du design de l'interface de l'extension.", "no_requirement": "Après l'avoir essayé, je n'ai pas trouvé de fonctionnalités qui me feraient continuer à l'utiliser.", "others": "Aucune des options ci-dessus ne couvre ma raison de désinstallation."}, "uninstall_reasons_detail_hint": {"no_required_feature": "Veuillez décrire les fonctionnalités que vous souhaitiez le plus.", "have_better_choice": "Veuillez lister les produits ou solutions alternatives que vous utilisez actuellement.", "encounter_issues": "Veuillez décrire les problèmes ou difficultés que vous avez rencontrés lors de l'utilisation de l'extension.", "do_not_like": "<PERSON><PERSON><PERSON><PERSON> lister les domaines où vous estimez que l'expérience utilisateur ou le design de l'interface sont insatisfaisants.", "no_requirement": "Pourriez-vous décrire les fonctionnalités que vous continueriez à utiliser ?", "others": "<PERSON><PERSON><PERSON><PERSON> lister toute autre raison spécifique de votre désinstallation."}}