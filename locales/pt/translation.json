{"phone": "telefone", "email": "email", "somebody_workspace": "Espaç<PERSON> de {{nickname}}", "somebody_writingspace": "Espaço de escrita de {{nickname}}", "powered_by": "Desenvolvido por", "db_new_property_name": "Nome", "db_new_property_status": "Status", "db_new_property_createdAt": "C<PERSON><PERSON> em", "db_new_property_url": "URL", "db_new_property_pin": "Fixar", "default_status_name": "Status", "default_date_name": "Data", "db_view_table": "<PERSON><PERSON><PERSON>", "db_view_board": "Quadro", "db_view_list": "Lista", "db_view_gallery": "Galeria", "db_view_timeline": "Linha do tempo", "db_view_chart": "Gráfico", "db_new_link_to_view": "Ver links", "db_property_default_label_prefix": "<PERSON><PERSON><PERSON><PERSON> ", "untitled_new_db": "Novo Banco de Dados", "untitled_new_slides": "Novos Slides", "untitled_new_flow": "Novo AIFlow", "untitled_new_doc": "Nova Página", "untitled_db": "Banco de Dados Sem Título", "untitled_slides": "Slide<PERSON> <PERSON>", "untitled_flow": "AIFlow <PERSON>", "untitled_doc": "<PERSON><PERSON>gin<PERSON> Tí<PERSON>lo", "todo": "A Fazer", "doing": "Fazendo", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seminar_powered_by": "Este seminário é desenvolvido pela FunBlocks", "present_powered_by": "Esta apresentação é desenvolvida pela FunBlocks", "seminar_slides_slogan": "@FunBlocks, copiloto com assistente de IA", "present_slides_slogan": "@FunBlocks, copiloto com assistente de IA", "api_level_1": "Modelo de IA Avançado", "api_level_2": "Modelo de IA Padrão", "quota_model_level_1": "Limite diário de acesso para o modelo de IA avançado", "quota_model_level_2": "Limite diário de acesso para o modelo de IA padrão", "quota_whiteboards": "Número de quadros brancos que podem ser criados", "quota_flow_nodes": "Número de nós que podem ser salvos em cada quadro branco", "message": {"user_not_exist": "Usuário não existe ou senha incorreta", "user_existed": "Usuário j<PERSON>e", "verification_code_error": "Erro no código de verificação ou expirado", "oauth_user_unmatched": "Incompatibilidade de informações do usuário durante a autenticação", "too_frequent": "Operação muito frequente", "verification_code_sent": "Código de verificação enviado para seu ", "sms_daily_limit": "Limite di<PERSON>rio de SMS atingido", "sms_service_error": "Erro no serviço de SMS", "activation_email_sent": "Mensagem de ativação enviada para seu endereço de email, você pode verificar e ativar mais tarde", "keep_permission_to_operator": "<PERSON><PERSON><PERSON> todas as permissões para você", "same_version": "<PERSON><PERSON> versão", "version_not_found": "Versão não encontrada", "org_not_exist_or_no_access": "Organização não existe ou você não tem acesso", "content_no_access": "Sem permissão para acessar este conteúdo, por favor faça login na FunBlocks ou entre em contato com o proprietário do conteúdo", "operation_failed": "Operação falhou, por favor tente novamente mais tarde", "operation_success": "Operação bem-sucedida", "server_error": "Erro no servidor", "recaptcha_verification_failed": "Falha na verificação do ReCAPTCHA"}, "notification": {"verification_email_subject": "Verifique seu email na FunBlocks", "verification_email_text": "<PERSON><PERSON><PERSON>, \n\n{{account}} foi registrado na FunBlocks. Clique no link a seguir para ativar/verificar seu email na FunBlocks, {{activate_url}}, ou copie o link e abra no navegador. \nSe não foi sua intenção, por favor ignore este email. \n\n<PERSON><PERSON><PERSON>,\nEquipe FunBlocks", "verification_email_html": "<PERSON><PERSON><PERSON>, <br/><br/>{{account}} foi registrado na FunBlocks. Clique no link a seguir para ativar/verificar seu email na FunBlocks, <a href='{{activate_url}}'>ativar</a>, <br/>Ou copie o link a seguir e abra no navegador: {{activate_url}} <br/>Se não foi sua intenção, por favor ignore este email. <br/><br/><PERSON><PERSON><PERSON>,<br/>Equipe FunBlocks", "verification_vcode_subject": "Código de verificação da FunBlocks", "verification_vcode_text": "<PERSON>o {{account}},\n<PERSON><PERSON><PERSON> por escolher nosso serviço. Para garantir a segurança da sua conta, precisamos verificar sua identidade.\nSeu código de verificação é: {{token}}. \nPor favor, insira o código dentro de 24 horas para completar o processo de verificação. Observe que este código só pode ser usado para este processo de verificação e não pode ser reutilizado.\nSe você recebeu este email sem tomar nenhuma ação, por favor desconsidere. Se você tiver alguma dúvida ou precisar de assistência, sinta-se à vontade para entrar em contato com nossa equipe de atendimento ao cliente.\nObrigado novamente por escolher nosso serviço.\n\nAtenciosamente,\nEquipe <PERSON>locks", "verified_title": "Bem-vindo à FunBlocks", "failed_verify_title": "Falha ao verificar sua conta FunBlocks", "callback": "Vá para {{app}}, divirta-se!", "token_not_exists": "O código/link de verificação não existe ou expirou, por favor vá para FunBlock, registre-se novamente ou envie um link de verificação", "doc_invitation_email_subject": "{{username}} compartilhou uma página da FunBlocks com você de {{org}}", "doc_invitation_email_text": "<PERSON><PERSON><PERSON>, \nSeu amigo {{username}} compartilhou com você a {{pageTitle}} de {{org}}. Clique no link a seguir para visualizar a página, {{url}}, ou copie o link e abra no navegador. \n\n<PERSON><PERSON><PERSON>,\nEquipe <PERSON>Blocks", "doc_invitation_email_html": "<PERSON><PERSON><PERSON>, <br/>Seu amigo {{username}} compartilhou com você a {{pageTitle}} de {{org}}. Clique no link a seguir para visualizar a página, <a href='{{url}}'>visualizar</a>, <br/>Ou copie o link a seguir e abra no navegador: {{url}} <br/><br/><PERSON><PERSON><PERSON>,<br/>Equipe <PERSON>Blocks", "doc_invitation_sms_text": "Seu amigo {{username}} compartilhou com você a {{pageTitle}} de {{org}}. abra: ", "org_invitation_email_subject": "{{username}} te convida para se juntar ao espaço FunBlocks {{org}}", "org_invitation_email_text": "<PERSON><PERSON><PERSON>, \nSeu amigo {{username}} te convida para se juntar ao espaço FunBlocks {{org}}. Clique no link a seguir para se juntar, {{url}}, ou copie o link e abra no navegador. \n\n<PERSON><PERSON><PERSON>,\nEquipe FunBlocks", "org_invitation_email_html": "<PERSON><PERSON><PERSON>, <br/>Seu amigo {{username}} te convida para se juntar ao espaço FunBlocks {{org}}. Clique no link a seguir para se juntar, <a href='{{url}}'>{{org}}</a>, <br/>Ou copie o link a seguir e abra no navegador: {{url}} <br/><br/><PERSON><PERSON><PERSON>,<br/>Equipe FunBlocks", "org_invitation_sms_text": "Seu amigo {{username}} te convida para se juntar ao espaço FunBlocks {{org}}. abra: ", "invite_friends_desc": "Compartilhe a alegria com amigos. Convide seus amigos para usar a FunBlocks AI e aproveite a assistência eficiente trazida pela FunBlocks AI juntos! \n\nPara cada amigo que você convidar, você receberá {{coins}} tokens gratuitos da FunBlocks AI! Seu saldo de tokens da IA é {{balance}}", "invite_friends_msg_extension": "Acabei de encontrar uma extensão de navegador super legal chamada FunBlocks AI. Ela pode te ajudar a resumir conteúdo da web, polir textos, corrigir erros gramaticais, responder emails com um clique e escrever vários tipos de conteúdo! Definitivamente vale a pena experimentar!\nClique no link a seguir agora para obter {{coins}} consultas gratuitas de IA", "invite_friends_msg_flow": "Diga adeus à IA conversacional chata! O FunBlocks AIFlow torna a interação com a IA mais intuitiva com mapas mentais! 🧠\n\n💡 Expanda suas ideias livremente e conecte seus pensamentos sem esforço. \n💪 Desenvolvido por modelos LLM líderes, ele lida com recuperação de informações, geração de conteúdo e resolução de problemas com facilidade. \n🚀 Veja o aumento na eficiência, não importa quão complexo seja o projeto.\n\nJunte-se agora para ter {{coins}} consultas gratuitas de IA! 😉", "invite_friends_msg_app": "Acabei de descobrir uma ferramenta super legal, FunBlocks AI, que pode gerar documentos e PPTs com um clique, modificar e otimizar composições, continuar escrevendo histórias, gerar esboços e realizar brainstorming, etc.! Vale a pena experimentar! \nJunte-se agora para ter {{coins}} consultas gratuitas de IA"}, "ai_items": {"review_group": "Editar ou revisar seleção", "generate_group": "Gerar a partir do texto fornecido", "write_group": "Escrever com IA", "draft_group": "Rascunho com IA", "slides_group": "Gerar a partir do texto fornecido para Slides", "workspace_prompts_group": "Prompts do @workspace", "pinned_prompts_group": "Prompts fixados", "user_prompts_group": "Prompts definidos pelo usuário", "add_prompt": "Adicionar prompt de IA", "writing_tutor": "Mentor de escrita", "writing_teacher": "Avaliação e feedback em redações", "reflect": "Reflexão", "memo_maestro": "Escrever um Memorando", "memo_maestro_image": "Escrever um Memorando", "story_image": "Contar uma história", "image_caption": "Gerar legenda de imagem", "central_ideas": "Ideias centrais", "query": "Enviar como conteúdo consultivo para o assistente de IA", "optimize": "Mel<PERSON><PERSON> legibilidade", "rewrite": "Reescrever redação", "spelling": "Corrigir ortografia e gramática", "translate_to_en": "Traduzir ou polir para o inglês", "continue": "<PERSON><PERSON><PERSON><PERSON> es<PERSON><PERSON>do", "extend": "Fazer mais longo", "short": "Fazer mais curto", "tone": "<PERSON><PERSON> tom", "summary_text": "Resumir", "summary_keypoints": "Resumo e pontos principais", "title": "<PERSON><PERSON><PERSON><PERSON>", "slide": "Slide de uma página", "slideshow": "Apresentação de várias páginas", "speechscript": "Roteiro de discurso", "todos": "Encontrar itens de ação", "flow_todolist": "Gerar Lista de Tarefas", "flow_infograph": "<PERSON><PERSON><PERSON>", "flow_flowchart": "Gerar <PERSON>", "flow_task_analysis": "<PERSON><PERSON><PERSON><PERSON> TaskMaster", "cornell_notes": "<PERSON><PERSON><PERSON>", "flow_image_prompt": "<PERSON><PERSON><PERSON> imagem", "generate_image": "<PERSON><PERSON><PERSON> imagem", "summary": "Resumir", "bullet": "Esboço", "keypoints": "Pontos principais", "speakernotes": "Notas do apresentador", "speech_suggestions": "Sugestões de apresentação", "slides_optimize": "Otimizações de slides", "highlights": "Destaques", "share_tips": "Escrever um texto para compartilhamento em redes sociais", "ask_questions": "Fazer perguntas relevantes", "related_questions_topics": "Perguntas e tópicos relevantes", "image_insights": "Insights de imagem", "breakdown": "<PERSON><PERSON><PERSON> t<PERSON>/ideias", "proof": "<PERSON><PERSON>", "positive_cases": "Casos positivos", "negative_cases": "Casos negativos", "contradict": "Contradizer", "fallacy": "Falácia", "related_knowledges": "Como aprender conhecimentos relacionados", "simplify": "Reescrever em linguagem mais simples", "topicspeech": "Palestra", "explain": "Explicar", "draft": "Escrever um ...", "brainstorming": "Brainstorming", "flow_brainstorming": "Brainstorming", "flow_mentalmodel": "Modelo mental", "flow_subtopic_brainstorming": "Expandir ideias", "flow_mindmap": "Gerar um mapa mental", "flow_book_mindmap": "Gerar um mapa mental", "flow_movie_mindmap": "Gerar um mapa mental", "flow_decision_analysis": "Análise de decisão", "flow_read_mindmap": "Leitura de livro", "flow_dream_analysis": "<PERSON><PERSON><PERSON><PERSON>", "flow_art_insight": "Insights sobre arte", "flow_art_image_insight": "Apreciação de arte", "flow_photography_coach": "Coach de fotografia", "draft_prompt": "Escrever um {content_type} sobre: ", "reply": "Escrever uma resposta", "email_outline": "Intenção do remetente", "message_outline": "Intenção da mensagem", "reply_email": "Compor um email de resposta", "quora_reply": "Responder pergunta do quora", "twitter_reply": "Compor uma resposta", "social_post_reply": "Compor um comentário", "producthunt_voter_comment": "Compor um comentário", "producthunt_maker_reply": "Compor uma resposta", "read_email": "<PERSON><PERSON>", "outline": "Esboço", "reply_outline": "Esboço ou pontos principais da resposta", "refine_question": "Escrever um post de consulta de ajuda", "explain_codes": "Explicar", "critial_analysis": "Análise crítica abrangente", "describe_image": "<PERSON><PERSON><PERSON> con<PERSON><PERSON><PERSON> da <PERSON>", "describe_image_mindmap": "Explorar imagem com mapa mental", "translate_image": "Traduzir", "homework": "Resolver este problema de dever de casa", "image_empathetic_reply": "Gerar resposta empá<PERSON>", "witty_insights": "WonderLens - <PERSON><PERSON><PERSON><PERSON> es<PERSON>", "image_avatar": "Transferência de estilo", "xSlides": "<PERSON><PERSON><PERSON> slides", "fix_codes_bug": "Corrigir problemas de código automaticamente", "improve_codes": "Modificar ou melhorar com IA", "lesson_plans": "Gerar planos de aula", "dok_assessment": "Gerar avaliação DOK", "teaching_slides": "<PERSON><PERSON><PERSON> <PERSON> de aula", "tone_professional": "Profissional", "tone_casual": "Casual", "tone_straightforward": "Direto", "tone_confident": "<PERSON><PERSON><PERSON>", "tone_friendly": "Amigável", "tone_academic": "Acadêmico", "tone_enthusiastic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tone_empathetic": "Empá<PERSON>o", "chart": "Fluxograma, Slide, Infográficos ...", "chart_infographics": "Infográficos", "chart_flowchart": "Fluxograma", "chart_sequencediagram": "Diagrama de sequência", "chart_quadrant": "Gráfico quadrante", "chart_timeline": "Linha do tempo", "chart_cornell_notes": "Notas <PERSON>", "chart_slide": "Slide de uma página", "expand_ideas": "Expandir <PERSON>", "expand_ideas_brainstorm": "Brainstorming", "expand_ideas_breakdown": "<PERSON><PERSON><PERSON><PERSON>", "expand_ideas_first_principle": "Princípio Fundamental", "expand_ideas_five_whys": "Análise de Causa Raiz (5 Porquês)", "expand_ideas_scamper": "Pensamento Criativo (SCAMPER)", "expand_ideas_problem_rephrazing": "Reformulação de Problemas", "expand_ideas_changing_perspectives": "Mudando Perspectivas", "expand_ideas_reverse_thinking": "Pensamento Reverso", "expand_ideas_pros_cons": "Prós e Contras", "expand_ideas_5w1h": "5W1H", "expand_ideas_role_storming": "Role Storming", "expand_ideas_triz": "Solução Inventiva de Problemas (TRIZ)", "expand_ideas_six_thinking_hats": "Seis Chapéus do Pensamento", "expand_ideas_disney_method": "Método Disney", "expand_ideas_swots": "Análise SWOT", "expand_ideas_value_proposition_canvas": "Canvas de Proposta de Valor", "expand_ideas_bussiness_model_canvas": "Canvas de Modelo de Negócio", "translate": "Traduzir para ...", "translate_english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "translate_japanese": "<PERSON><PERSON><PERSON><PERSON>", "translate_french": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "translate_spanish": "Espanhol", "translate_latin": "La<PERSON><PERSON>", "translate_italian": "Italiano", "translate_russian": "<PERSON>", "translate_portuguese": "Português", "translate_korean": "<PERSON><PERSON>", "translate_arabic": "<PERSON><PERSON><PERSON>", "translate_hebrew": "Hebraico", "translate_dutch": "<PERSON><PERSON><PERSON><PERSON>", "translate_german": "Alemão", "translate_indonesian": "Indonésio", "translate_danish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translate_swedish": "Sueco", "translate_finnish": "<PERSON><PERSON><PERSON>", "translate_turkish": "<PERSON><PERSON><PERSON>", "translate_polish": "<PERSON><PERSON><PERSON><PERSON>", "translate_hungarian": "<PERSON><PERSON><PERSON><PERSON>", "translate_czech": "Tcheco", "translate_romanian": "Romeno", "translate_bulgarian": "Búlgaro", "translate_greek": "<PERSON><PERSON>", "translate_thai": "Tailandês", "translate_vietnamese": "Vietnamita", "translate_malay": "Malaio", "translate_traditional_chinese": "<PERSON>ês Tradici<PERSON>", "translate_mandarin_chinese": "<PERSON><PERSON><PERSON>li<PERSON>", "draft_brainstorming": "Brainstorming", "draft_outline": "Esboço", "draft_article": "Artigo", "draft_blog": "Blog", "draft_question": "Post de consulta de ajuda", "draft_twitter": "Tweet", "draft_twitter_reply": "<PERSON><PERSON><PERSON><PERSON>", "draft_weibo": "Weibo", "draft_xiaohongshu": "Xiaohongshu", "draft_zhihu": "Resposta do Quora", "draft_wechat_post": "Legenda de Compartilhamento do WeChat", "draft_facebook": "Postagem no Facebook", "draft_press_release": "Comunicado de Imprensa", "draft_script": "Roteiro", "draft_creative_story": "História Criativa", "draft_presentation": "Roteiro de Discurso", "draft_essay": "Redação", "draft_poem": "<PERSON><PERSON>", "draft_love_letter": "Carta de Amor", "draft_swot": "Análise SWOT", "draft_weekly_report": "<PERSON><PERSON><PERSON><PERSON>", "draft_cons_pros": "Prós e Contras", "draft_job_description": "Descrição do Trabalho", "draft_sales_email": "<PERSON><PERSON>", "draft_more": "...", "input": "..."}, "sub_task": {"label": "Gerar {{action_label}} com IA"}, "sub_task_titles": {"email_outline": "Conteúdo do <PERSON>", "message_outline": "<PERSON><PERSON><PERSON><PERSON> da mensagem"}, "label": {"topic": "Tópico", "outline": "Pontos principais ou esboços", "email_outline": "Intenção do remetente", "message_outline": "Intenção da mensagem", "reply_outline": "Esboço ou pontos principais da resposta", "other_reqs": "Outros requisitos", "target_learners": "Público-alvo", "duration": "Duração", "educational_model": "Modelo educacional", "art_style": "<PERSON><PERSON><PERSON>"}, "hint": {"topic": "Tópico", "outline": "Pontos principais ou esboço", "reply_outline": "Esboço ou pontos principais para a resposta", "other_reqs": "Outros requisitos, como estilo de linguagem, contagem de palavras, etc.", "target_learners": "Por exemplo, 9º ano, estudantes universitários, profissionais, etc.", "duration": "Por exemplo, 45 min, 90 min, 120 min, etc."}, "ai_roles": {"doctor": "médico", "professor": "professor", "philosopher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "psychologist": "psicólogo", "relationship_coach": "<PERSON> <PERSON> Re<PERSON>cion<PERSON>", "scientist": "Cientista", "historian": "Historiador", "motivational_coach": "Coach <PERSON><PERSON><PERSON><PERSON><PERSON>", "fortune_teller": "<PERSON><PERSON><PERSON><PERSON>", "astrologer": "Astrólogo", "legal_advisor": "Consultor <PERSON><PERSON><PERSON><PERSON><PERSON>", "fallacy": "Falácia", "debater": "Debatedor", "storyteller": "Con<PERSON><PERSON> de Histórias", "novelist": "Romancista", "poet": "<PERSON><PERSON>", "essay_writer": "Redator de Ensaios", "social_writer": "Redator de Mídias Sociais", "question_answer": "Resposta do Quora", "question_writer": "Redator de Perguntas", "motivational_speaker": "Palestrante Motivacional", "elocutionist": "Elocucionista", "business_analyst": "Analista de Negócios", "it_expert": "Especialista em TI", "spoken_english_teacher": "Professor <PERSON><PERSON><PERSON>", "screen_writer": "<PERSON><PERSON><PERSON><PERSON>", "career_counselor": "Orientador de Carreira", "personal_trainer": "<PERSON><PERSON><PERSON><PERSON>", "mental_health_adviser": "Consultor <PERSON> Saúde Mental", "academician": "Acadêmico", "time_travel_guide": "<PERSON><PERSON><PERSON> de Viagem no Tempo", "confucius": "<PERSON><PERSON><PERSON><PERSON>", "lao_tzu": "Lao Tzu", "zhuangzi": "<PERSON><PERSON><PERSON>", "sun_tzu": "Sun Tzu", "simaqian": "<PERSON><PERSON>", "sushi": "<PERSON>", "libai": "<PERSON>", "wangyangming": "<PERSON>", "buddha": "Buda", "jesus": "<PERSON>", "socrates": "<PERSON><PERSON><PERSON>", "aristotle": "Aristóteles", "plato": "Platão", "nietzsche": "<PERSON>", "sartre": "<PERSON><PERSON><PERSON>", "freud": "<PERSON><PERSON><PERSON>"}, "ai": {"exceed_msg_limit": "Parece que você esgotou suas consultas gratuitas diárias com o assistente de IA. Para continuar usando nossos serviços, você pode:\n\n1. Obter uma assinatura da FunBlocks AI para acesso ilimitado.\n2. Configurar suas próprias APIs LLM para usar serviços LLM de terceiros.\n3. Voltar amanhã para mais 20 consultas gratuitas.\n\nObrigado pelo seu apoio!", "text_too_long": "Texto excede o limite de comprimento", "no_reply": "Falha ao obter resultados, por favor tente novamente mais tarde!", "exceed_askai_speed_limit": "Excedeu a velocidade de resposta do assistente de IA, por favor tente novamente mais tarde", "ai_request_timeout": "Sem resposta do assistente de IA, por favor tente novamente mais tarde", "content_filter": "O conteúdo contém informações sensíveis, filtrado", "llm_service_failed": "Serviço LLM de IA falhou, por favor tente novamente mais tarde", "welcome_msg": "Bem-vindo ao nosso chatbot alimentado por um modelo de linguagem grande! Estamos felizes que você esteja aqui. Como um modelo de linguagem, nós nos destacamos em tarefas de processamento de linguagem natural, incluindo geração de linguagem, tradução e resposta a perguntas. Podemos responder suas perguntas sobre uma ampla gama de tópicos, incluindo história, cultura, ciência, tecnologia e literatura. No entanto, é importante ter em mente que nossas respostas podem não ser sempre precisas, e não podemos fornecer atualizações de notícias em tempo real.\n\nPor exemplo, você pode nos perguntar: 'Qual é a capital da França?' e podemos usar nossa compreensão da linguagem e base de conhecimento para fornecer uma resposta. Mas, por favor, não hesite em nos fazer perguntas mais específicas ou reformular sua pergunta se nossa resposta não for útil. Estamos aqui para ajudar da melhor forma possível!", "coins": "Tokens de IA podem ser usados para pagar pelo acesso a assistentes de IA, e um token de IA é consumido por tarefa de IA. Você pode comprar tokens de IA, ou pode obter tokens de IA gratuitamente através de atividades como 'Convidar amigos'.", "coin": "tokens de IA", "balance": "Você atualmente tem {{balance}} tokens de IA.", "buy_coin_title": "Você pode comprar tokens de IA aqui:", "earn_coin_desc": "Ou você pode ganhar tokens de IA convidando amigos. Um registro bem-sucedido recompensará {{coins}} tokens de IA:", "exceed_daily_quota": "Você é membro da FunBlocks AI {{level}}, e sua cota de solicitações {{model_level}} para hoje foi esgotada. Por favor, atualize seu plano ou escolha outro modelo para continuar."}, "insights": {"exceed_daily_quota": "Você esgotou sua cota diária de geração. Por favor, volte amanhã ou atualize seu plano para continuar.", "exceed_free_trial_quota": "Sua cota de teste gratuito foi esgotada. Por favor, atualize para um membro para continuar.", "ai_reponse_nothing": "A IA não forneceu uma resposta completa, por favor tente novamente mais tarde", "failed_load_image": "Falha ao carregar imagem", "invalid_input": "Entrada inválida"}, "llm": {"gemini-2.5-pro": "O modelo mais recente e poderoso do Google, com forte raciocínio e capacidades multimodais, capaz de analisar imagens.", "gemini-2.0-pro": "Modelo do Google com forte raciocínio e capacidades multimodais, capaz de análise de imagem.", "gemini-1.5-pro": "Modelo do Google com forte raciocínio e capacidades multimodais, capaz de análise de imagem.", "gpt-4o": "Modelo GPT-4.1 da OpenAI, possuindo uma vasta base de conhecimento e excelente compreensão da linguagem.", "llama-3.1-70b-versatile": "Modelo de código a<PERSON>, conhecido por sua rápida velocidade de geração.", "gemini-thinking": "Modelo de Pensamento Gemini do Google, se destaca em raciocínio através de problemas complexos e resolução deles.", "gemini-2.5-flash": "Última versão experimental de raciocínio rápido do Google Gemini 2.5, oferecendo velocidade de geração mais rápida e análise de imagem, suporte para pesquisa na web", "gemini-2.0-flash": "Última versão de raciocínio rápido do Google Gemini 2.0, oferecendo velocidade de geração mais rápida e análise de imagem, suporte para pesquisa na web", "gemini-1.5-flash": "Uma versão de raciocínio rápido do Google Gemini, oferecendo velocidade de geração mais rápida e análise de imagem.", "gpt-4o-mini": "Uma versão leve do GPT-4.1, equilibrando velocidade e desempenho.", "claude-sonnet": "Modelo Claude Sonnet 4 da Anthropic, capaz de resolver problemas complexos e analisar imagens.", "claude-3-haiku": "Modelo Claude 3 Hai<PERSON> da <PERSON>ic, adequado para problemas complexos gerais, velocidade de geração rápida, suporte a longos contextos e análise de imagem.", "llama3-70b-8192": "Modelo Llama 3 da Meta, oferecendo velocidade de geração rápida.", "llama-3.2-90b-vision-preview": "Modelo de código a<PERSON>, oferecendo velocidade de geração rápida e análise de imagem.", "llama-3.2-11b-vision-preview": "Modelo de código a<PERSON>, oferecendo velocidade de geração rápida e análise de imagem.", "deepseek-v3": "Modelo DeepSeek-V3 de código aberto, com velocidade de geração rápida e fortes capacidades generativas", "deepseek-r1": "Modelo DeepSeek-R1 de raciocínio de código aberto, se destaca no raciocínio e resolução de problemas complexos"}, "privileges": {"rils": "Número de artigos salvos para LerDepois", "rilsRead": "Gerenciar artigos lidos do LerDepois", "rilsTrash": "Gerenciar artigos deletados do LerDepois", "notesTrash": "Gerenciar notas instantâneas deletadas", "media_count": "Número de imagens enviadas", "tts_voices": "Vozes premium de texto para fala", "mediaCollection": "Gerenciar coleção de mídia enviada", "blocks": "Número de blocos", "members": "Número máximo de usuários no espaço de trabalho", "askAI": "Número de solicitações ao assistente FunBlocks AI", "usePrivateAIApi": "Suporte para acesso direto à API LLM", "privatePrompts": "Número de prompts privados", "limited_ai_features": "Limitar recursos de IA", "generateCard": "<PERSON><PERSON><PERSON>", "memos": "Memorandos", "free_trial": "Teste gratuito", "true": "<PERSON>m", "false": "Não", "unlimited": "<PERSON><PERSON><PERSON><PERSON>", "aicoins": "30 vezes de testes", "daily_quota": "Cota diária", "notes": "1. Exceto pelas notas de bloco e funções de IA, as principais funções da FunBlocks são atualmente gratuitas;\n2. Se você gosta das funções de IA, é recomendável comprar a assinatura VIP de IA diretamente."}, "uninstall_reasons": {"no_required_feature": "A extensão não possui certos recursos que eu preciso.", "have_better_choice": "Encontrei outro aplicativo ou serviço que atende melhor às minhas necessidades.", "encounter_issues": "Encontrei problemas ou dificuldades ao usar a extensão.", "do_not_like": "Não estou satisfeito com a experiência do usuário ou o design da interface da extensão.", "no_requirement": "De<PERSON><PERSON> de experimentar, não encontrei recursos que me fizessem continuar usando.", "others": "Nenhuma das opções acima cobre meu motivo de desinstalação."}, "uninstall_reasons_detail_hint": {"no_required_feature": "Por favor, descreva os recursos que você mais desejava.", "have_better_choice": "Por favor, liste os produtos ou soluções alternativas que você está usando atualmente.", "encounter_issues": "Por favor, descreva os problemas ou dificuldades que você encontrou ao usar a extensão.", "do_not_like": "Por favor, liste as <PERSON><PERSON><PERSON> onde você sente que a experiência do usuário ou o design da interface são insatisfatórios.", "no_requirement": "Você poderia descrever os recursos que você continuaria a usar?", "others": "Por favor, liste quaisquer outros motivos específicos para sua desinstalação."}}