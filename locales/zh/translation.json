{"phone": "手机", "email": "邮箱", "somebody_workspace": "{{nickname}}的工作空间", "somebody_writingspace": "{{nickname}}的空间", "powered_by": "本视频由xSlides生成", "db_new_property_name": "标题", "db_new_property_status": "状态", "db_new_property_createdAt": "创建时间", "db_new_property_url": "文章链接", "db_new_property_pin": "置顶", "default_status_name": "状态", "default_date_name": "日期", "db_view_table": "表格视图", "db_view_board": "看板视图", "db_view_list": "列表视图", "db_view_gallery": "画廊视图", "db_view_timeline": "时间视图", "db_view_chart": "统计视图", "db_new_link_to_view": "链接视图", "db_property_default_label_prefix": "字段 ", "untitled_new_db": "新数据库", "untitled_new_slides": "新幻灯片", "untitled_new_flow": "新 AIFlow", "untitled_new_doc": "新页面", "untitled_db": "无标题数据库", "untitled_slides": "无标题幻灯片", "untitled_flow": "无标题 AIFlow", "untitled_doc": "无标题页面", "seminar_powered_by": "本会议由FunBlocks提供技术支持", "present_powered_by": "本幻灯片由 FunBlocks AI Slides 技术支持", "seminar_slides_slogan": "@FunBlocks — AI 加持的幻灯片创作，Markdown 带来的丝滑编辑体验", "present_slides_slogan": "@FunBlocks — AI 加持的幻灯片创作，Markdown 带来的丝滑编辑体验", "todo": "未开始", "doing": "进行中", "done": "已完成", "api_level_1": "高级 AI 模型", "api_level_2": "标准 AI 模型", "quota_model_level_1": "每日高级 AI 模型访问次数", "quota_model_level_2": "每日标准 AI 模型访问次数", "quota_whiteboards": "可创建的白板数量", "quota_flow_nodes": "每个白板可保存的节点数量", "message": {"user_not_exist": "用户不存在或密码错误", "user_existed": "用户已存在", "verification_code_error": "验证码错误或已过期", "oauth_user_unmatched": "验证用户信息不一致", "too_frequent": "请求过于频繁", "verification_code_sent": "验证码已发送", "sms_daily_limit": "每个手机号每天只能发送两次验证码", "sms_service_error": "短信服务异常", "activation_email_sent": "激活邮件已发送，请注意查收", "keep_permission_to_operator": "保持自己对文档的所有权限", "same_version": "和当前文档同一版本", "version_not_found": "版本不存在", "org_not_exist_or_no_access": "该空间不存在或您无权限访问", "content_no_access": "无权限访问该内容，请先登录FunBlocks或联系内容作者", "operation_failed": "操作失败，请稍后重试", "operation_success": "操作成功", "server_error": "服务器错误", "recaptcha_verification_failed": "ReCAPTCHA 验证失败"}, "notification": {"verification_email_subject": "您正在注册FunBlocks账号，请验证您的邮箱", "verification_email_text": "您好, \n\n{{account}} 正用于注册FunBlocks账号. 点击或拷贝如下链接到浏览器地址栏激活您的FunBlocks账号 {{activate_url}} \n如果不是您本人注册FunBlocks账号，请直接忽略本邮件. \n\n祝万事如意,\nFunBlocks团队", "verification_email_html": "您好, <br/><br/>{{account}} 正用于注册FunBlocks账号. 点击如下链接激活您的FunBlocks账号, <a href='{{activate_url}}'>activate</a>, <br/>或拷贝如下链接并在浏览器地址栏打开: {{activate_url}} <br/>如果不是您本人注册FunBlocks账号，请直接忽略本邮件. <br/><br/>祝万事如意,<br/>FunBlocks团队", "verification_vcode_subject": "来自FunBlocks的验证码", "verification_vcode_text": "尊敬的{{account}}：\n感谢您选择使用我们的服务。为了保证您的账户安全，我们需要对您的身份进行验证。\n您的验证码是：{{token}}\n请在24小时内输入验证码完成验证。请注意，该验证码仅可用于本次验证，不可重复使用。\n如果您没有进行任何操作却收到了此邮件，请忽略本邮件。如果您有任何疑问或需要帮助，请随时联系我们的客户服务团队。\n再次感谢您的选择。\n\n祝好！\nFunBlocks团队", "verified_title": "FunBlocks账号注册成功", "failed_verify_title": "FunBlocks账号注册失败", "callback": "点击进入 {{app}}，开启智慧之旅！", "token_not_exists": "验证码/链接不存在或已过期，请打开FunBlocks应用，重新注册或发送验证链接", "doc_invitation_email_subject": "{{username}}从 {{org}} 分享给您一个文档", "doc_invitation_email_text": "您好, \n{{username}} 从{{org}}分享给您一个文档: {{pageTitle}}. 点击或拷贝如下链接到浏览器地址栏查看文档 {{url}} .\n 如果您还没有FunBlocks账号，请点击或拷贝如下链接注册: {{regUrl}} \n\n祝万事如意,\nFunBlocks团队", "doc_invitation_email_html": "您好, <br/>{{username}} 从{{org}}分享给您一个文档: {{pageTitle}}. 点击如下链接查看文档, <a href='{{url}}'>{{pageTitle}}</a>, <br/>或拷贝如下链接并在浏览器地址栏打开: {{url}} <br/> 如果您还没有FunBlocks账号，请点击 <a href='{{regUrl}}'>注册FunBlocks账号</a> <br/><br/>祝万事如意,<br/>FunBlocks团队", "doc_invitation_sms_text": "{{username}} 从{{org}}分享给您一个文档: {{pageTitle}}，打开链接查看: ", "org_invitation_email_subject": "{{username}}邀请您加入FunBlocks空间 {{org}}", "org_invitation_email_text": "您好, \n{{username}} 邀请您加入FunBlocks空间 {{org}}. 点击或拷贝如下链接到浏览器地址栏加入空间 {{url}}\n\n祝万事如意,\nFunBlocks团队", "org_invitation_email_html": "您好, <br/>{{username}} 邀请您加入FunBlocks空间 {{org}}. 点击如下链接加入空间, <a href='{{url}}'>{{org}}</a>, <br/>或拷贝如下链接并在浏览器地址栏打开: {{url}} <br/><br/>祝万事如意,<br/>FunBlocks团队", "org_invitation_sms_text": "{{username}} 邀请您加入FunBlocks空间 {{org}}，打开链接加入空间: ", "invite_friends_desc": "快乐与朋友分享。邀请你的好友使用FunBlocks AI {{app}}，一起享受AI助手带来的高效助力吧！\n\n每邀请一个朋友，即可获得 {{coins}} 个AI币！\n\n您目前AI币余额为：{{balance}}", "invite_friends_msg_extension": "我刚刚找到了一个超级酷的浏览器插件，叫做FunBlocks AI。它可以帮你总结网页内容，修正文本和语法错误，一键回复邮件，还可以写各种文案！绝对值得一试！\n\n点击链接安装浏览器插件，即可享有 {{coins}} 次 免费 AI服务：", "invite_friends_msg_flow": "告别枯燥的对话式 AI，FunBlocks 用思维导图让你和 AI 互动更直观！🧠 \n\n💡 自由扩展想法，轻松连接思路 \n💪 主流 LLM 模型加持，信息检索、内容生成、问题解决都搞定 \n🚀 效率提升看得见，再复杂的项目也不怕\n\n快来一起玩转 AI 吧！注册即可享有 {{coins}}次 免费 AI服务 😉", "invite_friends_msg_app": "我刚刚发现了一个超级酷的工具，FunBlocks AI，能一键生成文档和PPT，修改和优化作文，续写故事，还能生成大纲和进行头脑风暴等！值得一试！\n\n立即注册享有 {{coins}}次 免费 AI服务"}, "ai_items": {"review_group": "编辑或审核选定内容", "generate_group": "从给定内容生成", "write_group": "用 AI 写作", "draft_group": "用 AI 起草", "slides_group": "用 AI 写幻灯片", "workspace_prompts_group": "空间共享指令", "pinned_prompts_group": "自选指令", "user_prompts_group": "自定义指令", "add_prompt": "添加自定义指令", "writing_tutor": "写作教练", "writing_teacher": "批改作文", "reflect": "反思", "memo_maestro": "整理会议记录", "memo_maestro_image": "整理会议记录", "story_image": "根据图片讲个故事", "image_caption": "生成图片标题", "central_ideas": "中心思想", "query": "作为咨询内容发给AI助手", "optimize": "润色", "rewrite": "优化重写", "spelling": "修改拼写和语法错误", "translate_to_en": "翻译成英文或润色英文文本", "continue": "继续写作", "extend": "拓展/加长文本", "short": "缩短文本", "tone": "变换语气", "summary_text": "总结", "title": "生成标题", "slide": "生成当前页内容", "slideshow": "生成多页幻灯片", "speechscript": "生成内容讲解", "todos": "抽取待办事项", "flow_todolist": "生成To-Do清单", "flow_task_analysis": "项目分析", "flow_infograph": "生成信息图", "flow_flowchart": "生成流程图", "cornell_notes": "生成Cornell笔记", "flow_image_prompt": "生成图片", "generate_image": "生成图片", "summary": "摘要", "summary_keypoints": "总结要点", "bullet": "生成大纲", "keypoints": "生成要点", "speakernotes": "演讲者备注", "speech_suggestions": "演讲建议", "slides_optimize": "幻灯片优化", "highlights": "看点", "share_tips": "写一段社交媒体分享文字", "ask_questions": "问几个相关问题", "related_questions_topics": "相关问题和主题", "image_insights": "图片关键主题", "breakdown": "拆解主题或想法", "proof": "给出论证", "positive_cases": "正面案例", "negative_cases": "反面案例", "contradict": "反方观点", "fallacy": "找出谬误或偏见", "related_knowledges": "如何了解相关知识", "simplify": "以更简单的语言重写", "topicspeech": "主题讲解", "explain": "解释", "draft": "帮我写一篇...", "draft_prompt": "根据如下描述，写一篇{content_type}：", "reply": "回复", "brainstorming": "头脑风暴", "flow_brainstorming": "头脑风暴", "flow_mentalmodel": "心智模型", "flow_subtopic_brainstorming": "拓展想法", "flow_mindmap": "生成思维导图", "flow_book_mindmap": "生成思维导图", "flow_movie_mindmap": "生成思维导图", "flow_decision_analysis": "决策分析", "flow_read_mindmap": "书籍导读", "flow_dream_analysis": "解梦师", "flow_art_insight": "艺术鉴赏", "flow_art_image_insight": "艺术鉴赏", "flow_photography_coach": "摄影教练", "email_outline": "来信意图", "message_outline": "原始信息要点分析", "reply_email": "生成回复邮件", "quora_reply": "回答问题", "twitter_reply": "回复", "social_post_reply": "撰写评论", "producthunt_voter_comment": "撰写评论", "producthunt_maker_reply": "撰写回复", "read_email": "阅读邮件", "outline": "提纲", "reply_outline": "回复要点", "refine_question": "求助帖", "explain_codes": "解释", "critial_analysis": "批判性分析", "describe_image": "图片内容描述", "describe_image_mindmap": "图片内容导图", "translate_image": "翻译", "homework": "解决这道家庭作业问题", "image_empathetic_reply": "生成高情绪价值回复", "witty_insights": "妙评", "image_avatar": "风格迁移", "xSlides": "生成 PPT", "fix_codes_bug": "自动修复代码问题", "improve_codes": "修改或优化", "lesson_plans": "生成教案", "dok_assessment": "生成DOK评估题目", "teaching_slides": "生成教学幻灯片", "tone_professional": "专业", "tone_casual": "随意", "tone_straightforward": "直截了当", "tone_confident": "充满信心", "tone_friendly": "友好热情", "tone_academic": "学术风格", "tone_enthusiastic": "热情洋溢", "tone_empathetic": "富有同理心", "chart": "信息图形", "chart_infographics": "信息图", "chart_flowchart": "流程图", "chart_sequencediagram": "时序图", "chart_quadrant": "四象限图", "chart_timeline": "时间线", "chart_cornell_notes": "康奈尔笔记", "chart_slide": "单页幻灯片", "expand_ideas": "扩展想法", "expand_ideas_brainstorm": "头脑风暴", "expand_ideas_breakdown": "继续分解", "expand_ideas_first_principle": "第一性原理", "expand_ideas_five_whys": "找问题根因(5Whys)", "expand_ideas_scamper": "创意思考(SCAMPER)", "expand_ideas_problem_rephrazing": "问题重述", "expand_ideas_changing_perspectives": "转换视角", "expand_ideas_reverse_thinking": "逆向思维", "expand_ideas_pros_cons": "利弊分析", "expand_ideas_5w1h": "5W1H", "expand_ideas_role_storming": "角色头脑风暴", "expand_ideas_triz": "创新解题法(TRIZ)", "expand_ideas_six_thinking_hats": "六顶思考帽", "expand_ideas_disney_method": "迪士尼法", "expand_ideas_swots": "SWOT 分析", "expand_ideas_value_proposition_canvas": "价值主张图", "expand_ideas_bussiness_model_canvas": "商业模式图", "translate": "翻译成...", "translate_english": "英语", "translate_japanese": "日语", "translate_french": "法语", "translate_spanish": "西班牙语", "translate_latin": "拉丁语", "translate_italian": "意大利语", "translate_russian": "俄语", "translate_portuguese": "葡萄牙语", "translate_korean": "韩语", "translate_arabic": "阿拉伯语", "translate_hebrew": "希伯来语", "translate_dutch": "荷兰语", "translate_german": "德语", "translate_indonesian": "印尼语", "translate_danish": "丹麦语", "translate_swedish": "瑞典语", "translate_finnish": "芬兰语", "translate_turkish": "土耳其语", "translate_polish": "波兰语", "translate_hungarian": "匈牙利语", "translate_czech": "捷克语", "translate_romanian": "罗马尼亚语", "translate_bulgarian": "保加利亚语", "translate_greek": "希腊语", "translate_thai": "泰语", "translate_vietnamese": "越南语", "translate_malay": "马来语", "translate_traditional_chinese": "繁体中文", "translate_mandarin_chinese": "中文", "draft_brainstorming": "头脑风暴", "draft_outline": "提纲/要点", "draft_article": "作文", "draft_blog": "公众号文章", "draft_question": "求助或提问贴", "draft_twitter": "Twitter 推文", "draft_twitter_reply": "Twitter 回复", "draft_weibo": "微博", "draft_xiaohongshu": "小红书", "draft_zhihu": "知乎回答", "draft_wechat_post": "微信朋友圈分享文案", "draft_facebook": "Facebook 时间线贴文", "draft_press_release": "新闻稿", "draft_script": "剧本", "draft_creative_story": "创意故事", "draft_essay": "论文", "draft_presentation": "演讲稿", "draft_poem": "诗歌", "draft_love_letter": "情书", "draft_swot": "SWOT分析", "draft_weekly_report": "周报", "draft_cons_pros": "优缺点分析", "draft_job_description": "职位描述", "draft_sales_email": "销售邮件", "draft_more": "...", "input": "..."}, "sub_task": {"label": "让 AI 生成 {{action_label}}"}, "sub_task_titles": {"email_outline": "邮件内容", "message_outline": "信息内容"}, "label": {"topic": "主题", "outline": "要点或大纲", "email_outline": "来信意图", "message_outline": "写作者意图", "reply_outline": "回复要点", "other_reqs": "其他要求", "target_learners": "目标学习者", "duration": "课时", "educational_model": "教育框架", "art_style": " 艺术风格"}, "hint": {"topic": "主题", "outline": "请提供大纲或要点，尽量详细和充实", "other_reqs": "其他要求，比如语言风格、字数等", "reply_outline": "请提供回复的大纲或要点，尽量详细和充实", "target_learners": "例如，9年级学生，大学生，职场人士等", "duration": "例如，45分钟，90分钟，120分钟等"}, "ai_roles": {"doctor": "医生", "professor": "教授", "philosopher": "哲学家", "psychologist": "心理学家", "relationship_coach": "情感教练", "scientist": "科学家", "historian": "历史学家", "motivational_coach": "鼓励师", "fortune_teller": "算命师", "astrologer": "占星师", "legal_advisor": "法律顾问", "fallacy": "谬误发现者", "debater": "辩论者", "storyteller": "故事大王", "novelist": "小说家", "poet": "诗人", "essay_writer": "论文写作者", "social_writer": "分享文案达人", "question_answer": "问题解答者", "question_writer": "问题撰写者", "motivational_speaker": "励志演讲者", "elocutionist": "演说家", "business_analyst": "商业分析师", "it_expert": "It专家", "spoken_english_teacher": "英语口语教练", "screen_writer": "编剧", "career_counselor": "职业顾问", "personal_trainer": "私人教练", "mental_health_adviser": "心理健康顾问", "academician": "专家学者", "time_travel_guide": "穿越向导", "confucius": "孔子", "lao_tzu": "老子", "zhuangzi": "庄子", "sun_tzu": "孙子", "simaqian": "司马迁", "sushi": "苏东坡", "libai": "<PERSON>白", "wangyangming": "王阳明", "buddha": "佛祖", "jesus": "耶稣", "socrates": "苏格拉底", "aristotle": "亚里士多德", "plato": "柏拉图", "nietzsche": "尼采", "sartre": "萨特", "freud": "弗洛伊德"}, "ai": {"exceed_msg_limit": "看起来您今天的免费 AI 查询次数已用完。为了继续使用我们的服务，您可以：\n\n1. 购买 FunBlocks AI 会员，享受无限次查询。\n2. 设置您自己的 LLM API，直接使用第三方 LLM 服务。\n3. 明天再来，每天可以获得 20 次免费查询。\n\n感谢您的支持！", "text_too_long": "文本超出长度限制", "no_reply": "智能助手未能获得结果，请稍后重试!", "exceed_askai_speed_limit": "超出AI助手回答速度，请稍后再试", "ai_request_timeout": "AI助手长时间未响应，请稍后重试", "content_filter": "内容包含敏感信息，已过滤", "llm_service_failed": "AI模型服务失败，请稍后重试", "welcome_msg": "欢迎来和我聊天！我是一个基于大语言模型的智能助手，可以和您聊天并回答您的问题。由于我的AI技术，我可以进行各种自然语言处理任务，包括文本生成、翻译、问答等。您可以问我任何关于历史、文化、科学、技术和文学等领域的问题。\n\n但是，我也有一些限制。我的答案可能是不准确的，因为我并不是一个人类，只是一个预训练的模型。此外，我不能回答即时性新闻，因为我的知识库只包含到2021年的信息。\n\n举个例子，您可以问我，“爱因斯坦的相对论是什么？”我将使用我的语言理解和知识库来回答您的问题。如果我的回答不能让你满意，您可以尝试提供更具体的问题，这样我可以更好地理解和回答。\n\n我期待着与您的交流！", "coins": "AI币可以支付对AI助手的访问，一次访问消耗一枚AI币。可以购买AI币，也可以通过“邀请好友”等活动免费获得AI币。", "coin": "AI币", "balance": "您的账户目前有 {{balance}} 枚 AI币。", "buy_coin_title": "您可以直接购买AI币：", "earn_coin_desc": "也可以通过邀请好友，获取AI币奖励，每邀请一个好友，获取 {{coins}} 枚AI币：", "exceed_daily_quota": "您是 FunBlocks AI {{level}} 会员，今天的{{model_level}}模型请求次数已用完。请升级会员或选择其他级别模型继续使用。"}, "insights": {"exceed_daily_quota": "您今天的生成次数已用完。请明日再来或升级会员继续使用。", "exceed_free_trial_quota": "您的免费试用额度已用完。请升级成会员继续使用。", "ai_reponse_nothing": "AI没有给出完整回复，请稍后再试", "failed_load_image": "加载图片失败", "invalid_input": "无效输入"}, "llm": {"gemini-2.5-pro": "Google的最新最强大模型,具有强大的推理和多模态能力,可以分析图片", "gemini-2.0-pro": "Google的模型,具有强大的推理和多模态能力,可以分析图片", "gemini-1.5-pro": "Google的模型,具有强大的推理和多模态能力,可以分析图片", "gpt-4o": "OpenAI的GPT-4.1模型,拥有广泛的知识和出色的语言理解能力", "llama-3.1-70b-versatile": "Meta的开源模型,生成速度快", "gemini-thinking": "Google Gemini 思考模型，擅长复杂问题推理、解题", "gemini-2.5-flash": "Google Gemini 2.5的最新、快速推理版本,生成速度快,可以分析图片，支持搜索网络", "gemini-2.0-flash": "Google Gemini 2.0的最新、快速推理版本,生成速度快,可以分析图片，支持搜索网络", "gemini-1.5-flash": "Google Gemini的快速推理版本,生成速度较快,可以分析图片", "gpt-4o-mini": "GPT-4.1的轻量级版本,在速度和性能之间取得平衡", "claude-sonnet": "Anthropic的Claude Sonnet 4模型,解决复杂问题能力强,可以分析图片", "claude-3-haiku": "Anthropic的Claude 3 Haiku模型,适用一般复杂问题,生成速度快,支持长上下文,可以分析图片", "llama3-70b-8192": "Meta的Llama 3模型,生成速度快", "llama-3.2-90b-vision-preview": "Meta的开源模型,生成速度快,可以分析图片", "llama-3.2-11b-vision-preview": "Meta的开源模型,生成速度快,可以分析图片", "deepseek-v3": "DeepSeek开源 V3 模型，生成速度快，具有强大的生成能力", "deepseek-r1": "DeepSeek开源 R1 推理模型，擅长复杂问题推理、解题"}, "privileges": {"rils": "稍后阅读文章数", "rilsRead": "管理稍后阅读已读文章", "rilsTrash": "管理稍后阅读已删除文章", "notesTrash": "管理已删除即时笔记", "media_count": "上传的图片数", "tts_voices": "精品语音合成", "mediaCollection": "管理上传的媒体库", "blocks": "blocks数", "members": "空间中用户数上限", "askAI": "AI助手访问次数", "usePrivateAIApi": "支持第三方AI接口(如OpenAI API)访问", "privatePrompts": "自定义私有Prompt数量", "limited_ai_features": "限制AI功能", "generateCard": "生成卡片", "memos": "备忘录", "free_trial": "免费试用", "true": "是", "false": "否", "unlimited": "不限", "aicoins": "30次试用", "daily_quota": "每日限次", "notes": "1. 除方块笔记和AI特定功能之外，FunBlocks主体功能目前免费使用；\n2. 如果您喜欢AI功能，推荐直接购买AI VIP会员；\n3. 方块笔记试用VIP会员的AI权限等同非会员权限。"}, "uninstall_reasons": {"no_required_feature": "应用缺少我需要的某些功能或特性", "have_better_choice": "发现了其他更适合我需求的应用或服务", "encounter_issues": "在使用应用时遇到了问题或困扰", "do_not_like": "对应用的使用体验或界面设计不够满意", "no_requirement": "尝试之后，没有发现会持续使用的功能", "others": "以上选项无法涵盖我的卸载原因"}, "uninstall_reasons_detail_hint": {"no_required_feature": "请描述一下您最想要的功能", "have_better_choice": "请列出您正在使用的替代产品或方案", "encounter_issues": "请描述您在使用应用时遇到的问题或困扰", "do_not_like": "请列出您认为使用体验或界面设计不够满意的地方", "no_requirement": "请描述一下您会持续使用的功能是什么呢？", "others": "请列出您卸载的其他具体原因"}}