const mongoose = require('mongoose');
const { async } = require('q');
const AIMessage = mongoose.model('AIMessage');
const CONSTANT = require('../utils/constant');
var rp = require('request-promise-native');
const config = require('../../config').config;
const articleController = require('./articleController');
const mediaController = require('./mediaController');
const docController = require('./docController');
const langDetect = require('../utils/langDetect');
const { Article } = require('../../models/Article');
const { RIL } = require('../../models/RIL');

const paymentController = require('./paymentController');
const { slateToMD } = require('../utils/SlateMarkdown');
const { marked } = require('marked');
const { removePTagFromListItem } = require('../utils/SlateHtml');
const { cloneDeep } = require('lodash');
const Prompt = require('../../models/Prompt');
const PinnedPrompt = require('../../models/PinnedPrompt');
const User = require('../../models/User');
const SharedAIContent = require('../../models/SharedAIContent');

const { AnthropicBedrock } = require('@anthropic-ai/bedrock-sdk');
const Artifact = require('../../models/Artifact');
const sharp = require('sharp');
const Like = require('../../models/Like');
const { JSDOM } = require('jsdom');
const { get_youtube_info } = require('../utils/youtubeUtils');
const Doc = require('../../models/Doc');
const { saveMimeData } = require('../utils/fs');
const SlateMarkdown = require('../utils/SlateMarkdown');

const client = new AnthropicBedrock({
    awsRegion: 'us-east-1',

    // awsAccessKey: "********************",
    awsAccessKey: '********************',
    // awsSecretKey: "tK1x1ZzcRpzC0v4Aed5m+wJr5OWjN2pp3xTA6VHr",
    awsSecretKey: 'o7+hKsBIbzS1ZPSuqUpIWjYy4sQmbnLMcbX5MiUE'
    // Add other configuration options as needed
});

const message = async (userId, data) => {
    const message = await new AIMessage({
        userId,
        role: 'user',
        sessionId: data.sessionId,
        isChat: true,
        content: data.message,
        options: data.options
    }).save();

    return message;
}

const initWelcomeMessage = async (userId, req) => {
    await new AIMessage({
        userId,
        role: 'funblocks',
        objType: 'instanote',
        content: req.t("ai.welcome_msg")
    }).save();
}

function truncateMessages(messages, max_length) {
    let totalLength = messages.reduce((sum, message) => sum + (message.content?.length || 0), 0);

    while (totalLength > max_length && messages.length > 0) {
        const removedMessage = messages.shift();
        totalLength -= removedMessage.content.length;
    }

    return messages;
}

const ai_api_models = [{
    id: 'api_level_1',
    type: 'title',
    // label: req.t('api_level_1')
}, {
    id: 'gemini-2.5-pro',
    label: 'gemini-2.5-pro',
    value: 'gemini-2.5-pro',
    capabilities: ['search'],
    // desc: req.t('llm.gemini-1.5-pro-latest'),
    level: 't1'
}, {
    //     id: 'gemini-2.0-pro',
    //     label: 'gemini-2.0-pro',
    //     value: 'gemini-2.0-pro-exp-02-05',
    //     capabilities: ['search'],
    //     // desc: req.t('llm.gemini-1.5-pro-latest'),
    //     level: 't1'
    // }, {
    //     id: 'gemini-2.5-flash-thinking',
    //     label: 'gemini-2.5-flash',
    //     value: 'gemini-2.5-flash',
    //     capabilities: ['search'],
    //     // desc: req.t('llm.gemini-1.5-flash-latest'),
    //     level: 't2'
    // }, {
    id: 'gemini-thinking',
    label: 'gemini-2.5-flash-thinking',
    value: 'gemini-2.5-flash-thinking',
    capabilities: ['search'],
    // desc: req.t('llm.gemini-thinking'),
    level: 't1'
}, {
    id: 'gpt-4o',
    label: 'gpt-4.1',
    value: 'gpt-4.1',
    // desc: req.t('llm.gpt-4o'),
    level: 't1'
}, {
    id: 'claude-sonnet',
    label: 'claude-4-sonnet',
    value: 'claude-4-sonnet',
    // desc: req.t('llm.claude-3-5-sonnet'),
    level: 't1'
}, {
    //     id: 'claude-3-5-sonnet',
    //     label: 'claude-3.5-sonnet',
    //     value: 'claude-3.5-sonnet',
    //     // desc: req.t('llm.claude-3-5-sonnet'),
    //     level: 't1'
    // }, {
    id: 'deepseek-r1',
    label: 'deepseek-r1',
    value: 'deepseek-reasoner',
    // id: 'deepseek-v3',
    // label: 'deepseek-v3',
    // value: 'deepseek-chat',
    // desc: req.t('llm.claude-3-5-sonnet'),
    level: 't1'
}, {
    //     label: 'llama3.1-70b-128k',
    //     value: 'llama-3.1-70b-versatile',
    //     desc: req.t('llm.llama-3.1-70b-versatile'),
    //     level: 't1',
    // },{
    //     label: 'llama3.2-90b-vision',
    //     value: 'llama-3.2-90b-vision-preview',
    //     desc: req.t('llm.llama-3.2-90b-vision-preview'),
    //     level: 't1',
    // }, {
    id: 'api_level_2',
    type: 'title',
    // label: req.t('api_level_2')
}, {
    id: 'gemini-2.5-flash',
    label: 'gemini-2.5-flash',
    value: 'gemini-2.5-flash',
    capabilities: ['search'],
    // desc: req.t('llm.gemini-1.5-flash-latest'),
    level: 't2'
}, {
    id: 'gemini-2.0-flash',
    label: 'gemini-2.0-flash',
    value: 'gemini-2.0-flash',
    // desc: req.t('llm.gemini-2.0-flash'),
    capabilities: ['search'],
    level: 't2'
}, {
    //     id: 'gemini-1.5-flash',
    //     label: 'gemini-1.5-flash',
    //     value: 'gemini-1.5-flash-latest',
    //     // desc: req.t('llm.gemini-1.5-flash-latest'),
    //     level: 't2'
    // }, {
    id: 'gpt-4o-mini',
    label: 'gpt-4.1-nano',
    value: 'gpt-4.1-nano',
    // desc: req.t('llm.gpt-4o-mini'),
    level: 't2'
}, {
    id: 'claude-3-haiku',
    label: 'claude-3-haiku',
    value: 'claude-3-haiku',
    // desc: req.t('llm.claude-3-haiku'),
    level: 't2'
}, {
    id: 'deepseek-v3',
    label: 'deepseek-v3',
    value: 'deepseek-chat',
    // desc: req.t('llm.claude-3-haiku'),
    level: 't2'
    // }, {
    //     label: 'llama3.2-11b-vision',
    //     value: 'llama-3.2-11b-vision-preview',
    //     desc: req.t('llm.llama-3.2-11b-vision-preview'),
    //     level: 't2'
    // }, {
    //     label: 'llama3-70b-8k',
    //     value: 'llama3-70b-8192',
    //     desc: req.t('llm.llama3-70b-8192'),
    //     level: 't2'
    // }, {
    //     type: 'title', label: 'T3 level API'
    // }, {
    //     label: 'mixtral-8x7b-32k',
    //     value: 'mixtral-8x7b-32768',
    //     level: 't3'
}];

const G_Paid_Key = 'AIzaSyBZDpB8_hHI4zlZ2IsKOMBG7i-9rEO9MrA';
const G_Keys = ['AIzaSyCnu_jofeEoFdQ8y07NkhJN-H7jE307nt4', 'AIzaSyCWf_QtLJj9MeVSje01aIi-OyhhtvOp_9U', 'AIzaSyA5FM3_QKfzk2shjQp_CFapXM3hOWhBHU0']

const callGemini = async ({ temperature, prompt, content, objType, image, use_search, modalities }, model, req) => {
    try {
        const isImagenModel = model?.includes('imagen');

        let params;

        if (isImagenModel) {
            params = {
                instances: [{
                    prompt: content
                }],
                parameters: {
                    sampleCount: 1
                }
            }

        } else {
            let messages = [{ parts: [{ text: !prompt ? content : (prompt + '. The given text is: ' + '```' + content + '```') }] }];
            if (!!image) {
                if (Array.isArray(image)) {
                    for (const img of image) {
                        messages[0].parts.push({
                            "inline_data": {
                                "mime_type": "image/jpeg",
                                "data": removeDataURLPrefix(img)
                            }
                        })
                    }
                } else {
                    messages[0].parts.push({
                        "inline_data": {
                            "mime_type": "image/jpeg",
                            "data": removeDataURLPrefix(image)
                        }
                    })
                }
            }

            if (!model) {
                model = 'gemini-2.5-flash';
            }

            params = {
                contents: messages,
                tools: use_search && !model.includes('image') && ai_api_models.find(m => m.value === model)?.capabilities?.includes('search') ? [{ 'google_search': {} }] : undefined,
                generationConfig: {
                    temperature,
                    // responseModalities: modalities ? ['Text'].concat(modalities) : undefined,
                    thinkingConfig: model.includes('gemini-2.5-flash') && !model.includes('image') && !model.includes('thinking') ? { thinkingBudget: 0 } : undefined
                },
            }
        }

        if (model.includes('gemini-2.5-flash-lite')) {
            model = 'gemini-2.5-flash-lite-preview-06-17';
        } else if (model.includes('gemini-2.5-flash') && !model.includes('image')) {
            model = 'gemini-2.5-flash';
        }

        // console.log('messages to Gemini........', model, JSON.stringify(messages, null, 4))
        const key = ['gemini-2.5-pro', 'gemini-2.0-pro-exp-02-05', 'gemini-2.5-flash-image-preview'].includes(model) ? G_Paid_Key : G_Keys[Math.floor(Math.random() * G_Keys.length)]

        //https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=GEMINI_API_KEY

        // 使用 AbortController 控制超时
        const abortController = new AbortController();
        const timeoutId = setTimeout(() => {
            abortController.abort();
        }, 180000);

        let response;

        try {
            response = await rp.post({
                url: `https://generativelanguage.googleapis.com/v1beta/models/${model}:${isImagenModel ? 'predict' : 'generateContent'}?key=${key}`,
                headers: { 'content-type': 'application/json' },
                body: JSON.stringify(params),
                signal: abortController.signal, // 传入 AbortSignal
                time: true, // 记录请求时间
                forever: false, // 不保持连接
                pool: {
                    maxSockets: 10
                }
            });

            // 清除超时定时器
            clearTimeout(timeoutId);

        } catch (requestErr) {
            clearTimeout(timeoutId);

            // 检查是否是 AbortError
            if (requestErr.name === 'AbortError' || requestErr.code === 'ABORT_ERR') {
                throw new Error('Request timeout after 180 seconds');
            }

            throw requestErr;
        }

        console.log('got Gemini response', response)

        if (!response) {
            throw 'no response from LLM'
        }

        response = JSON.parse(response);
        if (response.promptFeedback?.blockReason) {
            throw 'content_filter';
        }

        if (response.candidates && response.candidates[0]?.content?.parts?.length) {
            let message = response.candidates[0].content.parts.map((part, index, parts) => {
                if (!index && model.includes('think') && parts.length > 1) {
                    return null;
                }

                return part.text?.trim();
            }).filter(text => !!text).join('\n\n');

            let mimeContents = (await Promise.all(response.candidates[0].content.parts.map(async (part, index, parts) => {
                if (!part.inlineData?.data) return;

                let file = await saveMimeData(part.inlineData, req);
                file.src = config.media_server + file.path.split(config.mediasPath.substring(1))[1];

                return file;
            }))).filter(file => !!file);

            let groundingMetadata;
            if (response.candidates[0].groundingMetadata?.webSearchQueries) {
                groundingMetadata = {
                    webSearchQueries: response.candidates[0].groundingMetadata?.webSearchQueries
                };
            }

            // console.log('data got from genimi',{ content: message, mimeContents, groundingMetadata })

            return { success: 1, data: { content: message, mimeContents, groundingMetadata } }
        }

        if (response.candidates && response.candidates[0]?.finishReason) {
            throw response.candidates[0].finishReason
        }

    } catch (err) {
        console.log('error', err);
        let errMsg = err.message ? err.message : err.toString();

        console.log('errMsg', errMsg);
        let errorCode = 'llm_service_failed';
        if (errMsg?.toLowerCase()?.indexOf('timedout') > -1 ||
            errMsg?.toLowerCase()?.indexOf('timeout') > -1 ||
            errMsg?.toLowerCase()?.indexOf('abort') > -1) {
            errorCode = 'ai_request_timeout'
        }
        // else {
        //     errorCode = errMsg?.substring(0, errMsg.indexOf(' ') > -1 ? errMsg.indexOf(' ') : errMsg.length); // 提取状态码
        // }

        console.log('errCode', errorCode);

        return { success: 0, description: '操作失败', message: errMsg, code: errorCode };
    }
}

const callClaude = async ({ temperature, prompt, content, objType, image }, model) => {
    if (model === 'claude-3.5-sonnet') {
        model = 'anthropic.claude-3-5-sonnet-20241022-v2:0';
    } else if (model === 'claude-3.7-sonnet') {
        model = 'us.anthropic.claude-3-7-sonnet-20250219-v1:0';
    } else if (model === 'claude-4-sonnet') {
        model = 'us.anthropic.claude-sonnet-4-20250514-v1:0';
        // model = 'us.anthropic.claude-3-7-sonnet-20250219-v1:0';
    } else {
        model = 'anthropic.claude-3-haiku-20240307-v1:0';
    }

    let messages = [{ role: "user", content: !prompt ? content : (prompt + '. The given text is: ' + '```' + content + '```') }];

    if (!!image) {
        messages[0].content = [
            { type: "text", text: messages[0].content },
            {
                type: "image",
                source: {
                    type: "base64",
                    media_type: "image/jpeg",
                    data: removeDataURLPrefix(image)
                }
            }
        ];
    }

    try {
        let response = await client.messages.create({
            model,
            max_tokens: 40000,
            messages
        });

        let content = response?.content[0]?.text;
        console.log('got Claude response', response)

        if (!content) {
            throw 'content_filter';
        }
        return { success: 1, data: { content } }

    } catch (err) {
        console.log('error', err);
        let errMsg = err.message ? err.message : err.toString();

        console.log('errMsg', errMsg);
        let errorCode;
        if (errMsg?.toLowerCase()?.indexOf('timedout') > -1) {
            errorCode = 'ai_request_timeout'
        }

        return { success: 0, description: '操作失败', message: errMsg, code: errorCode };
    }
}

const callAzureOpenAI = async (userId, { temperature = 0.7, prompt, content, isChat, prev_messages, image }, llm_provider, model) => {
    try {
        let messages = [];

        if (prompt) {
            if (llm_provider === 'deepseek' && model?.includes('reasoner')) {
                content = prompt + '\n\nThe given text is:' + '```' + content + '```'
            } else {
                messages.push({
                    role: 'system',
                    content: prompt
                })
            }
        }

        if (image) {
            let user_content = [];

            if (content) {
                user_content.push({
                    type: 'text',
                    text: content
                })
            }

            if (!Array.isArray(image)) {
                user_content.push({
                    type: 'image_url',
                    image_url: {
                        url: `data:image/jpeg;base64,${image}`
                    }
                })
            } else {
                for (const img of image) {
                    user_content.push({
                        type: 'image_url',
                        image_url: {
                            url: `data:image/jpeg;base64,${img}`
                        }
                    })
                }
            }

            messages.push({
                role: 'user',
                content: user_content
            })
        } else {
            if (!isChat) {
                messages.push({
                    role: 'user',
                    content
                });
            } else {
                let chat_messages = truncateMessages((prev_messages || []).concat({ role: 'user', content }), 2000);
                if (chat_messages.length == 0) {
                    throw 'text_too_long';
                }

                messages = messages.concat(chat_messages);
            }
        }

        console.log('messages to chatGPT........', JSON.stringify(messages, null, 4))

        let endpoint;
        let headers = { "Content-Type": "application/json", "api-key": model?.startsWith('gpt-4.1') ? '9Jog7nIySOodtm8VzbSUwha0rMtIXoT5ZXT0aPMyNSUAwQqm1aFlJQQJ99BBACHYHv6XJ3w3AAAAACOGpj7U' : '2ZFRhmrTYI4YSMchYL5fPF1Alh3Bbw5WrDXOCxdObi7GpI6rxposJQQJ99AKACYeBjFXJ3w3AAABACOGb9ky' };
        // let model = "gpt-3.5-turbo";

        if (llm_provider === 'moonshot') {
            endpoint = 'https://api.moonshot.cn/v1/chat/completions';
            headers = { "Content-Type": "application/json", Authorization: 'Bearer sk-HsYboeHx47RGexCdEmeXAxXPUDme2pFSD9NCzeyjoYUbokHG' };
            model = model || ((prompt || '') + content).length < 6000 ? 'moonshot-v1-8k' : 'moonshot-v1-32k';
        } else if (llm_provider === 'groq') {
            endpoint = 'https://api.groq.com/openai/v1/chat/completions';
            headers = { "Content-Type": "application/json", Authorization: 'Bearer ********************************************************' };
        } else if (llm_provider === 'deepseek') {
            endpoint = 'https://api.deepseek.com/chat/completions';
            headers = { "Content-Type": "application/json", Authorization: 'Bearer sk-ef2bdedf48d64de5af33bb5ac8b2d677' }
        } else {
            model = model || 'gpt-4.1-nano';
            // endpoint = `https://fb.openai.azure.com/openai/deployments/${model}/chat/completions?api-version=2023-03-15-preview`;
            endpoint = model?.startsWith('gpt-4.1') ? `https://ai-woodnz20248881ai086264609691.cognitiveservices.azure.com/openai/deployments/${model}/chat/completions?api-version=2025-01-01-preview` : `https://fb.openai.azure.com/openai/deployments/${model}/chat/completions?api-version=2023-03-15-preview`;
            // userId =  undefined
        }

        // 使用 AbortController 控制超时
        const abortController = new AbortController();
        const timeoutId = setTimeout(() => {
            abortController.abort();
        }, 180000);

        try {
            let response = await rp.post({
                // url: `https://funblocks-ai.openai.azure.com/openai/deployments/fbgpt35/chat/completions?api-version=2023-03-15-preview`,
                // headers: { 'content-type': 'application/json', 'api-key': '********************************' },
                url: endpoint,
                headers,
                signal: abortController.signal, // 传入 AbortSignal
                body: JSON.stringify({
                    // model: "gpt-3.5-turbo",
                    model,
                    messages,
                    temperature,
                    user: userId
                }),
                time: true, // 记录请求时间
                forever: false, // 不保持连接
                pool: {
                    maxSockets: 10
                }
            });

            // 清除超时定时器
            clearTimeout(timeoutId);

            console.log('got ChatGPT response', userId, endpoint, response)

            if (!response) {
                throw 'no response from LLM'
            }

            response = JSON.parse(response);

            if (!response?.choices?.length) {
                throw 'no response from LLM'
            }

            if (response.choices[0]?.finish_reason === 'content_filter') {
                throw response.choices[0]?.finish_reason;
            }

            let message = { content: response.choices[0]?.message?.content?.trim() };

            return { success: 1, data: message }

        } catch (requestErr) {
            clearTimeout(timeoutId);

            // 检查是否是 AbortError
            if (requestErr.name === 'AbortError' || requestErr.code === 'ABORT_ERR') {
                throw new Error('Request timeout after 180 seconds');
            }

            throw requestErr;
        }

    } catch (err) {
        console.log('error', err);
        let errMsg = err.message ? err.message : err.toString();

        console.log('errMsg', errMsg);
        let errorCode;
        if (errMsg?.toLowerCase()?.indexOf('timedout') > -1 ||
            errMsg?.toLowerCase()?.indexOf('timeout') > -1 ||
            errMsg?.toLowerCase()?.indexOf('abort') > -1) {
            errorCode = 'ai_request_timeout'
        } else {
            errorCode = errMsg?.substring(0, errMsg.indexOf(' ') > -1 ? errMsg.indexOf(' ') : errMsg.length); // 提取状态码
        }

        console.log('errCode', errorCode);

        return { success: 0, description: '操作失败', message: errMsg, code: errorCode };
    }
}

const callAssistant = async (userId, data, llm_provider = 'OpenAI', llm_model, req) => {
    let response = null;

    console.log('call ai assistant......', llm_provider, llm_model, data);

    if (CONSTANT.isDev) {
        // return callAzureOpenAI(data)
        await new Promise(resolve => setTimeout(resolve, 1800));

        return {
            success: 1,
            data: {
                content: '# reply from AI\n\nThis is text content',
                mimeContents: [{
                    src: "http://localhost:50058/media/0/3/e/d/746e683470faae909bfc3c846359/70e6637f79f848c48dc8b5fa7ca55266.png"
                }]
                // content: 'this is reply from Chatgpt.\nthis is reply from Chatgpt.\nthis is reply from Chatgpt.\n this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, this is reply from Chatgpt, '
                // content: '### Replies:\nThis is reply from ChatGPT.\n\nTodo:\n 1. requirement.\n 2. design.\n 3. code\n'
                // content: '| 年份 | 销量（万辆）|\n|------|--------------|\n| 2018 | 126.1        |\n| 2019 | 110.0         |\n| 2020 | 185.6        |\n| 2021 | 305.2        |\n| 2022 | 427.4        |\n| 2023 | 650.0 （预期）|\n| 2024 | 800.0 （预期）|\n| 2025 | 950.0 （预期）|'
                // content: '| 年份 | 人口增长率 |\n| ---- | ---- |\n| 1949年 | - |\n| 1950年 | 18.50% |\n| 1951年 | 12.57% |'
                // content: `FunBlocks推出智能写作助手，让你的文章快速润色\n\n${new Date()}`
                // content: `{
                //     "generated": {"slides":[{"title": "Slide 1 title", "content": "slide 1 content (with markdown format)", "notes": "slide 1 notes (with markdown format)"}, {"title": "Slide 2 title", "content": "slide 2 content (with markdown format)", "notes": "slide 2 notes (with markdown format)"}]}
                //   }`
                //                 content: `
                // \`\`\`mermaid
                // graph LR
                //     A[用户发起交易] --> B[交易广播到网络]
                //     B --> C[节点验证交易]
                //     C --> D[交易打包成区块]
                //     D --> E[区块添加到链上]
                //     E --> F[交易完成!!!!&&&&&&&&]
                // \`\`\`
                // `
                // infograph

                // <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 800">
                //   <!-- Background -->
                //   <rect x="0" y="0" width="100%" height="100%" fill="#f5f5f5"/>

                //   <!-- Title -->
                //   <text x="50%" y="5%" text-anchor="middle" font-size="24" font-weight="bold" fill="#333">Economic Performance and PMI for September</text>

                //   <!-- Bar Chart for PMI data -->
                //   <text x="15%" y="15%" font-size="18" font-weight="bold" fill="#333">Manufacturing PMI Data</text>
                //   <rect x="15%" y="20%" width="20%" height="3%" fill="#4CAF50"/>
                //   <text x="36%" y="23%" font-size="14" fill="#333">Overall PMI: 49.8%</text>

                //   <rect x="15%" y="28%" width="21%" height="3%" fill="#FF9800"/>
                //   <text x="36%" y="31%" font-size="14" fill="#333">Large Enterprises: 50.6%</text>

                //   <rect x="15%" y="36%" width="19.5%" height="3%" fill="#FFC107"/>
                //   <text x="36%" y="39%" font-size="14" fill="#333">Medium Enterprises: 49.2%</text>

                //   <rect x="15%" y="44%" width="19%" height="3%" fill="#FF5722"/>
                //   <text x="36%" y="47%" font-size="14" fill="#333">Small Enterprises: 48.5%</text>

                //   <!-- Line chart for sector performance -->
                //   <text x="15%" y="55%" font-size="18" font-weight="bold" fill="#333">Sector Performance</text>
                //   <polyline points="15,60 25,55 35,58 45,62 55,63" fill="none" stroke="#2196F3" stroke-width="2"/>
                //   <text x="10%" y="65%" font-size="14" fill="#333">High-tech Manufacturing: 53.0%</text>
                //   <polyline points="15,68 25,64 35,67 45,70 55,69" fill="none" stroke="#673AB7" stroke-width="2"/>
                //   <text x="10%" y="75%" font-size="14" fill="#333">Equipment Manufacturing: 52.0%</text>
                //   <polyline points="15,78 25,73 35,75 45,80 55,79" fill="none" stroke="#FF9800" stroke-width="2"/>
                //   <text x="10%" y="85%" font-size="14" fill="#333">Consumer Goods: 51.1%</text>

                //   <!-- Legend -->
                //   <rect x="75%" y="15%" width="1%" height="3%" fill="#4CAF50"/>
                //   <text x="77%" y="17%" font-size="14" fill="#333">Overall PMI</text>
                //   <rect x="75%" y="20%" width="1%" height="3%" fill="#FF9800"/>
                //   <text x="77%" y="22%" font-size="14" fill="#333">Large Enterprises</text>
                //   <rect x="75%" y="25%" width="1%" height="3%" fill="#FFC107"/>
                //   <text x="77%" y="27%" font-size="14" fill="#333">Medium Enterprises</text>
                //   <rect x="75%" y="30%" width="1%" height="3%" fill="#FF5722"/>
                //   <text x="77%" y="32%" font-size="14" fill="#333">Small Enterprises</text>

                // </svg>`
                //                 content: `<svg viewBox="0 0 800 400" width="800" height="400">

                //     <rect width="100%" height="100%" fill="#f8f9fa"/>


                //     <g transform="translate(400,200)">

                //         <circle cx="0" cy="0" r="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>


                //         <path d="M-60,-104 A120,120 0 0,1 60,-104" fill="none" stroke="#1976d2" stroke-width="3" marker-end="url(#arrowhead)"/>
                //         <path d="M104,-60 A120,120 0 0,1 104,60" fill="none" stroke="#1976d2" stroke-width="3" marker-end="url(#arrowhead)"/>
                //         <path d="M60,104 A120,120 0 0,1 -60,104" fill="none" stroke="#1976d2" stroke-width="3" marker-end="url(#arrowhead)"/>
                //         <path d="M-104,60 A120,120 0 0,1 -104,-60" fill="none" stroke="#1976d2" stroke-width="3" marker-end="url(#arrowhead)"/>


                //         <defs>
                //             <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                //                 <polygon points="0 0, 10 3.5, 0 7" fill="#1976d2"/>
                //             </marker>
                //         </defs>


                //         <text x="0" y="-15" text-anchor="middle" font-size="16" fill="#000">付费压力</text>
                //         <text x="0" y="15" text-anchor="middle" font-size="16" fill="#000">运营责任</text>


                //         <text x="0" y="-80" text-anchor="middle" font-size="14" fill="#333">获取用户</text>
                //         <text x="80" y="0" text-anchor="start" font-size="14" fill="#333">吸引付费</text>
                //         <text x="0" y="80" text-anchor="middle" font-size="14" fill="#333">持续运营</text>
                //         <text x="-80" y="0" text-anchor="end" font-size="14" fill="#333">产品维护</text>
                //     </g>
                // </svg>`
                //                 content: `## Test card

                // This s a test card, amazing

                //                 <svg width=\"440\" height=\"900\" xmlns=\"http://www.w3.org/2000/svg\">\n<rect width=\"100%\" height=\"100%\" fill=\"#f0f0f0\"/>\n\n<text x=\"220\" y=\"40\" font-size=\"24\" text-anchor=\"middle\" fill=\"#333\">现象学解构器</text>\n\n<text x=\"20\" y=\"80\" font-size=\"16\" fill=\"#555\">输入: 美国总统大选</text>\n\n<line x1=\"20\" y1=\"100\" x2=\"420\" y2=\"100\" stroke=\"#999\" stroke-width=\"1\"/>\n\n<text x=\"20\" y=\"140\" font-size=\"18\" fill=\"#333\">本质洞察:</text>\n<text x=\"20\" y=\"170\" font-size=\"16\" fill=\"#555\">权力更迭的制度化仪式，反映民主理念与现实之间的张力</text>\n\n<text x=\"20\" y=\"220\" font-size=\"18\" fill=\"#333\">核心变量:</text>\n<text x=\"20\" y=\"250\" font-size=\"16\" fill=\"#555\">• 选举制度</text>\n<text x=\"20\" y=\"280\" font-size=\"16\" fill=\"#555\">• 候选人</text>\n<text x=\"20\" y=\"310\" font-size=\"16\" fill=\"#555\">• 民意愿</text>\n<text x=\"20\" y=\"340\" font-size=\"16\" fill=\"#555\">• 政治参与</text>\n<text x=\"20\" y=\"370\" font-size=\"16\" fill=\"#555\">• 媒体影响</text>\n\n<text x=\"20\" y=\"420\" font-size=\"18\" fill=\"#333\">推理链:</text>\n<text x=\"20\" y=\"450\" font-size=\"16\" fill=\"#555\">1. 制度设计 → 候选人筛选</text>\n<text x=\"20\" y=\"480\" font-size=\"16\" fill=\"#555\">2. 竞选活动 ↔ 选民接触</text>\n<text x=\"20\" y=\"510\" font-size=\"16\" fill=\"#555\">3. 媒体报道 * 公众舆论</text>\n<text x=\"20\" y=\"540\" font-size=\"16\" fill=\"#555\">4. 投票行为 ≈ 民意表达</text>\n<text x=\"20\" y=\"570\" font-size=\"16\" fill=\"#555\">5. 选举结果 ÷ 社会期望</text>\n\n<line x1=\"20\" y1=\"600\" x2=\"420\" y2=\"600\" stroke=\"#999\" stroke-width=\"1\"/>\n\n<rect x=\"20\" y=\"620\" width=\"400\" height=\"200\" fill=\"none\" stroke=\"#555\" stroke-width=\"2\"/>\n<text x=\"220\" y=\"660\" font-size=\"18\" text-anchor=\"middle\" fill=\"#333\">民主理想与现实困境的辩证统一</text>\n<line x1=\"70\" y1=\"680\" x2=\"370\" y2=\"680\" stroke=\"#555\" stroke-width=\"1\"/>\n<line x1=\"220\" y1=\"680\" x2=\"220\" y2=\"800\" stroke=\"#555\" stroke-width=\"1\"/>\n<text x=\"145\" y=\"720\" font-size=\"16\" text-anchor=\"middle\" fill=\"#555\">理想</text>\n<text x=\"295\" y=\"720\" font-size=\"16\" text-anchor=\"middle\" fill=\"#555\">现实</text>\n<text x=\"145\" y=\"760\" font-size=\"14\" text-anchor=\"middle\" fill=\"#777\">民主参与</text>\n<text x=\"295\" y=\"760\" font-size=\"14\" text-anchor=\"middle\" fill=\"#777\">利益博弈</text>\n<text x=\"145\" y=\"790\" font-size=\"14\" text-anchor=\"middle\" fill=\"#777\">公平公正</text>\n<text x=\"295\" y=\"790\" font-size=\"14\" text-anchor=\"middle\" fill=\"#777\">操纵影响</text>\n\n<line x1=\"20\" y1=\"840\" x2=\"420\" y2=\"840\" stroke=\"#999\" stroke-width=\"1\"/>\n\n<text x=\"220\" y=\"880\" font-size=\"16\" text-anchor=\"middle\" fill=\"#333\">权力的游戏，民主的试炼</text>\n\n</svg>`
                // content: `{
                //     "generated": {"commentary": ["Title", "Line 1...","Line 2...", "Line 3..."]}
                // }`

                //             content: '## 笑点识别关键要素\n' +
                // '\n' +
                // '• **语言层面**\n' +
                // '  - 双关语/谐音词识别\n' +
                // '  - 夸张/荒诞表达\n' +
                // '  - 反讽/戏谑语气\n' +
                // '\n' +
                // '• **情境层面**\n' +
                // '  - 故事转折点\n' +
                // '  - 预期反差\n' +
                // '  - 逻辑冲突点\n' +
                // '\n' +
                // '• **技术实现**\n' +
                // '```mermaid\n' +
                // 'graph TD\n' +
                // '    A[输入文本] --> B[分词与语义分析]\n' +
                // '    B --> C[模式匹配]\n' +
                // '    C --> D{笑点类型判断}\n' +
                // '    D --> E[语言类笑点]\n' +
                // '    D --> F[情境类笑点]\n' +
                // '    D --> G[文化类笑点]\n' +
                // '```\n' +
                // '\n' +
                // '• **判断维度**\n' +
                // '  - 突发性\n' +
                // '  - 意外性\n' +
                // '  - 矛盾性\n' +
                // '  - 夸张程度\n' +
                // '\n' +
                // '• **响应策略**\n' +
                // '  - 笑点前铺垫\n' +
                // '  - 笑点时强化\n' +
                // '  - 笑点后延伸'
                //                 content: `### 教学活动
                // | Marzano 策略                                                 | 时间分配 | 教学方法与策略                                     |          师生互动         |          学习活动          |
                // |:---|:---:|---|---|---|
                // |1|2|3|4|5|
                // |4|5|6|7|8|

                // **遵循 Marzano 教学策略结构:**

                // | **Marzano 策略**                                                 | **时间分配** | **教学方法与策略**                                     |          **师生互动**          |          **学习活动**          |
                // | ------------------------------------------------------------- | :------: | ---------------------------------------------- | ------------------------ | ------------------------ |
                // | **设定目标与提供反馈 (Setting Objectives and Providing Feedback)**      |    5分钟   | 明确告知本节课学习目标，并引导学生思考与旧知识的联系。                     |       教师提问，学生思考并尝试回答。      |  学生阅读并理解学习目标，尝试回忆一次方程的知识。  |
                // | **帮助学生回顾旧知识 (Helping Students Recall New Knowledge)**          |    5分钟   | 通过提问和小组讨论，回顾一次方程的解法和多项式乘法（尤其是分配律）。              |      教师引导，学生分组讨论，分享答案。     |    学生回顾一次方程解法，小组讨论多项式乘法。   |
                // | **呈现新知识 (Presenting New Knowledge)**                           |   10分钟   | 采用讲解、板书和多媒体演示结合的方式，清晰呈现一元二次方程的定义、标准形式和因式分解法的步骤。 |       教师讲解，学生听讲、做笔记。       |     学生听讲并记录关键概念，观察例题演示。    |
                // | **帮助学生练习和巩固 (Helping Students Practice and Deepen Knowledge)** |   15分钟   | 通过例题精讲（“手把手”演示）和变式练习，引导学生独立尝试解题。                | 教师巡视指导，提供即时反馈；学生独立或同伴合作解题。 | 学生完成教师布置的例题练习，并在同伴指导下尝试解题。 |
                // | **应用策略 (Applying Strategies)**                                 |    7分钟   | 提供一些情境问题，引导学生思考如何将所学知识应用到实际问题中，或进行拓展性思考。        |  教师提出开放性问题，学生尝试结合所学知识进行解答。 |   学生思考情境问题，讨论如何用一元二次方程解决。  |
                // | **总结与反思 (Summarizing and Reflecting)**                         |    3分钟   | 引导学生回顾本节课所学内容，总结关键点和难点，并布置课后巩固作业。               |     教师提问，学生主动总结；教师布置作业。    |    学生回顾并总结本节课知识点，记录课后作业。   |

                // ### 所需资源

                // * **材料和参考文献:**

                //   * 课本（人教版数学七年级下册相关章节）
                //   * 练习册或自编习题（包含一元二次方程识别、标准形式转换和因式分解解法的题目）
                //   * 粉笔、白板或投影仪
                // * **技术工具:**

                //   * 电脑、投影仪（用于展示概念图、例题和解题步骤）
                //   * 计时器（用于控制各环节时间）
                // * **所需设备:**

                //   * 教室、桌椅

                // ## 内容结构
                // **方法三：求根公式法**

                // $$
                // \\begin{aligned}
                // 	\\dot{x} & = \\sigma(y-x) \\\\
                // 	\\dot{y} & = \\rho x - y - xz \\\\
                // 	\\dot{z} & = -\\beta z + xy
                // \\end{aligned} 
                // $$

                // 对于一元二次方程 $ax^2 + bx + c = 0$，其求根公式为 $x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$。
                // 在本例中，$a = 1$, $b = -5$, $c = 6$。

                // 一元二次方程的定义和标准形式 ($ax^2 + bx + c = 0$, $a \\ne 0$)

                // 将 a, b, c 的值代入求根公式：
                // $x = frac{-(-5) pm sqrt{(-5)^2 - 4(1)(6)}}{2(1)}$

                // $x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$

                // 计算判别式 $Delta = b^2 - 4ac$：
                // $Delta = (-5)^2 - 4(1)(6) = 25 - 24 = 1$


                // 1. 将 a, b, c 的值代入求根公式：
                //    $x = \\frac{-(-5) \\pm \\sqrt{(-5)^2 - 4(1)(6)}}{2(1)}$
                // 2. 计算判别式 $\\Delta = b^2 - 4ac$：
                //    $\\Delta = (-5)^2 - 4(1)(6) = 25 - 24 = 1$
                // 3. 继续计算 x：
                //    $x = \\frac{5 \\pm \\sqrt{1}}{2}$
                //    $x = \\frac{5 \\pm 1}{2}$
                // 4. 求解两个根：
                //    $x_1 = \\frac{5 + 1}{2} = \\frac{6}{2} = 3$
                //    $x_2 = \\frac{5 - 1}{2} = \\frac{4}{2} = 2$

                // * **关键概念:**

                //   * 一元二次方程的定义和标准形式 ($ax^2 + bx + c = 0$, $a \\ne 0$)
                //   * 方程的系数、常数项 $ax^2 + bx + c = 0$
                //   * 方程的$ax^2 + bx + c = 0$根（解）
                //   * 因式分解法的原理与步骤
                // * **难点:**

                //   * 理解因式分解法与多项式乘法的逆运算关系。
                //   * 掌握因式分解的技巧（如十字相乘法）。
                //   * 区分一元二次方程与其他方程类型。
                // * **内容顺序:**

                //   1. 导入：从已知的一次方程引入新概念。
                //   2. 概念介绍：一元二次方程的定义、标准形式及各项含义。
                //   3. 解法介绍：因式分解法原理与步骤。
                //   4. 练习与巩固：例题讲解与学生练习。
                //   5. 总结与拓展。
                //                 `
                // content: `Generated mindmap
                //         \`\`\`json
                //         {
                //             "generated": {
                //                 "theme": "Mind map theme",
                //                 "central_topic": "Central theme clarified",
                //                 "target_scenario": "Inferred application scenario",
                //                 "key_perspectives": [
                //                     {
                //                         "name": "Primary Perspective 1",
                //                         "branches": [
                //                             {
                //                                 "name": "Secondary Branch 1.1",
                //                                 "branches": [
                //                                     "  Tertiary Branch 1.1.1",
                //                                     "Tertiary Branch 1.1.2"
                //                                 ]
                //                             },
                //                             {
                //                                 "name": "\t\t\tSecondary Branch 1.2"
                //                             }
                //                         ]
                //                     },
                //                     {
                //                         "name": "Primary Perspective 2",
                //                         "branches": [
                //                             {
                //                                 "name": "Secondary Branch 2.1",
                //                                 "branches": [
                //                                     "Tertiary Branch 2.1.1",
                //                                     "Tertiary Branch 2.1.2"
                //                                 ]
                //                             }
                //                         ]
                //                     }
                //                 ],
                //                 "summary_insights": [
                //                     "Insight 1",
                //                     "Insight 2"
                //                 ]
                //             }
                //         }
                //         \`\`\``

                //                 content: `\`\`\`json
                // {
                //     "generated": {
                //         "title": "title for whole slides",
                //         "slides": [
                //             {
                //                 "content": "slide 1 content (with markdown format)",
                //                 "notes": "slide 1 notes (with markdown format)"
                //             },
                //             {
                //                 "content": "slide 2 content (with markdown format)",
                //                 "notes": "slide 2 notes (with markdown format)"
                //             }
                //         ]
                //     }
                // }\`\`\``

                // content: `\`\`\`json
                // {
                //     "generated": {
                //         "summary": "Optimization plans explained concisely",
                //         "keypoints": ["key point 1", "key point 2", ...]
                //     }
                // }
                // \`\`\`
                // `

                // content: `\`\`\`markdown\n## 思维导图工具反思：FunBlocks AIFlow vs ChatGPT\n\n这段笔记主要介绍了 FunBlocks AIFlow，并将其定位为优于 ChatGPT 和传统思维导图工具（如 MindMeister 和 XMind）的 AI 驱动型思维工具。\n\n**核心观点总结：**\n\n1.  **传统工具过时：** 认为 2020 年的思维导图工具已无法满足 2024 年的需求，需要更强大的、支持思考而非仅记录的工具。\n2.  **ChatGPT 思维导图方案效率低下：**  指出 YouTube 上流行的 ChatGPT 生成思维导图方法需要手动复制、导入，缺乏可视化和互动性，比喻为“法拉利引擎装在马车上”。\n3.  **FunBlocks AIFlow 是更优解：**  强调 AIFlow 是 AI 原生解决方案，能作为“智能思考教练”，一键生成思维导图，并整合 SCAMPER 和六顶思考帽等思维框架，帮助用户进行多角度思考。\n4.  **功能演示和用例：**  通过营销策略、主题学习（蛋白质合成）、头脑风暴成果等案例展示 AIFlow 的功能，包括网页内容导入、自动摘要、基于思维框架的扩展、以及将思维导图转化为项目方案、演示文稿等。\n5.  **强调 AI 辅助思考：**  总结 2024 年需要的是能与人共同思考的工具，AIFlow 正是为此而生，并号召用户试用。\n\n**潜在假设、偏见与局限性：**\n\n*   **假设传统工具完全落后：**  认为 MindMeister、Xmind 等工具在 2024 年已经“过时”，可能忽略了这些工具在特定场景下的价值，例如简洁性、易用性或离线功能。\n*   **假设 ChatGPT 不适合思维导图：**  虽然指出了 ChatGPT 的一些缺点，但可能低估了其潜力。例如，可以通过更精细的提示词和工作流程，结合其他工具，更有效地利用 ChatGPT 进行思维导图的辅助生成或内容迭代。\n*   **对 FunBlocks AIFlow 的推崇可能存在偏见：**  作为产品推广视频脚本，内容带有明显的营销倾向，可能会过度强调 AIFlow 的优点，而忽略其潜在的缺点或不足，例如成本、数据隐私、AI 算法的局限性等。\n*   **工具二元对立的简化思维：**  将工具简单划分为“记录工具”（落后）和“思考工具”（先进），可能忽略了工具的本质是辅助思考，关键在于使用者如何运用。任何工具，包括传统思维导图和 ChatGPT，如果使用得当，都可以促进思考。\n\n**引发思考的问题：**\n\n*   传统思维导图工具真的“过时”了吗？在哪些场景下它们仍然有价值？它们的优势是什么？\n*   ChatGPT 在思维导图方面真的如此不堪吗？是否存在更有效利用 ChatGPT 生成思维导图或辅助思考的方法？例如，结合代码解释器或更精细的 Prompt 工程？\n*   过度依赖 AI 进行思维导图和头脑风暴，可能存在哪些潜在风险？是否会限制人的创造力或批判性思维？\n*   FunBlocks AIFlow 与其他 AI 驱动的思维导图工具相比，有哪些独特的优势和劣势？例如，MindManager 的 AI 功能、Miro 的可视化协作能力等。\n*   免费试用期结束后，FunBlocks AIFlow 的定价策略如何？其提供的价值是否能支撑其价格？\n*   FunBlocks AIFlow 如何处理用户数据隐私和安全问题？\n\n**其他视角和方法：**\n\n*   **工具的适用性取决于场景和需求：**  不同的工具适用于不同的任务和用户。传统工具可能适合快速简单的可视化，ChatGPT 可能适合初步 idea 生成或文本大纲，AIFlow 可能适合更复杂的、AI 辅助的头脑风暴和项目开发。\n*   **混合方法：**  可以考虑结合不同工具的优势，例如先用 ChatGPT 进行初步发散，再用传统工具或 AIFlow 进行可视化和结构化。\n*   **关注用户真实需求：**  用户目标是仅仅可视化想法，还是深入探索和发展想法？最佳工具应服务于具体目标。\n*   **批判性评估 AI 工具的宣传：**  “思维框架的整合”是否真正深入有效？AI 是否真的能作为“智能思考教练”，还是仅仅是自动化了一些任务？\n\n**进一步探索方向：**\n\n*   深入研究各种思维导图工具，包括传统工具和 AI 驱动工具，对比其功能、价格和用户评价。(例如：MindMeister, Xmind, Miro, Coggle, MindManager, MindPalace, Wisemapping AI 等)\n*   探索更高级的 ChatGPT Prompt 工程技巧，用于思维导图生成和创意性任务。\n*   学习 SCAMPER 和六顶思考帽等思维框架的理论和实际应用，不仅仅局限于思维导图工具。\n*   研究 AI 在创意和思考过程中的伦理考量和潜在偏见。\n\n**建设性反馈：**\n\n*   **优点：**\n    *   清晰地指出了问题（ChatGPT 手动思维导图的低效）。\n    *   提出了解决方案 (FunBlocks AIFlow) 并有明确的价值主张。\n    *   运用了生动的类比 (法拉利引擎和马车)。\n    *   用例演示有效。\n    *   结尾有明确的行动号召。\n*   **改进方向：**\n    *   对 ChatGPT 和传统工具的批评过于绝对，缺乏客观性。\n    *   呈现了较为片面的、带有推广倾向的视角。\n    *   可以更细致地对比不同工具和方法的优劣。\n    *   应提及 FunBlocks AIFlow 的潜在缺点和局限性。\n    *   可以更深入地介绍 AI 功能和思维框架的具体应用方式。\n\n总而言之，这段笔记有效地推广了 FunBlocks AIFlow，但也需要更全面和批判性的视角来审视思维导图工具的选择和 AI 在辅助思考中的作用。工具本身是中性的，关键在于我们如何根据自身需求和场景，明智地选择和使用它们。\n\`\`\``,

                // content: `generated tasks
                // \`\`\`json
                // {
                //     "generated":  {
                //         "items": [
                //             {
                //                 "description": "Subtask 1 description",
                //                 "priority": "high/medium/low"
                //             },
                //             {
                //                 "description": "Subtask 2 description", 
                //                 "priority": "high/medium/low"
                //             }
                //         ]
                //     }
                // }
                // \`\`\`` 

                // content: `## AI History Timeline\n\nThis timeline presents a chronological overview of key events in the history of Artificial Intelligence.\n\n\`\`\`mermaid\ntimeline\n    title AI History\n        1943 : Warren McCulloch and Walter Pitts publish \"A Logical Calculus of Ideas Immanent in Nervous Activity\" : Proposing the first mathematical model for neural networks.\n        1950 : Alan Turing publishes \"Computing Machinery and Intelligence\" : Proposing the Turing Test.\n        1956 : Dartmouth Workshop : Considered the birth of AI.\n        1957 : Frank Rosenblatt creates the Perceptron : An early artificial neural network.\n        1966 : ELIZA chatbot developed : Demonstrating natural language processing.\n        1972 : MYCIN expert system developed : Assisting in medical diagnosis.\n        1980s : Expert systems gain popularity\n        1997 : Deep Blue defeats Garry Kasparov : A milestone in computer chess.\n        2000s: Machine learning sees increased development\n        2011 : IBM's Watson wins Jeopardy!\n        2012 : AlexNet achieves breakthrough in image recognition : Ushering in the deep learning era.\n        2014 : Eugene Goostman chatbot passes Turing test in a limited setting.\n        2015-Present : Deep learning revolutionizes various fields: including natural language processing, computer vision, and robotics.\n        2022-Present : Large Language Models: Rise of transformer models.\n\`\`\`\n`

                //                 content: `## Flow chart

                // This is a flowchart

                // \`\`\`mermaid
                // flowchart TD
                //     A[Start Photosynthesis] --> B[Sunlight Absorption Chlorophyll absorbs light energy]
                //     B --> C[Water Uptake through roots]
                //     C --> D[Carbon Dioxide Intake through stomata]
                //     D --> E{Light-Dependent Reactions}
                //     E -- Water split into Oxygen Protons Electrons --> F[Oxygen Released]
                //     E -- Protons Electrons used to generate ATP NADPH --> G[ATP NADPH Created]
                //     G --> H{Light-Independent Reactions Calvin Cycle}
                //     H -- ATP NADPH convert Carbon Dioxide into Glucose --> I[Glucose Created]
                //     I --> J{Glucose Storage and Use}
                //     J -- Energy --> K[Glucose Used for Energy]
                //     J -- Storage --> L[Glucose Stored as Starch]
                //     J -- Building other molecules --> M[Glucose Used to build Cellulose]
                //     K --> N[End Photosynthesis]
                //     L --> N
                //     M --> N

                // \`\`\``
                // content: `除此之外，FunBlocks智能助手还可以根据我们的写作风格和主题，自动调整语言模式和语气，使得文章更符合读者的阅读习惯。\n\n通过使用FunBlocks智能助手，我们可以更加专注于文章内容的创作，而不用担心繁琐的语言细节和错误。这也让我们更有信心地完成写作任务，并且可以更快地将文章发布到网站和社交媒体上。\n\n总之，FunBlocks的智能写作助手是一个非常强大和实用的工具，可以提高我们的创作效率和文章质量。相信在未来的日子里，它会不断地优化和升级，为我们带来更多惊喜和帮助。`
                // content: '# To-Do List\n' +
                //     '\n' +
                //     "1. Test\nTest FunBlocks' new intelligent writing assistant \n\n" +
                //     '2. Utilize the ChatGPT API to write high-quality articles quickly and accurately\n\n' +
                //     '3. Try out the functions available with the FunBlocks intelligent assistant:\n\n' +
                //     '  - continuation writing\n' +
                //     '  - summary writing\n' +
                //     '  - sentence optimization\n' +
                //     '  - automatic detection of grammar errors\n' +
                //     '4. Use the writing assistant to draft outlines, brainstorm ideas, write social media posts, weekly reports, emails, papers, etc.Use the writing assistant to draft outlines, brainstorm ideas, write social media posts, weekly reports, emails, papers, etc.\n' +
                //     "5. Ask FunBlock's intelligent assistant any questions to receive optimal answers\n" +
                //     '6. Enjoy the benefits of using the writing assistant to achieve better writing outcomes'
                // content: `{
                //     "title": "罗刹国",
                //     "content": [
                //     "1. 定义与起源",
                //     "2. 罗刹的特征",
                //     "3. 在文学与文化中的影响",
                //     "4. 现代意义与应用"
                //     ],
                //     "notes": {
                //     "1. 定义与起源": "罗刹国是古印度文学中的一个重要概念。它起源于佛教经典中的描写，描述了一个极端邪恶与黑暗的虚构之地。罗刹一词最初指的是一类恶鬼或恶魔。",
                //     "2. 罗刹的特征": "罗刹国被描述为一个充满邪恶和恐怖的地方。其中的罗刹通常拥有多个头颅和肢体，具有强大的恶意力量。在佛教故事中，罗刹常常是与善良之人对立的存在。",
                //     "3. 在文学与文化中的影响": "罗刹国作为一个神秘与恐怖的概念，出现在古代印度文学、佛教故事以及民间传说中。它象征着邪恶与人性黑暗的一面，同时也反映了古代印度人对未知与超自然力量的想象。",
                //     "4. 现代意义与应用": "在现代，罗刹国这一概念仍然被用于文学、电影、游戏等创作中。它常常被当作一个引人入胜的题材，用于创造悬疑、恐怖的情节。此外，罗刹国也在某种程度上成为对邪恶和黑暗的象征，被引用于描述社会或个人中的极端邪恶行为。"
                //     }
                //     }`
                // content: `{"slides":[{"title": "上帝死了", "content": "一、尼采对上帝的声明\\n\\n- 尼采宣称"上帝已死"是他哲学思想中的核心，引起了广泛的争议。\\n\\n- 对尼采而言，上帝是人类自由和自主性的天敌，让人类追求力量和进化变得不可能。"}]}`
                // content: `{
                //     "generated": {
                //         "title": "title for whole slides",
                //         "slides": [
                //             {
                //                 "content": "slide 1 content (with markdown format)",
                //                 "notes": "slide 1 notes (with markdown format)"
                //             },
                //             {
                //                 "content": "slide 2 content (with markdown format)",
                //                 "notes": "slide 2 notes (with markdown format)"
                //             }
                //         ]
                //     }
                // }`
            }
        }
    }


    if (llm_provider == 'Gemini' && config.GeminiEnabled) {
        return callGemini(data, llm_model, req);
    } else if (llm_provider == 'Claude') {
        return callClaude(data, llm_model);
    }

    return callAzureOpenAI(userId, data, llm_provider, llm_model);
}

const getTodayMsgCount = async (userId, model_level) => {
    let query = {
        userId,
        role: 'assistant',
        model_level,
        withCoins: false,
        createdAt: {
            $gte: new Date(new Date().setHours(0, 0, 0, 0)),
            $lt: new Date(new Date().setHours(23, 59, 59, 999))
        }
    };

    return await AIMessage.count(query);
}

const getAIProvider = async (userId, privilege, model_level = 't2') => {
    let msgs_today;
    let useCoin;
    const balance = await paymentController.getAICoinBalance(userId);

    // return {
    //     err: 'exceed_msg_limit'
    // }
    // console.log('user privileges...............', JSON.stringify(privilege, null, 4))

    if (privilege.desc == 'aicoins') {
        if (balance < 1) {
            return { err: 'exceed_msg_limit' }
        } else {
            useCoin = true;
        }
    } else if (privilege.desc == 'daily_quota') {
        msgs_today = await getTodayMsgCount(userId, model_level);

        // console.log('msgs today............', msgs_today, privilege[model_level]);
        if (!privilege[model_level] || msgs_today >= privilege[model_level]) {
            if (balance < 1) {
                return {
                    err: 'exceed_daily_quota'
                }
            } else {
                useCoin = true;
            }
        }
    }

    return { useQuota: true, useCoin };
}

const chargeAICoins = async (aiProvider, msgData, req) => {
    if (msgData.role == 'assistant' && aiProvider.useCoin) {
        await paymentController.aiCoinTransaction({ userId: msgData.userId, coins: msgData.model_level === 't1' ? -5 : -1, activity: 'cost' }, req);
    }
}

const getPrevMsgs = async (userId, options, count) => {
    let selector = {
        userId,
        isChat: true,
        createdAt: { $gte: new Date(new Date().getTime() - 60 * 60 * 1000) },
        role: { $in: ['user', 'assistant'] }
    }

    let messages = await AIMessage.find(selector).sort({ createdAt: -1 }).limit(10).lean();
    let prevMsgs = [];

    for (let message of messages) {
        if (message.options?.role != options?.role || !message.content) {
            return prevMsgs;
        }

        if (prevMsgs.length >= count) {
            return prevMsgs;
        }

        if (message.role === 'assistant') {
            let user_message = messages.find(msg => msg.role === 'user' && msg.sessionId === message.sessionId);
            if (user_message) {
                prevMsgs.unshift({
                    role: 'assistant',
                    content: message.content
                });
                prevMsgs.unshift({
                    role: 'user',
                    content: user_message.content
                });
            }
        }
    }

    return prevMsgs;
}

const askAI = async (userId, data, req) => {
    const { sessionId, options } = data;

    let action = 'askAI';
    let msgData = {
        userId,
        action,
        role: 'funblocks',
        sessionId,
        options,
        isChat: true,
        content: req.t('ai.text_too_long')
    };
    let exceed_msg_limit = false;

    const privilege = await paymentController.getServingPrivilege(userId, 'askAI');
    const aiProvider = await getAIProvider(userId, privilege);
    if (aiProvider?.err) {
        msgData.content = req.t('ai.exceed_msg_limit');

        exceed_msg_limit = true;
    }

    let msg = data.message;
    let prompt;

    if (options?.role) {
        let lng = await getLng(msg)

        prompt = config.appConfig.assistant_items.find(item => item.objTypes.includes('askAI') && item.action === 'roleplay' && item.role === options.role)?.prompt;
        if (!prompt) {
            prompt = config.appConfig.character_prompt_template;
            prompt = prompt.replace(/{people}/g, req.t("ai_roles." + options.role, { lng: 'en' }));
            // prompt = prompt.replace(/{people}/g, options.people ? req.t("ai_roles." + options.people, { lng: 'en' }) : '');
        }

        if (prompt) {
            prompt = `${prompt}. Answer in ${lng}`
        }
    }

    let prev_messages = await getPrevMsgs(userId, options, 4);

    if (!exceed_msg_limit && msg.length < 3000) {
        data.message = msg;
        let response = await callAssistant(userId, {
            isChat: true,
            prompt,
            content: data.message,
            prev_messages
        }, 'Gemini', 'gemini-1.5-flash', req);
        msgData.content = response.data?.content;
        if (response.success === 0) {
            msgData.content = req.t('ai.exceed_askai_speed_limit');

            console.log('get error from AI........', response.code)
            //TODO: specific err details
            if (response.code === 'ai_request_timeout') {
                msgData.content = req.t('ai.ai_request_timeout');
            } else if (response.code === 'text_too_long') {
                msgData.content = req.t('ai.text_too_long');
            } else if (response.code = 'content_filter' || response.code === '400') {
                msgData.content = req.t('ai.content_filter');
            }
        } else if (!response.err) {
            msgData.role = 'assistant';
        }
    }

    let message = await new AIMessage(msgData).save();
    message = message.toObject();

    await chargeAICoins(aiProvider, msgData, req);

    return message;
}

function isChinese(str) {
    var reg = /[\u4e00-\u9fa5]/g; //使用Unicode字符集范围来匹配中文字符
    return reg.test(str);
}

const getLng = async (str) => {
    let lng = 'English';
    if (isChinese(str)) {
        lng = 'Chinese';
    }
    // else {
    //     lng = await langDetect.detectLang(str);
    // }

    return lng;
}

const isGeneralAction = (action) => {
    return ['query', 'extend', 'short', 'translate', 'continue'].includes(action);
}


function arraysHaveIntersection(arr1, arr2) {
    const set1 = new Set(arr1);
    return arr2.some(item => set1.has(item));
}


const assistant_item_selector = (item, action, objType, service) => {
    let legal_objTypes = [];
    if (service == 'flow') {
        legal_objTypes = ['flow', 'flow_notes', 'image', 'flow_background']
    }

    return item.action == action && (isGeneralAction(action) || item.objTypes.includes(objType) || arraysHaveIntersection(item.objTypes, legal_objTypes))
}

const removeDataURLPrefix = (dataURL) => {
    if (!dataURL) return '';
    const dataURLRegex = /^data:([^;]+);base64,/;
    return dataURL.replace(dataURLRegex, '');
}

const get_llm_provider = (model) => {
    if (!model) return;

    let llm_provider;
    if (model.startsWith('gpt')) {
        llm_provider = 'OpenAI'
    } else if (model.startsWith('claude')) {
        llm_provider = 'Claude'
    } else if (model.startsWith('gemini') || model.startsWith('imagen')) {
        llm_provider = 'Gemini'
    } else if (model.startsWith('moonshot')) {
        llm_provider = 'moonshot'
    } else if (model.startsWith('moonshot')) {
        llm_provider = 'moonshot'
    } else if (model.startsWith('deepseek')) {
        llm_provider = 'deepseek'
    } else if (model.startsWith('llama3') || model.startsWith('mixtral')) {
        llm_provider = 'groq'
    }

    return llm_provider;
}
const callAIAssistant = async (userId, orgId, data, req) => {
    const { service, action, objType, _id, sessionId, content, hid, draftingType, sub_item, sub_item_more, app, lang, image_url, image, use_search, responseReqs } = data;
    let { model, model_level } = data;

    // if (model === 'gpt-4o-mini') {
    //     model = 'gemini-1.5-flash-latest'
    // } else if (model === 'gpt-4o') {
    //     model = 'gemini-1.5-pro-latest'
    // }

    if (model) {
        model_level = model === 'claude-3.5-sonnet' && 't1' || ai_api_models.find(m => m.value === model)?.level || model_level || 't2'
    }

    let action_gen_image = false;
    if (['generate_image', 'flow_generate_image', 'flow_edit_image', 'flow_edit_group_image_prompt', 'flow_erase_image_watermarks', 'flow_image_editor', 'flow_sketch_art', 'flow_image_avatar', 'image_avatar', 'flow_image_studio', 'flow_mix_images', 'flow_mix_images_breeding'].includes(action)) {
        // model = 'gemini-2.0-flash-preview-image-generation';
        model = 'gemini-2.5-flash-image-preview'
        model_level = 't1'
        action_gen_image = true;
        // if(!action.includes('prompt')) {
        //     model = 'imagen-3.0-generate-002'
        // }
    }

    const privilege = await paymentController.getServingPrivilege(userId, 'askAI');

    const aiProvider = await getAIProvider(userId, privilege, model_level);
    if (aiProvider?.err) {
        return {
            content: objType == 'instatext' ? marked.parse(req.t('ai.exceed_msg_limit')) : req.t('ai.' + aiProvider.err, { level: privilege.servingProduct?.productId, model_level: req.t('api_level_' + model_level.replace('t', '')) }),
            sessionId,
            err: 'exceed_msg_limit',
            servingProduct: privilege.servingProduct
        }
    }

    const appConfig = cloneDeep(config.appConfig);

    let assistant_item;
    if (sub_item) {
        assistant_item = appConfig.assistant_items.find(item => assistant_item_selector(item, sub_item, objType, service));
        if (!assistant_item) {
            assistant_item = appConfig.assistant_items.find(item => assistant_item_selector(item, action, objType, service));
            assistant_item.prompt = assistant_item.prompt?.replace(/{sub_action}/g, sub_item_more || req.t("ai_items." + sub_item, { lng: 'en' }))
        }

    } else {
        assistant_item = appConfig.assistant_items.find(item => assistant_item_selector(item, action, objType, service));
    }

    if (!assistant_item) {
        assistant_item = {
            action
        }
    }

    let err;

    let doc;
    let objContent = '';
    if (objType == 'ril') {
        objContent = !_id ? content : (await articleController.getArticleContentText(_id));
        // let text_length_limit = config.GeminiEnabled ? 24000 : 3000
        // if (objContent.length > text_length_limit) {
        //     objContent = shortText(objContent, text_length_limit);
        // }
    } else if (objType == 'instatext') {
        objContent = slateToMD(content, true);
    } else if (objType == 'instanote') {
        objContent = await docController.getInstantNoteText(userId, orgId, _id, ['continue'].includes(assistant_item.action) ? 3000 : -1);
        if (objContent.length > 3002) {
            err = 'text_too_long';
        }
    } else if (objType == 'doc') {
        doc = await docController.getDocWithContent(userId, orgId, hid);
        doc.blocks?.unshift({
            type: 'h1',
            children: [{ text: doc.title }]
        })
        let md = slateToMD(doc.blocks, true);
        objContent = trimMarkdown(md, 80000);
    } else {
        objContent = content;
    }

    await new AIMessage({
        userId,
        role: 'user',
        sessionId,
        app,
        objId: _id,
        content: assistant_item.action !== 'query' ? assistant_item.label : objContent,
        objType: assistant_item.action !== 'query' ? objType : undefined
    }).save();

    let msgData = {
        userId,
        action,
        model,
        model_level,
        use_search,
        role: 'funblocks',
        objType: assistant_item.action !== 'query' ? objType : undefined,
        sessionId: data.sessionId,
        content: req.t('ai.text_too_long'),
        withCoins: !!aiProvider.useCoin
    };

    let response;
    if (err) {
        msgData.content = err === 'text_too_long' ? req.t('ai.text_too_long') : req.t('message.server_error');
    } else {
        let lng = lang || 'the same language with the given text or context'
        // let respondReq = `. \n\n[Required respond language]: please write your output in ${lng}`
        let respondReq = `.\n\n[Output requirements]: Output the result directly in ${lng} without any explanation, greeting or opening remarks. Use markdown formatting where appropriate. Output ONLY the requested content with no additional text`
        // await getLng(objContent);

        let prompt;

        if (assistant_item.prompt) {
            if (action.startsWith('translate')) {
                respondReq = '';
            }
            // else {
            //     if (['markdown', 'slides', 'doc', 'instatext'].includes(objType) && assistant_item.action != 'title') {
            //         respondReq = `. The given text is in markdown format, please output in ${lng}, apply markdown style if necessary`;
            //     }
            // }

            prompt = assistant_item.prompt;

        } else if (sub_item) {
            const role = appConfig.assistant_items.find(item => item.action == sub_item || item.goodAt?.includes(sub_item.replace(action + '_', '')));
            if (role) {
                if (action.startsWith('translate')) {
                    respondReq = '';
                }

                prompt = role.prompt;
            }
        } else if (action === 'query' && draftingType) {
            const role = appConfig.assistant_items.find(item => item.goodAt?.includes(draftingType));
            if (role) {
                prompt = role.prompt;
            }
        }

        // if (!!prompt) {
        //     prompt = `${prompt} \n\n${responseReqs || respondReq}`
        // }

        let images = [];
        if (Array.isArray(image_url)) {
            for (const url of image_url) {
                const base64 = await mediaController.getBase64Image({ url });
                if (base64) {
                    images.push(removeDataURLPrefix(base64));
                }
            }
        } else if (image_url) {
            const base64 = await mediaController.getBase64Image({ url: image_url });
            if (base64) {
                images.push(removeDataURLPrefix(base64));
            }
        }

        data = {
            ...data,
            user: userId,
            use_search,
            temperature: assistant_item.temperature,
            prompt,
            content: content || objContent,
            image: image || (images.length ? images : undefined)
        }

        if (!action_gen_image) {
            if (data.prompt) {
                data.prompt = `${data.prompt} \n\n${responseReqs || (responseReqs === undefined ? respondReq : '')}`
            } else if (objContent) {
                // if (!!prompt) {
                //     data.content = objContent;
                // } else {
                data.content = `${objContent}. \n\n${responseReqs || (responseReqs === undefined ? respondReq : '')}`
                // }
            }
        }

        let llm_provider = 'OpenAI';

        if (model) {
            llm_provider = get_llm_provider(model) || llm_provider;
        } else {
            // llm_provider = objContent.length > 3000
            //     || ['ril', 'flow', 'flow_background'].includes(objType)
            //     || assistant_item.objTypes?.includes('ril')
            //     || assistant_item.objTypes?.includes('flow')
            //     || assistant_item.objTypes?.includes('flow_background')
            //     || !!data.image
            //     ? (
            //         Math.random() > 0.999 &&
            //         lang?.toLowerCase().includes('chinese') && !data.image && 'moonshot'
            //         || 'Gemini'
            //     ) : 'OpenAI'

            llm_provider = 'Gemini';
        }

        response = await callAssistant(userId, data, llm_provider, model, req);

        if (!response?.success) {
            msgData.content = req.t('ai.exceed_askai_speed_limit');

            console.log('get error from AI........', response?.code)
            msgData.err = response?.code
            //TODO: specific err details
            if (msgData.err === 'ai_request_timeout') {
                msgData.content = req.t('ai.ai_request_timeout');
            } else if (msgData.err === 'text_too_long') {
                msgData.content = req.t('ai.text_too_long');
            } else if (msgData.err === 'content_filter' || msgData.err === '400') {
                msgData.content = req.t('ai.content_filter');
            } else {
                msgData.content = req.t('ai.llm_service_failed');
            }
        } else if (response?.data) {
            if (!response.err) {
                msgData.role = 'assistant';

                if (action !== 'xSlides') {
                    response.data.content = response.data.content?.replace(/^推荐文案：|^摘要：/, '');
                } else {
                    let content = response.data.content;
                    try {
                        let generated = extractJSONFromString(content)?.generated;
                        if (!generated?.slides?.length) throw 'Error in generating slides';

                        content = generated.slides.map(slide => {
                            // return makeSlide(slide);
                            return slide.content + '\n\nNotes: ' + slide.notes;
                        }).join('\n\n---\n\n')

                        // content = '#  ' + generated.title + '\n\n---\n\n' + content;

                        doc = await docController.upsertDoc(userId, orgId, {
                            doc: {
                                type: 'slides',
                                title: generated.title,
                                markdown: content
                            }
                        }, req);


                        response.data.doc = doc;
                        response.data.content = doc.hid;
                        response.data.contentType = 'slides';
                    } catch (err) {
                        console.log('err........', err)
                        msgData.err = err?.message || err;
                    }
                }
            }

            msgData.content = response.data.content || (!response.data.mimeContents?.length ? req.t('ai.no_reply') : '');
            msgData.mimeContents = response.data.mimeContents;
            msgData.contentType = response.data.contentType;
        }
    }

    await new AIMessage(msgData).save();

    await chargeAICoins(aiProvider, msgData, req);

    // console.log('response........', response.data)
    return {
        ...(response?.data || {}),
        sessionId: msgData.sessionId,
        action: msgData.action,
        err: msgData.err,
        content: objType == 'instatext' ? removePTagFromListItem(marked.parse(msgData.content)) : msgData.content,
        groundingMetadata: response?.data?.groundingMetadata
    };
}

const getTodayArtifactsCount = async (userId, service) => {
    let query = {
        userId,
        service,
        createdAt: {
            $gte: new Date(new Date().setHours(0, 0, 0, 0)),
            $lt: new Date(new Date().setHours(23, 59, 59, 999))
        }
    };

    return await Artifact.count(query);
}

const parseAISVGResponse = (response) => {
    const lines = response.split('\n');
    let title = '';
    let description = '';
    let svgCode = '';
    let parsingSVG = false;
    let foundTitle = false;

    for (const line of lines) {
        if (line.startsWith('## ')) {
            title = line.substring(3).trim();
            foundTitle = true;
        } else if (foundTitle && !description && !parsingSVG && line.trim() !== '') {
            description = line.trim();
        } else if (line?.trim()?.startsWith('<svg') || parsingSVG) {
            parsingSVG = true;
            svgCode += line + '\n';
            if (line.includes('</svg>')) {
                parsingSVG = false;
            }
        }
    }

    return {
        title,
        description,
        content: svgCode
    };
};

const generateSVGCard = async (data, llm_provider, llm_model, req) => {
    const { promptId, userInput, mentalModel, lang } = data;
    if (!userInput) {
        throw 'invalid_input';
    }

    let prompt = config.appConfig.assistant_items.find(item => assistant_item_selector(item, promptId, 'flow', 'flow'));

    let content;

    if (!prompt) {
        prompt = await Prompt.findById(promptId);
    }

    if (!prompt) {
        throw 'Unknown AI prompt ID'
    }

    content = prompt.prompt;

    const refine_and_final_adjust = `## Refinement and Final Adjustments
1. **Review for Completeness**:
   - Verify that all essential information is represented visually.

2. **Clarity and Balance**:
   - Recheck for any unintentional overlaps. Adjust spacing as needed.
   - Confirm text legibility and sufficient size & color contrast.
   - Recheck and adjust SVG and viewBox dimensions large enough to accommodate all content without overflow.

3. **SVG Optimization**:
   - Simplify the SVG code by removing unnecessary elements and attributes.
   - Present the final output as a cohesive SVG file that communicates the information effectively`;

    if (content.includes('{{refine_and_final_adjust}}')) {
        content = content.replaceAll('{{refine_and_final_adjust}}', refine_and_final_adjust);
    } else if (!content.includes('```svg') && !content.includes('## Refinement and Final Adjustments')) {
        content = content + '\n\n' + refine_and_final_adjust;
    }

    const output_requirements = `**Output requirements:** 
- A line start with '## ' for title
- A paragraph follow the title line for concise description of the content
- The complete SVG code:
     \`\`\`svg
     complete SVG code
     \`\`\`
- No other explanations and comments`

    if (content.includes('{{output_requirements}}')) {
        content = content.replaceAll('{{output_requirements}}', `[Output requirements]: \n\n${output_requirements}`);
    } else if (!content.includes('[Output requirements]:')) {
        content = content + '\n\n[Output requirements]: \n\n' + output_requirements;
    }

    if (content.includes('{{output_lang}}')) {
        content = content.replaceAll('{{output_lang}}', lang);
    } else {
        content = content + '\n\n[Response content language]: ' + lang;
    }

    if (content.includes('{{selected_text}}')) {
        content = content.replaceAll('{{selected_text}}', '```' + userInput + '```');
    } else {
        content = content + '\n\n' + '[Given text]: ```' + userInput + '```';
    }

    if (mentalModel) {
        content = content + '\n\n' + '[Specified mental model]: ```' + mentalModel + '```';
    }

    const response = await callAssistant('wise_card', { content }, llm_provider, llm_model, req);

    if (!response?.data?.content) {
        throw 'ai_reponse_nothing';
    }

    const parsedResponse = parseAISVGResponse(response.data.content);

    if (!parsedResponse.content) {
        throw 'ai_reponse_nothing';
    }

    parsedResponse.promptLabel = prompt.name;
    parsedResponse.type = 'svg';

    return parsedResponse;
}

const generateImageInsights = async (data, llm_provider, llm_model, req) => {
    const { imageUrl, lang, promptId } = data;
    if (!imageUrl) {
        throw 'invalid_input';
    }

    const responseReqs = !!lang ? `[Required respond language]: please write your output in ${lang}` : '';

    const assistant_item = config.appConfig.assistant_items.find(item => item.action === promptId);

    let prompt_data = {
        temperature: assistant_item.temperature,
        prompt: assistant_item.prompt + '\n\n' + responseReqs,
        image: removeDataURLPrefix(await mediaController.getBase64Image({ url: imageUrl }))
    }

    if (!prompt_data.image) {
        throw 'failed_load_image';
    }

    // const response = await callAssistant('wise_card', prompt_data, 'Gemini', 'gemini-2.0-pro-exp-02-05');
    const response = await callAssistant('wise_card', prompt_data, llm_provider, llm_model, req);

    let parsedResponse = extractJSONFromString(response.data.content);
    if (!parsedResponse?.generated?.commentary) {
        throw 'ai_reponse_nothing';
    }

    let commentary = parsedResponse.generated.commentary;

    return {
        title: commentary[0],
        content: '## ' + commentary.join('\n\n'),
        promptLabel: assistant_item.label,
        imageUrl,
        type: 'markdown'
    }
}

const generateFlowchart = async (data, llm_provider, llm_model, req) => {
    const { lang, promptId, userInput } = data;
    if (!userInput) {
        throw 'invalid_input';
    }

    const responseReqs = !!lang ? `[Required respond language]: please write your output in ${lang}` : '';

    const assistant_item = config.appConfig.assistant_items.find(item => item.action === promptId);

    let prompt_data = {
        temperature: assistant_item.temperature,
        prompt: assistant_item.prompt + '\n\n' + responseReqs,
        content: userInput
    }

    // const response = await callAssistant('wise_card', prompt_data, 'Gemini', 'gemini-2.0-pro-exp-02-05');
    const response = await callAssistant('wise_card', prompt_data, llm_provider, llm_model, req);

    const titleMatch = response.data.content?.match(/## (.*)/);
    const title = titleMatch ? titleMatch[1].trim() : '';
    const codeBlockMatch = response.data.content?.match(/```(.*?)```/s);
    const codeblock = codeBlockMatch ? codeBlockMatch[1].split('\n').slice(1).join('\n').trim() : '';
    const descriptionParts = response.data.content?.split('```')[0]?.split('\n').filter(line => line.trim() !== '' && !line.startsWith('## '));
    const description = descriptionParts.join('\n').trim();

    if (!codeblock) {
        throw 'ai_reponse_nothing';
    }

    const parsedResponse = {
        title: title.replace('## ', '').trim(),
        description,
        codeblock
    };

    return {
        title: parsedResponse.title,
        description: parsedResponse.description,
        content: parsedResponse.codeblock,
        promptLabel: assistant_item.label,
        userInput,
        type: 'mermaid'
    }
}

const generateWiseCard = async (userId, data, req) => {
    const { userInput, lang, mode, promptId } = data;

    let action = generateSVGCard;
    // let llm_provider = 'Claude';
    // let llm_model = 'claude-3.7-sonnet';
    const llms = {
        gemini: {
            llm_provider: 'Gemini',
            // llm_model: 'gemini-2.5-pro-exp-03-25'
            llm_model: 'gemini-2.5-flash'
        },
        claude: {
            llm_provider: 'Claude',
            llm_model: 'claude-4-sonnet'
        }
    }

    let llm = Math.random() > 0.2 ? llms.claude : llms.gemini;

    if (mode === 'imageInsights') {
        action = generateImageInsights;
        llm = llms.gemini;
        // llm_model = 'gemini-2.0-pro-exp-02-05';
    } else if (['chart_flowchart', 'chart_sequencediagram', 'chart_timeline', 'chart_quadrant'].includes(promptId)) {
        action = generateFlowchart;
        llm = llms.gemini;
        // llm_model = 'gemini-2.0-pro-exp-02-05';
    }

    let model_level = ai_api_models.find(m => m.value === llm.llm_model)?.level || 't2'

    const privilege = await paymentController.getServingPrivilege(userId, 'askAI');

    const aiProvider = await getAIProvider(userId, privilege, model_level);
    if (aiProvider?.err) {
        throw aiProvider.err
    }

    let parsedResponse = await action(data, llm.llm_provider, llm.llm_model, req);

    const artifact = await new Artifact({
        userId: userId,
        service: 'aiinsights',
        userInput: userInput,
        promptId,
        ...parsedResponse,
        lang: lang,
        tags: [parsedResponse.promptLabel]
    }).save();

    await chargeAICoins(aiProvider, { role: 'assistant', userId, model_level }, req);

    return artifact;
}

const updateArtifact = async ({ userId, _id, app, mode, data }) => {
    let artifact = app?.toLowerCase() === 'graphics' ? await Artifact.findOneAndUpdate({ _id, userId }, { $set: data }) : await Doc.findOneAndUpdate({ _id, userId }, { $set: data });
    return artifact;
}

const getArtifact = async ({ _id, format, app }) => {

    let artifact;
    if (['graphics', 'poetic', 'one-page-slide', 'infographic', 'mindsnap', 'insightcards', 'prompt-optimizer', 'lesson-plans', 'dok-assessment'].includes(app?.toLowerCase())) {
        artifact = await Artifact.findOne({ _id }).lean();

        console.log('queried article...........', artifact)
        if (format === 'png') {
            artifact.content = 'data:image/png;base64,' + (await sharp(Buffer.from(artifact.content)).png().toBuffer()).toString('base64');
        }
    } else {
        artifact = await Doc.findById(_id).lean();
    }

    return artifact;
}

const getArtifacts = async (userId, selector = {}, sort, filter, onlyList) => {
    const pageNum = filter ? filter.pageNum : 0;
    const pageSize = filter ? filter.pageSize : 1;

    let artifacts;
    if (selector.app === 'Graphics') {
        delete selector.app;
        delete selector.mode;
        artifacts = await Artifact.find(selector).sort({ createdAt: -1 }).skip(pageNum * pageSize).limit(pageSize).lean();
    } else {
        artifacts = await Doc.find(selector, !onlyList ? undefined : { jsonString: 0, markdown: 0, blockIds: 0 }).sort(sort || { createdAt: -1 }).skip(pageNum * pageSize).limit(pageSize).lean();
    }

    if (userId && selector.app === 'Graphics') {
        for (let artifact of artifacts) {
            let like = await Like.findOne({ userId, artifactId: artifact._id });
            artifact.isLiked = like ? true : false;
        }
    }

    return artifacts;
}

const getMyArtifacts = async (userId, selector, filter) => {
    return await getArtifacts(userId, { userId, folder: { $ne: 1 }, ...selector }, null, filter, false);
}

const getShowcaseArtifacts = async (userId, selector, filter) => {
    let sort;
    if (selector.app.toLowerCase() === 'graphics') {
        selector = { likes: { $gte: 1 }, ...selector };
    } else {
        selector.folder = 2;
        sort = { title: 1 };
        delete selector.app;
    }
    return await getArtifacts(userId, selector, sort, filter, true);
}

const likeArtifact = async (userId, isLike, artifactId) => {
    const artifact = await Artifact.findById(artifactId);
    if (!artifact) {
        throw 'Unknown artifact ID';
    }

    const like = await Like.findOne({ userId, artifactId });
    if (isLike && like || !isLike && !like) {
        return;
    }

    if (isLike) {
        await new Like({ userId, artifactId }).save();
    } else {
        await Like.deleteOne({ userId, artifactId });
    }

    if (isLike) {
        artifact.likes = artifact.likes ? artifact.likes + 1 : 1;
    } else {
        artifact.likes = artifact.likes ? artifact.likes - 1 : 0;
    }

    await artifact.save();
    return artifact;
}

const fetchUrlContent = async (url) => {
    try {
        const apiUrl = 'https://r.jina.ai/';
        const response = await fetch(`${apiUrl}${url}`, {
            // headers: {
            //     'Accept': 'application/json'
            // }
        });
        const data = await response.text()
        return data
    } catch (error) {
        console.error('fetch url content err...................', error)
    }
};

const generateProductInfo = async (data, req) => {
    const llm_model = 'gemini-2.5-flash'
    const llm_provider = 'Gemini';


    if (!data.url) {
        console.log('data.........', data)
        throw 'invalid_input';
    }

    const page = await fetchUrlContent(data.url);
    console.log('page.............', page)

    const prompt = `You are a professional product analyst specializing in extracting key information from web content and generating structured product descriptions.

## Task
Analyze the provided product web page content and generate a structured product description based on the extracted information.

## Instructions
1. **Carefully read** the web page content to identify core product information
2. **Extract key elements**: product name, value proposition, features, target audience, use cases
3. **Generate structured description** ensuring accuracy, conciseness, and value

## Output Format
Return results strictly in the following JSON format:

\`\`\`json
{
   "generated": {
        "productInfo": {
            "name": "Product name",
            "tagline": "Core value proposition or slogan",
            "description": "Product description including problem solved, key features, use scenarios, target users, etc.",
            "category": "Product category, 'text-generation|image-generation|code-generation|data-analysis|audio-processing|video-editing|translation|search-engines|education|marketing|productivity|customer-service'",
            "tags": "tag array, select 1-3 tags from <ai_assistant|conversational_ai|smart_qa|language_model|writing_assistant|content_generation|marketing_copy|image_generation|image_editing|avatar_generation|background_removal|video_generation|video_editing|video_subtitles|speech_synthesis|speech_recognition|music_generation|text_to_speech|speech_to_text|code_generation|development_assistant|low_code_platform|business_intelligence|machine_learning|office_automation|document_processing|web_design|note_taking|seo_optimization|market_analysis|machine_translation|voice_translation|graphic_design>"
        }
   }
}
\`\`\`

## Content Generation Guidelines

### name
- Extract the official product name clearly identified on the web page
- If multiple variants exist, select the primary official name

### tagline
- Extract the official slogan or core value proposition
- If no explicit tagline exists, create a concise, powerful statement based on core functionality
- Keep length between 5-15 words
- Highlight unique value proposition

### description
Generate a 100-1000 word description that should include:

1. **Problem solved**: What pain point or need the product addresses
2. **Key features**: Primary functionality and advantages
3. **Use cases**: Specific scenarios and environments where it's applicable
4. **Target audience**: Primary user groups
5. **Unique value**: Differentiation from competitors

The description should be:
- Objective and accurate, based on actual web page content
- Clear and concise, avoiding marketing jargon
- Well-structured with logical flow
- Focused on practical value and core selling points
- Compose with markdown formatting

## Requirements
- Base analysis solely on provided web page content
- Do not add external information
- If information is unclear, make reasonable inferences from context
- Ensure JSON format is valid and parseable
- Use professional, clear language
`;


    const content = `${prompt}\n\n[Given Product Webpage Content]: \n\n${page}`
    const response = await callAssistant('mental_model', { content }, llm_provider, llm_model, req);

    if (!response?.data?.content) {
        throw 'ai_reponse_nothing';
    }

    const jsonContent = extractJSONFromString(response.data.content);

    console.log('get product json info', jsonContent);

    return jsonContent;
}

const generateMentalModelPage = async (data, req) => {
    // const llm_model= 'gemini-2.5-pro-exp-03-25';
    const llm_model = 'gemini-2.5-flash'
    const llm_provider = 'Gemini';
    const { mental_model } = data;


    if (!mental_model) {
        throw 'invalid_input';
    }

    let content = `I need you to create a comprehensive educational article about the mental model: "${mental_model}".

Please organize the content according to the following structure:

1. **Introduction** (300-400 words)
   - Introduce the mental model in an engaging way
   - Briefly explain why this model is important in modern thinking and decision-making
   - Provide a concise yet powerful definition

2. **Historical Background** (400-500 words)
   - Explain the origins of this mental model
   - Introduce the creator(s)/discoverer(s) and their contributions
   - Describe how the model has evolved over time

3. **Core Concepts Analysis** (800-1000 words)
   - Elaborate on the key components and principles of the model
   - Explain complex concepts using simple language
   - Provide at least 3 clear examples illustrating how it works

4. **Practical Applications** (700-800 words)
   - Provide at least 5 specific application cases from different domains
   - Include areas such as business, personal life, education, technology, etc.
   - Briefly analyze each application scenario

5. **Comparison with Related Mental Models** (400-500 words)
   - Compare 2-3 related or similar mental models
   - When mentioning each related mental model, include a markdown link in the format: [Mental Model Name](/thinking-matters/classic-mental-models/mental-model-name) where "mental-model-name" is the model name with spaces replaced by hyphens
   - Explain their relationships, similarities, and differences
   - Clarify when to choose this model over others

6. **Critical Thinking** (400-500 words)
   - Analyze the limitations and drawbacks of the model
   - Discuss potential misuse cases
   - Provide advice on avoiding common misconceptions

7. **Practical Guide** (600-700 words)
   - Provide a step-by-step operational guide
   - Include practical suggestions for beginners to start applying the model
   - Design a simple thinking exercise or worksheet

8. **Conclusion** (200-300 words)
   - Summarize key points
   - Emphasize the value and significance of the model
   - Encourage readers to integrate the model into their thinking processes

Please ensure the article:
- Uses clear, professional yet accessible language
- Includes relevant subheadings and lists to enhance readability
- Avoids overly academic expressions while maintaining content integrity
- Uses pronouns like "you" and "we" to enhance interactivity
- Incorporates 2-3 original analogies or metaphors to help readers understand
- Includes a "Frequently Asked Questions" section addressing 5 common questions about the model
- Provides resource suggestions for advanced readers seeking deeper understanding

SEO optimization requirements:
- Naturally incorporate target keywords in the title, subheadings, opening paragraphs, and conclusion
- Include relevant long-tail keywords and LSI keywords
- Maintain a total article length between 3,500-4,500 words
- Keep paragraphs short, with no more than 4-5 sentences each
- Use descriptive subheadings (H2 and H3 tags)
- Ensure high content originality, avoiding copying common explanations
- Include practical content to increase reader retention time

Finally, please ensure the article style is both educational and engaging, appealing to both beginners and expert readers.

Output only the final polished article with Markdown format, without any comments or explanations.`;


    console.log('mental model.........', mental_model)
    const response = await callAssistant('mental_model', { content }, llm_provider, llm_model, req);

    if (!response?.data?.content) {
        throw 'ai_reponse_nothing';
    }

    return response.data.content;
}

const extractCodes = (content, type) => {
    const codeBlockMatch = content?.match(/```(.*?)```/s);
    if (!codeBlockMatch) return '';

    const fullContent = codeBlockMatch[1];
    const lines = fullContent.split('\n');

    // 如果第一行是语言标识符（如 mermaid, svg 等），则跳过
    // 否则保留所有内容
    const firstLine = lines[0]?.trim().toLowerCase();
    const isLanguageIdentifier = firstLine &&
        ['javascript', 'js', 'python', 'java', 'html', 'css', 'mermaid', 'svg', 'json', 'xml'].includes(firstLine);

    const codeblock = isLanguageIdentifier
        ? lines.slice(1).join('\n').trim()
        : fullContent.trim();

    return codeblock;
}

const aiAction = async (userId, orgId, data, req) => {
    const { mode, videoId, mentalModel, imageUrl, horoscopeInfo } = data;
    const app = data.app?.toLowerCase();

    let action = data.action || 'flow_mindmap';

    if (app === 'mindmap') {
        if (mode === 'book') {
            action = 'flow_book_mindmap'
        } else if (mode === 'movie') {
            action = 'flow_movie_mindmap'
        } else if (['video', 'link'].includes(mode)) {
            action = 'flow_mindmap'
        }
    } else if (['brainstorming', 'decision'].includes(app)) {
        action = app === 'brainstorming' && 'flow_brainstorming' || 'flow_decision_analysis';

        data.content = `[${app === 'brainstorming' && 'Specified topic' || 'Decisions to analysis'}]: ${data.content}`;

        if (app === 'decision') {
            data.content = '[Application Scenario]: Decision Analysis\n\n' + data.content
        }

        if (mentalModel) {
            data.content = data.content + '\n\n' + `[Specified mental model and analysis framework]: ${mentalModel}`
        }
    } else if (['mindkit'].includes(app)) {
        action = 'flow_mentalmodel';

        data.content = `[Problem]: ${data.content}`;

        if (mentalModel) {
            data.content = data.content + '\n\n' + `[Specified mental model name]: ${mentalModel}`
        }
    } else if (app === 'startupmentor') {
        action = 'flow_startup_mentor';
    } else if (app === 'businessmodel') {
        action = 'flow_business_model';
    } else if (app === 'okr') {
        action = 'flow_okr_development';
    } else if (app === 'planner') {
        action = 'flow_task_breakdown';

        data.content = `[Task to breakdown]: ${data.content}`;
    } else if (app === 'slides') {
        action = 'slideshow';
    } else if (app === 'teaching-slides') {
        action = 'teaching_slides';
    } else if (app === 'youtube') {
        action = 'summary_keypoints';
    } else if (app === 'critical-thinking') {
        action = 'flow_critical_analysis';
    } else if (app === 'refine-question') {
        action = 'flow_refine_question';
    } else if (app === 'reflection') {
        action = 'flow_critical_reflection';
    } else if (app === 'bias') {
        action = 'flow_critical_bias';
    } else if (app === 'feynman') {
        action = 'flow_feynman_mindmap';
    } else if (app === 'bloom') {
        action = 'flow_bloom_mindmap';
    } else if (app === 'solo') {
        action = 'flow_solo_mindmap';
    } else if (app === 'dok') {
        action = 'flow_dok_mindmap';
    } else if (app === 'marzano') {
        action = 'flow_marzano_mindmap';
    } else if (app === 'layered-explanation') {
        action = 'flow_layered_explanation';
    } else if (app === 'counselor') {
        action = 'flow_psychologist';
    } else if (app === 'dreamlens') {
        action = 'flow_dream_analysis';
        data.content = `['Dream description']: ${data.content}`;
    } else if (app === 'horoscope') {
        action = 'flow_fortune_analysis';
        if (horoscopeInfo?.range === 'today') {
            action = 'flow_fortune_daily';
        }
        data.content = `['User astrology info']: ${data.content}`;
    } else if (app === 'art') {
        if (mode === 'art') {
            action = 'flow_art_insight';
        } else if (mode === 'image') {
            action = 'flow_art_image_insight';
        }
    } else if (app === 'photo') {
        action = 'flow_photography_coach';
    } else if (app === 'erase') {
        action = 'flow_erase_image_watermarks';
        data.modalities = ['Image'];
        data.content = 'Please eliminate all watermarks from the image.';
        data.responseReqs = '';
    } else if (app === 'sketch') {
        action = 'flow_sketch_art';
        data.modalities = ['Image'];
        data.content = `Create a high-quality artwork based on the provided sketch, rendered in [ART_STYLE] style. 

**Requirements:**
- Accurately preserve the composition, proportions, and key elements from the original sketch
- Maintain the structural layout and spatial relationships exactly as shown
- Apply [ART_STYLE] artistic techniques, colors, textures, and visual characteristics
- Ensure the final artwork clearly reflects [ART_STYLE] aesthetics while staying true to the sketch

**Quality Parameters:**
- High resolution, detailed rendering
- Professional artistic quality
- Clean, polished finish
- Proper lighting and shading consistent with [ART_STYLE]

[Specified ART_STYLE]: ${data.imageStyle}
`
        if (data.userInput) {
            data.content = data.content + '\n\n[User description]: ' + data.userInput
        }
        data.responseReqs = '';
    } else if (app === 'imageeditor') {
        action = 'flow_image_editor';
        data.modalities = ['Image'];
        data.content = 'Please editor image per required: \n\n' + data.content;
        data.responseReqs = '';
    } else if (app === 'avatar') {
        action = 'flow_image_avatar';
        data.modalities = ['Image'];
        // data.content = `Please redraw the specified image in the specified style: "${data.imageStyle}"`;
        data.content = `Please redraw the given image in the specified style while preserving the original composition, details, and key elements as accurately as possible. Maintain the structure, proportions, and key features of the original image, applying only stylistic changes to match the specified style.
        
        [Specified style]: ${data.imageStyle}
        `
        data.responseReqs = '';
    } else if (app === 'studio') {
        action = 'flow_image_studio';
        data.modalities = ['Image'];
        // data.content = `Please redraw the specified image in the specified style: "${data.imageStyle}"`;
        data.content = `## Role
AI image processing expert that transforms casual photos into professional business portraits with studio-level quality.

## Task
**Auto-generate** polished business photos ready for professional use. Apply all optimizations automatically while considering user requirements.

## Auto-Processing
- **Background**: Remove clutter, replace with clean business backgrounds (white/gray/navy)
- **Lighting**: Face lighting, shadow removal, balanced exposure
- **Color**: White balance correction, natural skin tones
- **Composition**: Standard headshot ratios, centered subject, proper framing
- **Enhancement**: Light blemish correction, eye brightening, skin smoothing, sharpening
- **Quality**: Noise reduction, optimized contrast/saturation

## Auto-Fixes
- Shadows → Soft lighting
- Messy background → Clean replacement
- Poor angles → Perspective correction
- Dark photos → Smart brightening
- Color issues → White balance
- Blur → Sharpening

## Guidelines
- Preserve authentic features
- Maintain professional business appearance
- Output ready-to-use portraits directly
`

        if (data.userInput) {
            data.content += `\n\n[User requirements]: \`\`\`${data.userInput}\`\`\``;
        }

        data.content += '\n\nProcess given photos into professional business photo immediately.';

        data.responseReqs = '';
    } else if (app === 'reading') {
        action = 'flow_read_mindmap';
    } else if (app === 'movie') {
        action = 'flow_movie_mindmap';
    } else if (app === 'prompt-optimizer') {
        action = 'prompt_optimize';
        data.responseReqs = `[Output requirements]: Respond in ${data.lang}`;
    } else if (app === 'related_questions_topics') {
        action = 'related_questions_topics';
        data.responseReqs = `[Output requirements]: Respond in ${data.lang}`;
    } else if (app === 'prompt_detail_form') {
        action = 'prompt_detail_form';
        data.responseReqs = `[Output requirements]: Respond in ${data.lang}`;
    } else if (app === 'prompt_image_form') {
        action = 'prompt_image_form';
        data.responseReqs = `[Output requirements]: Respond in ${data.lang}`;
    } else if (app === 'question_optimize') {
        action = 'question_optimize';
        data.responseReqs = `[Output requirements]: Respond in ${data.lang}`;
    } else if (app === 'critical_analysis') {
        action = 'critial_analysis';
        data.objType = 'ril';
        data.responseReqs = `[Output requirements]: Respond in ${data.lang}`;
    } else if (app === 'lesson-plans') {
        action = 'lesson_plans';
        data.objType = 'edu';

        if (mentalModel) {
            data.content = `[Topic]: ${data.content}.\n\n[Specified educational framework]: ${mentalModel}`;
        }
    } else if (app === 'dok-assessment') {
        action = 'dok_assessment';
        data.objType = 'edu';
    }

    data.action = action;

    let context;
    if (!data.content && videoId) {
        const info = await get_youtube_info({ videoId });
        data.content = (info.title && `[video title]: ${info.title}` || '') + info.transcripts?.map(t => t.text).join(' ')

        context = {
            vid: videoId,
            ...info,
            transcripts: info.transcripts.map(t => t.text).join(' ')
        }
    }

    if (imageUrl) {
        data.image_url = imageUrl;
        context = {
            src: imageUrl,
            caption: data.userInput
        }
    }

    if (!data.model) {
        if (['fix_codes_bug', 'improve_codes'].includes(app)) {
            data.model = 'claude-4-sonnet';
        } else {
            data.model = 'gemini-2.0-flash-001';
        }
    }

    let response = await callAIAssistant(userId, orgId, data, req);
    response.action = action;

    //TODO: fix in future.
    if (app === 'decision') {
        response.action = 'flow_brainstorming';
    }

    if (['fix_codes_bug', 'improve_codes'].includes(app)) {
        if (['mermaid', 'svg'].includes(data.type)) {
            let artifact = await Artifact.findById(data.artifactId).lean();

            artifact.content = extractCodes(response.content);
            delete artifact._id;

            artifact = await new Artifact(artifact).save();

            return artifact
        }
    }

    if (['prompt-optimizer', 'lesson-plans', 'dok-assessment'].includes(app)) {
        response.type = 'markdown';
        const artifact = await new Artifact({
            userId: userId,
            service: 'aitools',
            app: 'prompt-optimizer',
            userInput: data.content,
            promptId: 'prompt_optimize',
            content: response.content,
            type: response.type,
            lang: data.lang,
        }).save();

        const blocks = SlateMarkdown.deserialize(response.content);

        const doc = await docController.createNewDocWithContent(
            userId,
            orgId,
            { title: data.userInput, blocks, space: 'private' },
            { type: 'doc' },
            req
        );

        response.hid = doc.hid;
        response._id = artifact._id;
    }

    if (['slides', 'teaching-slides'].includes(app)) {
        const jsonResult = extractJSONFromString(response.content);
        const generated = jsonResult?.generated;

        if (generated?.slides?.length) {
            const title = generated.title;
            const slides_mkd = generated.slides?.map(slide => `${!!slide.title && ('## ' + slide.title + '\n\n') || ''}${slide.content}\n\nNotes: ${slide.notes}`).join('\n\n---\n');

            // let doc = await docController.upsertDoc(userId, orgId, {
            //     doc: {
            //         type: 'slides',
            //         title: title,
            //         markdown: slides_mkd,
            //         app,
            //         mode
            //     }
            // }, req);

            let doc = await docController.createNewDocWithContent(
                userId,
                orgId,
                {
                    title: title,
                    space: 'private'
                },
                {
                    type: 'slides',
                    markdown: slides_mkd,
                    app,
                    mode
                },
                req
            );

            delete doc.markdown;
            delete response.content;
            response = {
                // ...response,
                ...doc,
                action
            };
        } else {
            response = {
                ...response,
                err: response.err || 'Failed to generate Slides, please try again later!',
            }
        }
    }

    if (response.err === 'exceed_msg_limit') {
        response.code = 'exceed_daily_quota';
    }

    response.context = context

    return response;
}

function extractJSONFromString(str) {
    let stack = [];
    let startIndex = -1;
    let endIndex = -1;

    for (let i = 0; i < str.length; i++) {
        if (str[i] === "{") {
            if (stack.length === 0) {
                startIndex = i;
            }
            stack.push("{");
        } else if (str[i] === "}" && stack.length > 0) {
            stack.pop();
            if (stack.length === 0) {
                endIndex = i;
                break; // 找到第一个完整的 JSON 对象，提前结束循环
            }
        }
    }

    if (startIndex !== -1 && endIndex !== -1) {
        const jsonString = str.substring(startIndex, endIndex + 1);

        try {
            const parsedJSON = JSON.parse(jsonString);
            return parsedJSON;
        } catch (error) {
            console.error("Invalid JSON format:", error);
            return null; // JSON 解析错误，返回 null 或其他合适的值
        }
    } else {
        return null; // 如果字符串中没有找到完整的 JSON 对象，返回 null 或其他合适的值
    }
}

const processSlideContent = (content) => {
    if (Array.isArray(content)) {
        return content.join('\n\n').replace(/\n\n(\d+\.\s)/g, '\n$1').replace(/\n\n([-*]\s)/g, '\n$1');
    } else if (typeof content === 'object') {
        return Object.keys(content).map(key => key + ':' + content[key]).join('\n\n').replace(/\n\n(\d+\.\s)/g, '\n$1').replace(/\n\n([-*]\s)/g, '\n$1');
    }

    return content;
}
const makeSlide = (slide) => {
    return '## ' + slide.title + '\n\n' + processSlideContent(slide.content) + (slide.notes ? '\n\nNotes:\n\n' + processSlideContent(slide.notes) : '')
}

// 根据不同句子分隔符切割句子
function splitSentences(paragraph) {
    // 句子分隔符包括"."、"!"、"?"、汉字中的句号符号（。）、"？"和"！"
    return paragraph.split(/\.|\!|\?|。|？|！/).filter(sentence => sentence.trim() !== '');
}

// 判断是否为特殊段落（headings和list）
function isSpecialParagraph(paragraph) {
    // 假设特殊段落以"#"开头或以"*"、"-"或数字开头，可根据实际情况修改判断逻辑
    return paragraph.length < 300 && paragraph.startsWith("#") || /^\s*([*-]|\d+\.)\s+/.test(paragraph);
}

// 缩减文档长度
function trimMarkdown(markdownDocument, maxLength) {
    let willDropLength = countWords(markdownDocument) - maxLength;
    let droppedLength = 0;

    console.log('length............', countWords(markdownDocument), willDropLength)

    let paragraphs = markdownDocument.split("\n\n").filter(p => !!p).map(p => {
        if (isSpecialParagraph(p)) {
            return p;
        }

        return splitSentences(p);
    }).filter(p => !!p && p.length > 0);

    let nonSpecialParagraphs = paragraphs.map((p, index) => {
        if (typeof p !== 'string') {
            return {
                index
            }
        }

        return null;
    }).filter(item => !!item).map(item => item.index);

    const droppedParagraphs = [];
    while (willDropLength > droppedLength && nonSpecialParagraphs.length > 0) {
        let choosenParagraphIndex = nonSpecialParagraphs[chooseRandomly(nonSpecialParagraphs.length)];
        let choosenParagraph = paragraphs[choosenParagraphIndex];

        if (choosenParagraph.length === 0) {
            nonSpecialParagraphs = nonSpecialParagraphs.filter(item => item != choosenParagraphIndex);
            droppedParagraphs.push(choosenParagraphIndex);
            continue;
        }

        let choosenSentenceIndex = chooseRandomly(choosenParagraph.length);
        let choosenSentence = choosenParagraph[choosenSentenceIndex];

        droppedLength += countWords(choosenSentence);
        choosenParagraph.splice(choosenSentenceIndex, 1);
    }

    while (willDropLength > droppedLength && paragraphs.length > 1) {
        // console.log('drop paragraph, length............', willDropLength, droppedLength)
        let choosenParagraphIndex = chooseRandomly(paragraphs.length);
        while (droppedParagraphs.includes(choosenParagraphIndex)) {
            choosenParagraphIndex = chooseRandomly(paragraphs.length);
        }
        droppedLength += countWords(paragraphs[choosenParagraphIndex]);

        droppedParagraphs.push(choosenParagraphIndex);
    }

    return paragraphs.filter((p, index) => {
        return !droppedParagraphs.includes(index)
    }).map(p => {
        if (typeof p === 'string') {
            return p;
        } else {
            return p.join("。")
        }
    }).join("\n\n");
}

function chooseRandomly(length) {
    const random = Math.floor(Math.random() * length);
    return random;
}

function countWords(text) {
    const wordPattern = /\b\w+\b/g;

    const englishWordCount = (text.match(wordPattern) || []).length;
    const chineseCharCount = text.replace(/[^\u4e00-\u9fa5]/g, "").length;
    //Todo: 不清楚ChatGPT是否算空格、标点符合之类的
    const otherCharCount = text.replace(/[\u4e00-\u9fa5]/g, "").replace(wordPattern, "").length;
    const totalWordCount = englishWordCount + otherCharCount + chineseCharCount;

    return totalWordCount;
}

const shortText = (text, max_length) => {
    let splitArray = text.split(/[。.\n]/).filter(str => !!str);
    if (splitArray.length > 40) {
        splitArray = splitArray.slice(4, splitArray.length - 4);
    }

    let resultArray = [];

    // 保留数组的开头部分连续子数组
    let headArray = [];
    let i = 0;
    let count = 0;
    while (i < splitArray.length && count + splitArray[i].length <= max_length / 4) {
        count += splitArray[i].length;
        headArray.push(splitArray[i]);
        i++;
    }

    // 保留数组的结尾部分连续子数组
    let tailArray = [];
    i = splitArray.length - 1;
    count = 0;
    while (i >= 0 && count + splitArray[i].length <= max_length / 4) {
        count += splitArray[i].length;
        tailArray.unshift(splitArray[i]);
        i--;
    }

    // 数组中间部分，尽可能多地保留，但可以随机删除一些元素
    let middleArray = splitArray.slice(headArray.length, splitArray.length - tailArray.length);
    count = middleArray.reduce((sum, str) => sum + str.length, 0);
    while (count > max_length / 2) {
        let index = Math.floor(Math.random() * middleArray.length);
        let deleted = middleArray.splice(index, 1);
        count -= deleted[0].length;
    }

    resultArray = headArray.concat(middleArray);
    resultArray = resultArray.concat(tailArray);

    return resultArray.join('。');
}

const getMessages = async (userId, orgId, selector, filter) => {
    selector = CONSTANT.selector_by_page(selector, filter);
    let pageSize = filter ? filter.pageSize : 1;

    console.log('message selector', JSON.stringify(selector));
    let messages = await AIMessage.find(selector)
        .sort({ createdAt: -1 }).limit(pageSize).lean();

    messages = await Promise.all(messages.map(async msg => {
        if (msg.objId) {
            if (msg.objType === 'ril') {
                msg.obj = await Article.findById(msg.objId).lean();
                if (msg.obj) {
                    const ril = await RIL.findOne({ userId, articleId: msg.objId }).lean();

                    if (ril.title) {
                        msg.obj.title = ril.title;
                    }
                }
            } else if (msg.objType === 'instanote') {
                msg.obj = await docController.getInstantNoteItem(userId, orgId, { _id: msg.objId });
            }
        }

        return msg;
    }))

    return messages;
}

const getAIResponse = async (userId, sessionId) => {
    const message = await AIMessage.findOne({
        userId,
        sessionId,
        role: 'assistant'
    }).lean();

    if (message?.action === 'xSlides') {
        message.doc = await docController.getDocWithoutContent({ hid: message.content });
    }

    return message;
}

const upsertPrompt = async (req, userId, data) => {
    let existing_prompt;
    if (!data._id) {
        data.userId = userId;
        data._id = new mongoose.mongo.ObjectID();
    } else {
        existing_prompt = await Prompt.findById(data._id).lean();
    }

    if (data.visibility === Prompt.VISIBILITY.org) {
        data.orgId = req.session.orgId;
    } else if (existing_prompt && existing_prompt.visibility != Prompt.VISIBILITY.org) {
        delete data.orgId;
    }

    if (data.visibility !== Prompt.VISIBILITY.public && (!existing_prompt || existing_prompt.visibility !== Prompt.VISIBILITY.public)) {
        let privilege = await paymentController.getServingPrivilege(userId, 'privatePrompts');
        if (privilege != 'unlimited') {
            let private_prompts_count = await Prompt.count({ userId, visibility: { $ne: Prompt.VISIBILITY.public } });
            if (privilege <= private_prompts_count) {
                throw 'ExceedsQuota'
            }
        }
    }

    delete data.__v
    data.updatedAt = new Date();

    let prompt = await Prompt.findOneAndUpdate({ _id: data._id }, { $set: data }, { upsert: true, setDefaultsOnInsert: true, new: true })

    prompt.userId = await User.findById(prompt.userId).lean();

    return prompt.toObject();
}

const deletePrompt = async (userId, _id) => {
    const prompt = await Prompt.findOneAndRemove({ userId, _id });
    return prompt;
}

const pinPrompt = async (userId, promptId, pin) => {
    let prompt = await Prompt.findById(promptId).populate('userId', User.userProfileFilter).lean();

    if (pin) {
        if (prompt.userId._id == userId) {
            return null;
        }

        let item = await PinnedPrompt.findOne({ userId, promptId });
        if (!item) {
            await new PinnedPrompt({ userId, promptId }).save();
        }
    } else {
        await PinnedPrompt.remove({ userId, promptId })
    }

    if (prompt) {
        if (pin) {
            prompt.pinned = true;
        } else {
            prompt.pinned = false;
        }
    }

    return prompt;
}

const getPrompts = async (userId, orgId) => {
    return await Prompt.find({
        userId, $or: [{ orgId: null },
        { orgId: undefined },
        { orgId }]
    }).sort({ updatedAt: -1 }).lean();
}

const getPrompt = async (id) => {
    return await Prompt.findById(id).populate('userId', User.userProfileFilter).lean();
}

const getPublicPrompts = async (userId, selector, filter) => {
    const pageNum = filter ? filter.pageNum : 0;
    const pageSize = filter ? filter.pageSize : 20;

    selector = CONSTANT.selector_by_page(selector, filter);

    let pinnedPrompts = await PinnedPrompt.find({ userId }).lean();

    let prompts = await Prompt.find(selector)
        .sort({ createdAt: -1 })
        .skip(pageNum * pageSize).limit(pageSize)
        .populate('userId', User.userProfileFilter)
        .lean();

    prompts = prompts.map(prompt => {
        if (pinnedPrompts.find(pinned => {
            return pinned.promptId.toString() == prompt._id.toString()
        })) {
            prompt.pinned = true
        }

        return prompt;
    })

    return prompts;
}

const getPinnedPrompts = async (userId, filter) => {
    const pageNum = filter ? filter.pageNum : 0;
    const pageSize = filter ? filter.pageSize : 20;

    let pinnedPrompts = await PinnedPrompt.find({ userId })
        .sort({ createdAt: -1 })
        .skip(pageNum * pageSize)
        .limit(pageSize)
        .populate('promptId')
        .lean();

    pinnedPrompts = await Promise.all(pinnedPrompts.map(async pp => {
        let populatedPrompt = await User.populate(pp.promptId, { path: 'userId' });
        populatedPrompt.pinned = true;
        return populatedPrompt;
    }));

    return pinnedPrompts;
}

const addSharedContent = async (req, userId, data) => {
    data.userId = userId;
    // await new Promise(resolve => setTimeout(resolve, 3000));

    let sharedContent = await new SharedAIContent(data).save();
    return sharedContent;
}

const getRemainDailyAIQueryQuotas = async (userId, req) => {
    const privilege = await paymentController.getServingPrivilege(userId, 'askAI');
    const privilege_boards = await paymentController.getServingPrivilege(userId, 'whiteboards');
    const privilege_nodes = await paymentController.getServingPrivilege(userId, 'flowNodes');

    let data = {
        t1: {
            level: req.t('quota_model_level_1'),
            quota: (privilege.t1 != undefined) ? privilege.t1 : privilege.desc,
            remain: 0
        },
        t2: {
            level: req.t('quota_model_level_2'),
            quota: (privilege.t2 != undefined) ? privilege.t2 : privilege.desc,
            remain: 0
        },
        whiteboards: {
            level: req.t('quota_whiteboards'),
            quota: privilege_boards.whiteboards,
            remain: 0
        },
        flowNodes: {
            level: req.t('quota_flow_nodes'),
            quota: privilege_nodes.flowNodes
        },
        servingProduct: privilege.servingProduct
    }
    const msgs_today_t1 = await getTodayMsgCount(userId, 't1');
    const msgs_today_t2 = await getTodayMsgCount(userId, 't2');
    const boards_created = await Doc.count({ userId, type: 'flow', folder: Doc.FOLDER.normal });

    data.t1.remain = typeof privilege.t1 === 'number' ? privilege.t1 - msgs_today_t1 : 0;
    data.t2.remain = typeof privilege.t2 === 'number' ? privilege.t2 - msgs_today_t2 : 0;
    data.whiteboards.remain = Math.max(0, data.whiteboards.quota - boards_created);

    return data
}

exports.initWelcomeMessage = initWelcomeMessage;
exports.message = message;
exports.askAI = askAI;
exports.callGemini = callGemini;
exports.callAIAssistant = callAIAssistant;
exports.getMessages = getMessages;
exports.getAIResponse = getAIResponse;
exports.upsertPrompt = upsertPrompt;
exports.deletePrompt = deletePrompt;
exports.pinPrompt = pinPrompt;
exports.getPrompts = getPrompts;
exports.getPrompt = getPrompt;
exports.getPublicPrompts = getPublicPrompts;
exports.getPinnedPrompts = getPinnedPrompts;
exports.addSharedContent = addSharedContent;
exports.aiAction = aiAction;
exports.generateWiseCard = generateWiseCard;
exports.generateMentalModelPage = generateMentalModelPage;
exports.generateProductInfo = generateProductInfo;
exports.getArtifact = getArtifact;
exports.getArtifacts = getArtifacts;
exports.getMyArtifacts = getMyArtifacts;
exports.updateArtifact = updateArtifact;
exports.getShowcaseArtifacts = getShowcaseArtifacts;
exports.likeArtifact = likeArtifact;
exports.getRemainDailyAIQueryQuotas = getRemainDailyAIQueryQuotas;
exports.ai_api_models = ai_api_models;