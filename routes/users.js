const express = require('express');
const router = express.Router();
const fs = require('fs');

const mongoose = require('mongoose');
const async = require('async');
const _ = require('underscore');
const { URL } = require('url');
const moment = require('moment');
const randtoken = require('rand-token');
const svgCaptcha = require('svg-captcha');

const ytxSms = require('./utils/ytxSms');
const hashUtil = require('./utils/hashUtil');
const auth = require('./utils/auth');
const log = require('./utils/log');
const emailHelper = require('./utils/emailHelper');
const config = require('../config').config;
const VerificationToken = mongoose.model('VerificationToken');
const User = mongoose.model('User');
const PushId = mongoose.model('PushId');
const Slide = mongoose.model('Slide');
const RIL = mongoose.model('RIL');
const OrgJoinInvitation = mongoose.model('OrgJoinInvitation');
const docController = require('./controllers/docController');
const paymentController = require('./controllers/paymentController');
const userController = require('./controllers/userController');
const articleController = require('./controllers/articleController');
const aiController = require('./controllers/aiController');

const CONSTANT = require('./utils/constant');
const validator = require('./utils/validator');
const sendVerifySMSTo = ytxSms.sendVerifySMSTo_promisify;

var signature = require("cookie-signature");
const Organization = require('../models/Organization');
const session = require('express-session');
const { cloneDeep } = require('lodash');
const InviteCode = require('../models/InviteCode');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const { choosen_languages } = require('./utils/langDetect');
const {
    OAuth2Client,
} = require('google-auth-library');

const { RecaptchaEnterpriseServiceClient } = require('@google-cloud/recaptcha-enterprise');
const { isProhibitedMailServiceProvider } = require('./utils/security');

const oAuth2Client = new OAuth2Client(
    "988058218123-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com",
    "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",
    "postmessage"
);


function validatePhone(text) {
    return validator.validatePhone(text);
}

function validateEmail(email) {
    return validator.validateEmail(email);
}

function validateUsername(username) {
    return validatePhone(username) || validateEmail(username);
}

router.post('/oauth-sign-in', async (req, res) => {
    const { tokenInfo } = req.body;

    try {
        const response = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                Authorization: `${tokenInfo.token_type} ${tokenInfo.access_token}`,
                'Content-Type': 'application/json'
            },
        });

        const userInfo = await response.json();
        if (isProhibitedMailServiceProvider(userInfo.email)) {
            throw 'unsupported email address';
        }
        await login(userInfo.email, 'oauth', { ...req.body, locale: userInfo.locale, oauthInfo: userInfo }, req, res);
    } catch (err) {
        if (err.message == 'OAuth user unmatched') {
            res.json({ success: 0, description: '用户不匹配', message: req.t("message.oauth_user_unmatched"), code: 1 });
            return;
        }

        console.log('error.............', err)

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }
});

router.post('/oauth-sign-in-credential', async (req, res) => {
    const { credential } = req.body;

    try {
        const ticket = await oAuth2Client.verifyIdToken({
            idToken: credential,
            audience: "988058218123-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com"
        });
        const userInfo = ticket.getPayload();

        // console.log('get result from verify Id token..........', ticket, userInfo);
        if (isProhibitedMailServiceProvider(userInfo.email)) {
            throw 'unsupported email address';
        }

        await login(userInfo.email, 'oauth', { ...req.body, locale: userInfo.locale, oauthInfo: userInfo }, req, res);
    } catch (err) {
        console.log('err in oauth to google..........', err)
        if (err.message == 'OAuth user unmatched') {
            res.json({ success: 0, description: '用户不匹配', message: req.t("message.oauth_user_unmatched"), code: 1 });
            return;
        }

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }
});

router.post('/oauth', async (req, res) => {
    const { auth_info } = req.body;

    try {
        await login(auth_info.email, 'oauth', { ...req.body, locale: auth_info.locale, oauthInfo: auth_info }, req, res);
    } catch (err) {
        if (err.message == 'OAuth user unmatched') {
            res.json({ success: 0, description: '用户不匹配', message: req.t("message.oauth_user_unmatched"), code: 1 });
            return;
        }

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }
});


const login = async (username, mode, info, req, res) => {
    const { deviceBrand, appVersion, app, vcode, locale, oauthInfo, password } = info;

    if (isProhibitedMailServiceProvider(username)) {
        throw 'unsupported email address';
    }

    const isEmail = username.indexOf('@') > -1;

    if (locale) {
        req.session.locale = locale;
    }

    let userItem = await User.findOne({ username });

    if (['temp_token', 'oauth'].includes(mode)) {
        if (mode == 'temp_token') {
            await verifyToken(username, vcode, VerificationToken.type.login);
        }

        if (!userItem) {
            let userdata = {
                username,
                activated: 1,
                nickname: oauthInfo?.name,
                oauthInfo,
                avatarURL: oauthInfo?.picture
            }

            if (isEmail) {
                userdata.email = username;
                userdata.emailVerified = mode == 'oauth' ? (oauthInfo.email_verified ? 1 : 0) : 1;
            }

            let createdNewUserInfo = await createUser(null, { username }, userdata, 'somebody_writingspace', req);
            userItem = createdNewUserInfo?.user;

            if (userItem?.inviteCode) {
                await inviteFriendAwards(userItem);
            }
        } else {
            if (mode == 'oauth') {
                if (userItem.oauthInfo && userItem.oauthInfo.sub != oauthInfo.sub) {
                    throw new Error('OAuth user unmatched');
                }

                let update_data = { oauthInfo };

                if (!userItem.activated) {
                    update_data.activated = 1;
                    update_data.password = null;
                }
                userItem = await User.findOneAndUpdate({ username }, { $set: update_data }, { new: true });
            } else if (mode == 'temp_token' && !userItem.activated) {
                userItem = await User.findOneAndUpdate({ username }, { $set: { activated: 1, password: null } }, { new: true });
            }
        }
    } else {
        if (!userItem) {
            throw new Error('User Not Exist');
        } else if (userItem.password != hashUtil.encodeMd5(userItem.randomNum + '_' + password)) {
            throw new Error('Wrong Password');
        }
    }

    if (userItem) {
        userItem = userItem.toObject();
    }

    let orgs = await Organization.find({ "users.user": userItem._id }).sort({ createdAt: -1 }).lean();
    if (orgs && orgs.length) {
        let org = orgs[0];
        req.session.orgId = org._id;
        userItem.workingOrgId = org._id;
    }

    if (app) {
        let apps = userItem.apps || [];
        if (!apps.includes(app)) {
            apps.push(app);

            await User.update({ _id: userItem._id }, { $set: { apps } });

            if (app.startsWith('ril')) {
                await aiController.initWelcomeMessage(userItem._id, req);
                await paymentController.serviceSubscription(userItem._id, 'ril_vip_1', 'trial');
                await articleController.bootcampRil(userItem._id, { deviceBrand, appVersion });
            }
        }

        if (app == 'email_service') {
            req.session.emailServiceOrgId = userItem.emailServiceOrgId;
        }
    }

    // if (deviceId) {
    //     await PushId.update({ deviceId }, { $set: { userId: userItem._id, isLogin: true } });
    // }

    req.session.username = username;
    if (userItem.activated) {
        req.session.userId = userItem._id;//都使用_id
    }

    userItem.isnew = 0;
    userItem.isLogin = 1;
    userItem.token = hashUtil.encodeMd5('' + userItem._id + '666666' + userItem.randomNum);
    userItem.password = undefined;
    userItem.permission = undefined;
    userItem.randomNum = undefined;

    let real_sid = signature.sign(req.sessionID, 'yipinzixun_lkadfuu3fj');
    userItem.sessionID = real_sid;


    res.json({ success: 1, description: '登录成功！', code: 0, data: userItem });
}

router.post('/login', async function (req, res, next) {
    var username = req.body.phone || req.body.username;
    username = String(username);
    username = username.toLowerCase();
    var password = req.body.password;
    const { mode } = req.body;

    if (!username || (!password && mode != 'temp_token') || !validateUsername(username)) {
        res.sendStatus(400);
        return;
    }

    try {
        await login(username, mode, req.body, req, res);
    } catch (err) {
        console.log(err);

        if (err.message == 'User Not Exist') {
            res.json({ success: 0, description: '用户名不存在', message: req.t('message.user_not_exist'), code: 1 });
            return;
        }

        if (err.message == 'Wrong Password') {
            res.json({ success: 0, description: '密码错误', message: req.t("message.user_not_exist"), code: 1 });
            return;
        }

        if (err.message == 'Verification Code Error') {
            res.json({ success: 0, description: '验证码错误', message: req.t("message.verification_code_error"), code: 1 });
            return;
        }

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }
});

router.post('/lngSetting', async function (req, res, next) {
    var lng = req.body.lng;

    if (!lng) {
        res.sendStatus(400);
        return;
    }

    if (lng) {
        req.session.locale = lng;
    }

    res.json({ success: 1, description: '设置成功！', message: 'OK', code: 0, data: lng });
});

router.get('/getLngSetting', async function (req, res, next) {
    var userId = req.session.userId;

    if (!userId) {
        res.sendStatus(400);
        return;
    }

    res.json({ success: 1, description: '获取成功！', code: 0, data: req.session.locale });
});

router.post('/switchWorkspace', auth.checkUser, async function (req, res, next) {
    var orgId = req.body.orgId;
    var userId = req.session.userId;

    if (!orgId) {
        res.sendStatus(400);
        return;
    }

    try {
        let org = await Organization.findOne({ "users.user": userId, _id: orgId });
        if (!org) {
            return res.json({ success: 0, description: '未找到该组织', message: req.t('message.org_not_exist_or_no_access'), code: 1 });
        }

        req.session.orgId = orgId;
        return res.json({ success: 1, data: { orgId } });
    }
    catch (err) {
        console.log(err);
        return res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }
});

router.post('/logout', async function (req, res, next) {
    var userId = req.session.userId;
    var deviceId = req.body.deviceId;

    if (deviceId) {
        try {
            await PushId.findOneAndUpdate({ userId, deviceId }, { $set: { isLogin: false } });
        } catch (err) {
            console.log(err);
        }
    }

    req.session.destroy();
    res.clearCookie('userId');
    res.json({ success: 1, data: {} });
});

const createUser = async (existingUser, selector, userData, workspace_name, req) => {
    if (req.body.inviteCode) {
        userData.inviteCode = req.body.inviteCode;
    }

    if (req.body.aid) {
        userData.source_aid = req.body.aid;
    }

    userData.nickname = userData.username.indexOf('@') > -1 ? userData.username.split('@')[0] : userData.username.slice(-4);
    let user = await User.findOneAndUpdate(selector, { $set: userData }, { upsert: true, new: true, setDefaultsOnInsert: true });

    //add default personal workspace for all new user
    let org;

    if (existingUser?.rilBindedOrgId) {
        org = await Organization.findById(existingUser.rilBindedOrgId);
    } else {
        org = await new Organization({
            // name: `${user.nickname}'s workspace`,
            name: req.t(workspace_name, { nickname: user.nickname }),
            users: [{ user: user._id, role: 'admin', confirmed: 1 }]
        }).save();
        await docController.bootcampDocs(req, user._id, org._id, true, true);

        await paymentController.aiCoinTransaction({ userId: user._id, coins: config.trial_coins, activity: 'new_user_trial' });

        const { deviceBrand, appVersion, app, source } = req.body;

        if (source === 'ril' || app && app.startsWith('ril')) {
            await aiController.initWelcomeMessage(user._id, req);
        }

        //add default personal workspace for all new user
        // let org = await new Organization({
        //     // name: `${user.nickname}'s workspace`,
        //     name: req.t('somebody_workspace', { nickname }),
        //     users: [{ user: user._id, role: 'admin', confirmed: 1 }]
        // }).save();

        await User.update({ _id: user._id }, { $set: { rilBindedOrgId: org._id } });
        await docController.initInstantNotesBook(req, user._id, org._id);
        // await paymentController.serviceSubscription(user._id, 'ril_vip_1', 'trial');
        await articleController.bootcampRil(user._id, { deviceBrand, appVersion, source });
    }

    user.workingOrgId = org._id;
    user.rilBindedOrgId = org._id;
    return { user, org };
}

router.post('/register', async function (req, res, next) {
    var username = String(req.body.phone || req.body.username);
    username = username.toLowerCase();

    if (isProhibitedMailServiceProvider(username)) {
        throw 'unsupported email address';
    }

    var password = req.body.password;
    var verificationCode = req.body.vcode;
    var locale = req.body.locale || 'cn';
    var invitationId = req.body.invitationId;
    const { app, inviteCode, aid } = req.body;

    if (app) {
        log.registerLog(app, username);
    }
    if (!username || !password || !validateUsername(username)) {
        res.sendStatus(400);
        return;
    }

    var isUsernamePhone = validatePhone(username);

    if (locale) {
        req.session.locale = locale;
    }

    try {
        let existingUser = await User.findOne({ username });
        if (existingUser && !!existingUser.password && existingUser.activated) {
            throw new Error('User Exist');
        }

        let accountVerified = false;
        let invitation = null;
        if (invitationId) {
            invitation = await OrgJoinInvitation.findById(invitationId).lean();
            if (invitation && invitation.invitedUserAccount === username) {
                accountVerified = true;
            }
        }

        if (isUsernamePhone && !accountVerified) {
            await verifyToken(username, verificationCode, VerificationToken.type.register);
        }

        var randomNum = _.random(100000, 999999);
        password = hashUtil.encodeMd5(randomNum + '_' + password);
        var userdata = {
            username: username,
            password: password,
            randomNum: randomNum,
            activated: (isUsernamePhone || accountVerified) ? 1 : 0,
        };

        if (invitation && validator.validateEmail(invitation.invitedUserAccount)) {
            userdata.email = invitation.invitedUserAccount;
            userdata.emailVerified = 1;
        }

        if (inviteCode) {
            userdata.inviteCode = inviteCode;
        }

        if (aid) {
            userdata.source_aid = aid;
        }

        if (app) {
            userdata.apps = [app];
        }

        //验证码正确，用户名密码等正确，则添加用户
        let userSelector = invitation ? { _id: invitation.invitedUserId } : { username };
        let { user, org } = await createUser(existingUser, userSelector, userdata, 'somebody_workspace', req);
        // let user = await User.findOneAndUpdate(userSelector, { $set: userdata }, { upsert: true, new: true, setDefaultsOnInsert: true });

        if (!user) {
            throw 'err in create new user';
        }

        if (validateEmail(username) && !accountVerified) {
            sendVerificationEmail(username, user._id, VerificationToken.type.register, req);
        }

        if (inviteCode && user.activated) {
            await inviteFriendAwards(user);
        }

        // if (invitation) {
        //     await Organization.updateOne({ _id: invitation.orgId, users: { $elemMatch: { "user": user._id } } }, { $set: { "users.$.confirmed": 1 } });
        //     docController.bootcampDocs(req, user._id, invitation.orgId, false);
        // }

        // PushId.update({ deviceId }, { $set: { userId: user._id, isLogin: true } }, function (error, result) {
        //     if (error) {
        //         console.log('error in update pushId', error);
        //     }
        // });

        // let demoSlide = await Slide.findById(config.demo_slide_id);
        // if (demoSlide) {
        //     await slidesController.upsertSlides(user._id, { title: demoSlide.title, markdown: demoSlide.markdown });
        // }

        if (user.activated) {
            req.session.orgId = org._id;
            req.session.username = username;
            req.session.userId = user._id;//都使用_id
        } else {
            req.session.orgId = undefined;
            req.session.username = undefined;
            req.session.userId = undefined;//都使用_id
        }
        user.isnew = 1;
        user.isLogin = 1;
        user.password = undefined;
        //existingUser && validateEmail(username) && !existingUser.password ? null :
        res.json({ success: 1, description: '注册成功！', message: 'OK', code: 0, data: user });
        return;
    } catch (err) {
        console.log('err..............', err);
        if (err.message == 'User Exist') {
            res.json({ success: 0, description: '手机号已注册，请直接登录', message: req.t("message.user_existed"), code: 1 });
            return;
        }

        if (err.message == 'Verification Code Error') {
            // setTimeout(() =>
            res.json({ success: 0, description: '验证码错误或超时', message: req.t("message.verification_code_error"), code: 1 })
            // , 100000000)
            return;
        }

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }
});

const inviteFriendAwards = async (user) => {
    if (user.invitor) {
        return;
    }

    let invCode = await InviteCode.findOneAndUpdate({ code: user.inviteCode }, { $inc: { usedTimes: 1 } }, { new: true })?.lean();
    if (!invCode) {
        return;
    }

    !!config.invited_award && await paymentController.aiCoinTransaction({ userId: user._id, coins: config.invited_award, activity: 'invited_by_friend', reward_partner_id: invCode.userId });
    !!config.invite_friend_award && await paymentController.aiCoinTransaction({ userId: invCode.userId, coins: config.invite_friend_award, activity: 'invite_friend', reward_partner_id: user._id, reward_partner_name: user.nickname });
    await User.update({ _id: user._id }, { $set: { invitor: invCode.userId } });
}


router.post('/updateUserName', auth.checkUser, async function (req, res, next) {
    var userId = req.session.userId;
    var name = req.body.name;

    if (!name) {
        res.sendStatus(400);
        return;
    }

    try {
        let user = await User.findById(userId);
        if (!user) {
            throw new Error('User Not Found');
        }

        user = await User.findByIdAndUpdate(userId, { $set: { nickname: name } }, { new: true }).lean();
        user.password = undefined;
        user.permission = undefined;
        user.randomNum = undefined;
        user.workingOrgId = req.session.orgId;

        res.json({ success: 1, data: user, message: req.t("message.operation_success"), code: 0 });
    }
    catch (err) {
        if (err.message == 'User Not Found') {
            res.json({ success: 0, description: '用户不存在', message: req.t("message.user_not_exist"), code: 1 });
            return;
        }

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }

});

router.get('/getReceiveUpdateEmails', auth.checkUser, async function (req, res, next) {
    var userId = req.session.userId;
    let user = await User.findById(userId);
    if (!user) {
        res.json({ success: 0, description: '用户不存在', message: req.t("message.user_not_exist"), code: 1 });
        return;
    }

    res.json({ success: 1, description: '获取成功', message: req.t("message.operation_success"), code: 0, data: !user.emailOptOut });
});

router.post('/updateReceiveUpdateEmails', auth.checkUser, async function (req, res, next) {
    var userId = req.session.userId;
    let user = await User.findById(userId);
    if (!user) {
        res.json({ success: 0, description: '用户不存在', message: req.t("message.user_not_exist"), code: 1 });
        return;
    }

    user.emailOptOut = !req.body.emailOptIn;
    await user.save();
    res.json({ success: 1, description: '更新成功', message: req.t("message.operation_success"), code: 0, data: !user.emailOptOut });
});

router.post('/updateOccupation', auth.checkUser, async function (req, res, next) {
    var userId = req.session.userId;
    let user = await User.findById(userId, User.userProfileFilter);
    if (!user) {
        res.json({ success: 0, description: '用户不存在', message: req.t("message.user_not_exist"), code: 1 });
        return;
    }

    user.occupation = req.body.occupation;
    await user.save();
    res.json({ success: 1, description: '更新成功', message: req.t("message.operation_success"), code: 0, data: user });
});

router.post('/updateRILBindedOrgId', auth.checkUser, async function (req, res, next) {
    var userId = req.session.userId;
    var orgId = req.body.orgId;

    if (!orgId) {
        res.sendStatus(400);
        return;
    }

    try {
        let user = await User.findById(userId);
        if (!user) {
            throw new Error('User Not Found');
        }

        let org = await Organization.findOne({ "users.user": userId, _id: orgId });
        if (!org) {
            return res.json({ success: 0, description: '组织不存在', message: req.t("message.org_not_exist_or_no_access"), code: 1 });
        }

        user = await User.findByIdAndUpdate(userId, { $set: { rilBindedOrgId: orgId } }, { new: true }).lean();
        user.password = undefined;
        user.permission = undefined;
        user.randomNum = undefined;
        user.workingOrgId = req.session.orgId;

        let rilsCount = await RIL.count({ userId });
        if (!rilsCount) {
            articleController.bootcampRil(userId);
        }

        res.json({ success: 1, data: user, message: req.t("message.operation_success"), code: 0 });
    }
    catch (err) {
        console.log('err...', err)
        if (err.message == 'User Not Found') {
            res.json({ success: 0, description: '用户不存在', message: req.t('message.user_not_exist'), code: 1 });
            return;
        }

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }

});

router.post('/resetpwd', async function (req, res, next) {
    var username = String(req.body.username);
    username = username.toLowerCase();
    var password = req.body.password;
    var oldPassword = req.body.oldPassword;
    var verificationCode = req.body.vcode;
    var type = req.body.type;

    if (!username || !password || !validateUsername(username) || !(verificationCode || oldPassword) || !(type === VerificationToken.type.forget || type === VerificationToken.type.reset)) {
        res.sendStatus(400);
        return;
    }

    try {
        let existingUser = await User.findOne({ username });
        if (!existingUser) {
            throw new Error('User Not Exist');
        }

        if (type === VerificationToken.type.forget) {
            await verifyToken(username, verificationCode, type);
        } else if (type === VerificationToken.type.reset) {
            if (existingUser.password != hashUtil.encodeMd5(existingUser.randomNum + '_' + oldPassword)) {
                throw new Error('Wrong Password');
            }
        }

        var randomNum = _.random(100000, 999999);
        password = hashUtil.encodeMd5(randomNum + '_' + password);
        //验证码正确，用户名密码等正确，则添加用户
        let user = await User.findByIdAndUpdate(existingUser._id, {
            $set: {
                password,
                randomNum,
                lastUpdateTime: new Date()
            }
        });

        if (!user) {
            throw 'err in update password';
        }

        user.password = undefined;
        res.json({ success: 1, description: '修改成功！', message: req.t("message.operation_success"), code: 0, data: user });
        return;
    } catch (err) {
        console.log('err..............', err);
        if (err.message == 'User Not Exist') {
            res.json({ success: 0, description: req.t("message.user_not_exist"), message: err.message, code: 1 });
            return;
        }

        if (err.message == 'Wrong Password') {
            res.json({ success: 0, description: '密码错误', message: '密码错误', code: 1 });
            return;
        }

        if (err.message == 'Verification Code Error') {
            res.json({ success: 0, description: '验证码错误或超时', message: req.t('message.verification_code_error'), code: 1 });
            return;
        }

        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 1 });
    }
});

const verifyToken = async (account, token, type) => {
    let validTime = 24 * 3600 * 1000;
    if (type !== VerificationToken.type.emailBind && type !== VerificationToken.type.register && type !== VerificationToken.type.login) {
        validTime = 3600 * 1000 / 2;
    }
    let verificationToken = await VerificationToken.findOneAndUpdate({
        account,
        token,
        verified: 0,
        createdAt: { $gt: new Date(new Date().getTime() - validTime) }
    }, { $set: { verified: 1 } });

    if (!verificationToken) {
        throw new Error('Verification Code Error');
    }

    return verificationToken;
}

router.get('/info', async function (req, res) {
    var userId = req.query.userId;
    if (!userId) {
        userId = req.session.userId;
    }
    if (!userId) {
        res.sendStatus(403);
        return;
    }

    if (req.query.locale) {
        req.session.locale = req.query.locale;
    }

    try {
        let user = await userController.getUserInfo(userId);
        if (!user) {
            throw 'No user found';
        }

        if (req.session.orgId) {
            user.workingOrgId = req.session.orgId;
        } else {
            let orgs = await Organization.find({ "users.user": userId }).sort({ createdAt: -1 }).lean();
            if (orgs && orgs.length) {
                let org = orgs[0];
                req.session.orgId = org._id;
                user.workingOrgId = org._id;
            }
        }

        return res.json({ success: 1, description: '查询成功', message: '', code: 0, data: user });
    } catch (error) {
        console.log('err in querying user data', error);
        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 3, data: {} });
    }
});

router.post('/deleteAccount', async function (req, res) {
    var userId = req.session.userId;
    var _id = req.body._id;
    if (!userId || !_id || _id != userId) {
        res.sendStatus(400);
        return;
    }

    try {
        await User.remove({ _id: userId });
        req.session.userId = null;

    } catch (error) {
        console.log('err in deleting user', error);
        res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t('message.server_error'), code: 3, data: {} });
    }
    res.json({ success: 1, description: '删除成功', message: req.t('message.operation_success'), code: 0, data: {} });
});

router.get('/invitation', async function (req, res) {
    var invitationId = req.query.invitationId;

    if (!invitationId) {
        res.sendStatus(400);
        return;
    }

    try {
        let invitation = await OrgJoinInvitation.findById(invitationId);
        if (!invitation) {
            throw 'No invitation found';
        }
        if (invitation.invitedUserId) {
            let user = await User.findById(invitation.invitedUserId);
            if (user && user.activated) {
                invitation.userActivated = true;
            }
        }

        return res.json({ success: 1, description: '查询成功', message: '', code: 0, data: invitation });
    } catch (error) {
        console.log('err in querying user data', error);
    }

    return res.json({ success: 0, description: '查询失败', message: err.message, code: 3, data: {} });
});

async function createAssessment({
    projectID = "funblocks",
    recaptchaKey = "6LcolWYqAAAAAJuipe7B6EKECW5E_pcjHEDSXfaw",
    token,
    recaptchaAction = "action-name",
}) {
    const apiKey = "AIzaSyDSmwNJMu1sXo_73HTRiEYBjsONK8wbEno"; // Replace with your actual API key
    const apiEndpoint = `https://recaptchaenterprise.googleapis.com/v1/projects/funblocks/assessments?key=${apiKey}`;

    try {
        const response = await axios.post(apiEndpoint, {
            event: {
                token: token,
                siteKey: recaptchaKey,
                expectedAction: recaptchaAction,
            },
        });

        const assessment = response.data;

        // Check if the token is valid
        if (!assessment.tokenProperties.valid) {
            console.log(`The assessment call failed because the token was: ${assessment.tokenProperties.invalidReason}`);
            return null;
        }

        // Check if the expected action was executed
        if (assessment.tokenProperties.action === recaptchaAction) {
            console.log(`The reCAPTCHA score is: ${assessment.riskAnalysis.score}`);
            assessment.riskAnalysis.reasons.forEach((reason) => {
                console.log(reason);
            });

            return assessment.riskAnalysis.score;
        } else {
            console.log("The action attribute in your reCAPTCHA tag does not match the action you are expecting to score");
            return null;
        }
    } catch (error) {
        console.error('Error creating reCAPTCHA assessment:', error.response ? error.response.data : error.message);
        return null;
    }
}

//手机号请求验证码
router.get('/verify', async function (req, res) {
    let username = String(req.query.phone || req.query.username);
    username = username.toLowerCase();
    let type = req.query.type;
    let recaptchaToken = req.query.recaptchaToken;

    //AIzaSyDSmwNJMu1sXo_73HTRiEYBjsONK8wbEno

    if (recaptchaToken) {
        const score = await createAssessment({ token: recaptchaToken, recaptchaAction: 'SEND_EMAIL_VERIFICATION_CODE' });
        if (score === null || score < 0.5) {
            return res.json({ success: 0, description: '验证失败', message: req.t("message.recaptcha_verification_failed"), code: 1 });
        }
    }

    if (!_.contains(['register', 'forget', 'reset', 'login'], type) || !validateUsername(username)) {
        res.sendStatus(400);
        return;
    }

    let locale = req.query.locale;

    if (locale) {
        req.session.locale = locale;
    }

    var isUsernamePhone = validatePhone(username);

    var vcode = generateVerificationToken(username, type);
    var datas = [vcode, '30'];//目前验证码的时效都是30分钟

    try {
        let existingUser = await User.findOne({ username });
        if (type === VerificationToken.type.register && existingUser) {
            throw new Error('User Exist');
        } else if ((type === VerificationToken.type.reset || type === VerificationToken.type.forget) && !existingUser) {
            throw new Error('User Not Exist');
        }

        let existingTokens = await VerificationToken.find({ account: username }).sort({ updatedAt: -1 }).limit(5);
        var dailyCount = 0;//今天发送的数量
        var inTwoMinutesCount = 0;//两分钟内发送的数量
        _.each(existingTokens, function (element, index) {
            var now = moment();
            var createTime = moment(element.createdAt);
            if (now.isSame(createTime, 'day')) {
                dailyCount++;
            }
            if (now.unix() - createTime.unix() < 59) {
                inTwoMinutesCount++;
            }
        });

        if (!CONSTANT.isDev) {
            if (isUsernamePhone && dailyCount >= 4) {
                throw new Error('Daily Limit');
            } else if (inTwoMinutesCount > 0) {
                throw new Error('Too Often');
            }
        }

        let response = {};
        if (!isUsernamePhone) {
            response = await emailHelper.sendEmailTo(getEmailContent(username, vcode, type, req));
        } else if (CONSTANT.isDev) {
            console.log('simulating vCode............', vcode);
            response = { result: 'Token sent to console.' };
        } else {
            response = await sendVerifySMSTo(username, datas);
            if (response.body.statusCode !== '000000') {
                console.log("response from SMS provider", response.body);
                throw new Error('SMS Provider Error');
            }
        }

        await new VerificationToken({
            account: username,
            token: vcode,
            type: type,
            deliveryResponse: JSON.stringify(response)
        }).save();

        return res.json({ success: 1, description: '验证码已发送', message: req.t("message.verification_code_sent") + (isUsernamePhone ? req.t("phone") : req.t("email")), code: 0 });
    } catch (err) {
        console.log('err..............', err);
        if (err.message == 'User Exist') {
            return res.json({ success: 0, description: '该账号已注册，请直接登录', message: req.t("message.user_existed"), code: 1 });
        }

        if (err.message == 'User Not Exist') {
            return res.json({ success: 0, description: '该账号未注册，请直接注册', message: req.t("message.user_not_exist"), code: 1 });
        }

        if (err.message == 'Too Often') {
            return res.json({ success: 0, description: '请求过于频繁，请稍候', message: req.t("message.too_frequent"), code: 1 });
        }

        if (err.message == 'Daily Limit') {
            return res.json({ success: 0, description: '每个手机号每天只能发送两次验证码', message: req.t("sms_daily_limit"), code: 1 });
        }

        if (err.message == 'SMS Provider Error') {
            return res.json({ success: 0, description: '短信服务问题，请稍候', message: req.t("sms_service_error"), code: 1 });
        }

        return res.status(500).json({ success: 0, description: '服务器内部错误', message: req.t("message.server_error"), code: 1 });
    }
});

const generateVerificationToken = (account, type) => {
    let token = null;
    if (type === VerificationToken.type.emailBind || (validateEmail(account) && type === VerificationToken.type.register)) {
        let tokenGenerator = randtoken.generator();
        token = tokenGenerator.generate(16);
    } else {
        let tokenGenerator = randtoken.generator({ chars: '0-9' });
        token = tokenGenerator.generate(6);
    }

    return token;
}

const getEmailContent = (account, token, type, req) => {
    let activate_url = `${config.server_domain}users/verifyEmail?token=${token}`;

    let emailData = {
        to: account,
        subject: req.t('notification.verification_vcode_subject'),
        text: req.t('notification.verification_vcode_text', { token, account }),
    }

    if (type === VerificationToken.type.register || type === VerificationToken.type.emailBind) {
        emailData = {
            to: account,
            subject: req.t('notification.verification_email_subject'),
            text: req.t('notification.verification_email_text', { activate_url, account }),
            html: req.t('notification.verification_email_html', { activate_url, account }),
        }
    }

    console.log('emailData..............', emailData);

    return emailData;
}

const sendVerificationEmail = async (account, userId, type, req) => {
    let existingToken = null;
    try {
        existingToken = await VerificationToken.findOne({
            userId, account, verified: 0, type,
            updatedAt: { $gt: new Date(new Date().getTime() - 24 * 3600 * 1000) }
        }).sort({ updatedAt: -1 })
    } catch (error) {
        console.log('err in finding verification token:', error);
    }

    let token = existingToken ? existingToken.token : generateVerificationToken(account, type);

    try {
        await emailHelper.sendEmailTo(getEmailContent(account, token, type, req));
        if (existingToken) {
            await VerificationToken.findByIdAndUpdate(existingToken._id, { $set: { updatedAt: new Date() } })
        } else {
            await new VerificationToken({ account, token, type, userId }).save();
        }
    } catch (error) {
        console.log('error in sending email:', error);
    }
}

router.post('/resendVerificationEmail', async function (req, res) {
    const email = req.body.email;

    if (!email) {
        res.sendStatus(400);
        return;
    }

    let existedUser = await User.findOne({ username: email });
    if (!existedUser) {
        return res.json({ success: 1, description: 'No user account found！', message: req.t("message.user_not_exist"), code: 0 });
    }

    try {
        await sendVerificationEmail(email, existedUser._id, VerificationToken.type.register, req);
        return res.json({ success: 1, description: '操作成功！', message: req.t("message.activation_email_sent"), code: 0 });
    } catch (error) {
        console.log('err in sending verification email', error);
        return res.json({ success: 0, description: '操作失败！', message: req.t("message.operation_failed"), code: 1 });
    }
});

router.post('/bindEmail', auth.checkUser, async function (req, res) {
    const userId = req.session.userId;
    const email = req.body.email;

    if (!userId || !email) {
        res.sendStatus(400);
        return;
    }

    let existedUser = await User.findOne({ $or: [{ username: email }, { email, emailVerified: 1 }] });
    if (existedUser) {
        if (String(userId) === String(existedUser._id)) {
            return res.json({ success: 1, description: '修改成功！', message: 'OK!', code: 0, data: existedUser });
        } else {
            return res.json({ success: 0, description: '邮箱已经绑定到其他账号', message: '邮箱已经绑定到其他账号', code: 1 });
        }
    }

    let emailUpdateData = {
        email,
        emailVerified: 0
    }

    let verifiedToken = await VerificationToken.findOne({ account: email, userId, verified: 1 });
    if (verifiedToken) {
        emailUpdateData.emailVerified = 1;
    } else {
        try {
            await sendVerificationEmail(email, userId, VerificationToken.type.emailBind, req);
        } catch (error) {
            console.log('err in sending verification email', error);
        }
    }

    try {
        let user = await User.findByIdAndUpdate(userId, { $set: emailUpdateData }, { new: true, fields: User.userProfileFilter });
        if (!user) {
            throw 'no user found';
        }
        return res.json({ success: 1, description: '修改成功！', message: 'OK', code: 0, data: user });
    } catch (err) {
        return res.json({ success: 0, description: '修改失败！', message: '修改失败', code: 1 });
    }
});

router.get('/verifyEmail', async function (req, res) {
    const token = req.query.token;

    if (!token) {
        res.sendStatus(400);
        return;
    }

    let result = {
        verified: false
    };
    try {
        result.verification_result_title = req.t('notification.verified_title');
        result.callback = req.t("notification.callback", { app: "FunBlocks" });
        let verificationToken = await VerificationToken.findOneAndUpdate({ token, updatedAt: { $gt: new Date(new Date().getTime() - 24 * 3600 * 1000) } }, { $set: { verified: 1 } }, { sort: { 'updatedAt': -1 } });
        if (!verificationToken) {
            result.verification_result_title = req.t('notification.failed_verify_title');
            result.info = req.t('notification.token_not_exists');
            throw 'no valid verification token exists';
        }

        let user = null;

        if (verificationToken.type === VerificationToken.type.register) {
            user = await User.findByIdAndUpdate(verificationToken.userId,
                { $set: { email: verificationToken.account, emailVerified: 1, activated: 1 } },
                { new: true }
            );

            if (user.inviteCode) {
                await inviteFriendAwards(user);
            }

        } else if (verificationToken.type === VerificationToken.type.emailBind) {
            user = await User.findByIdAndUpdate(verificationToken.userId,
                { $set: { email: verificationToken.account, emailVerified: 1 } },
                { new: true, fields: User.userProfileFilter }
            );
        }

        if (!user) {
            result.info = 'User not exist!'
            throw 'no user found';
        }

        result.verified = true;
    } catch (err) {
        console.log(err);
    }

    return res.render('emailVerified', result);
});

router.post('/bindPushId', async function (req, res) {
    const userId = req.session.userId;
    const deviceToken = req.body.deviceToken;
    const deviceId = req.body.deviceId;
    const os = req.body.os;
    const channel = req.body.channel;

    console.log('bind pushId params', req.body);

    if (!deviceToken) {
        res.sendStatus(400);
        return;
    }

    try {
        let data = { deviceId, deviceToken, os, channel, updatedAt: new Date() };
        if (userId) {
            data.userId = userId;
            data.isLogin = true;
        } else {
            data.isLogin = false;
        }

        await PushId.update({ deviceId }, { $set: data }, { upsert: true });

        return res.json({ success: 1, description: '修改成功！', code: 0, data: {} });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '修改失败！', message: '修改失败', code: 1 });
    }
});

router.get('/captcha', function (req, res) {
    var captcha = svgCaptcha.create({
        size: 4, // size of random string
        ignoreChars: '0oO1Il', // filter out some characters like 0o1i
        noise: 2, // number of noise lines
    });
    req.session.captcha = captcha.text;

    res.type('svg');
    res.status(200).send(captcha.data);
});

router.get('/verifyCaptcha', (req, res) => {
    let captcha = req.query.captcha ? req.query.captcha.trim() : '';

    let captchaSaved = req.session.captcha || 'No captcha';
    let verified = captchaSaved.toUpperCase() === captcha.toUpperCase();

    console.log('verify captcha:', captcha, captchaSaved, verified)

    return res.json({ success: 1, description: '修改成功！', code: 0, data: { verified } });
})

router.get('/usercount', async function (req, res) {
    const count = await User.count();
    console.log('user count......', count)

    res.status(200).send('ok');
});

router.get('/servingProduct', async function (req, res) {
    let userId = req.session.userId;
    const service = req.query.service;

    if (!service) {
        res.sendStatus(400);
        return;
    }

    if (!userId) {
        userId = 'nobody'
    }

    try {
        const servingProduct = await paymentController.getServingProduct(userId, service);
        return res.json({ success: 1, description: '获取成功！', code: 0, data: servingProduct });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/servingProducts', async function (req, res) {
    let userId = req.session.userId;
    let services = req.query.services;

    if (services && !Array.isArray(services)) {
        services = services.split(",");
    }

    if (!services) {
        res.sendStatus(400);
        return;
    }

    if (!userId) {
        userId = 'nobody'
    }

    try {
        const servingProducts = await paymentController.getServingProducts(userId, services);
        return res.json({ success: 1, description: '获取成功！', code: 0, data: servingProducts });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/wechatBindingCode', auth.checkUser, async (req, res) => {
    const userId = req.session.userId;

    try {
        const code = await userController.getWechatBindingCode(userId);
        return res.json({ success: 1, description: '获取成功！', code: 0, data: code });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/inviteFriendMsg', auth.checkUser, async (req, res) => {
    const userId = req.session.userId;
    const { app } = req.query;

    try {
        const prompt = await userController.generateInvitePrompt(userId, app, req);
        return res.json({ success: 1, description: '获取成功！', code: 0, data: prompt });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/getInviteCode', auth.checkUser, async (req, res) => {
    const userId = req.session.userId;

    try {
        const code = await userController.generateUniqueInviteCode(userId, req);
        return res.json({ success: 1, description: '获取成功！', code: 0, data: code });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/inviteRecords', auth.checkUser, async (req, res) => {
    const userId = req.session.userId;

    try {
        const records = await userController.getInviteRecords(userId, req);
        return res.json({ success: 1, description: '获取成功！', code: 0, data: records });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/affiliateUsers', auth.checkUser, async (req, res) => {
    const userId = req.session.userId;
    const { pageSize = 50, pageNum = 0 } = req.query;

    try {
        const records = await userController.getUsers({ source_aid: userId }, { pageNum, pageSize });
        return res.json({ success: 1, description: '获取成功！', code: 0, data: records });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/affiliateEarnings', auth.checkUser, async (req, res) => {
    const userId = req.session.userId;
    const { pageSize = 50, pageNum = 0 } = req.query;

    try {
        const records = await paymentController.getAffiliateEarnings({ source_aid: userId }, { pageNum, pageSize });
        return res.json({ success: 1, description: '获取成功！', code: 0, data: records });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

router.get('/appConfigs', async (req, res) => {
    const userId = req.session.userId;

    if (req.query.locale) {
        req.session.locale = req.query.locale;
    }

    const { service, deviceBrand, appVersion } = req.query;

    try {
        let appConfig = cloneDeep(config.appConfig);
        appConfig.stripe_public_key = config.stripeKeys.publishable_key;

        if (appConfig.ai_blacklist.filter(item => item.version === appVersion && (item.brand === '*' || item.brand.toLowerCase() === (deviceBrand || '').toLowerCase())).length > 0) {
            appConfig.askAI = false;
        }

        if (appConfig.ai_assist_blacklist.filter(item => item.version === appVersion && (item.brand === '*' || item.brand.toLowerCase() === (deviceBrand || '').toLowerCase())).length > 0) {
            appConfig.aiAssistant = false;
        }

        delete appConfig.ai_blacklist;
        delete appConfig.ai_assist_blacklist;

        let ai_objs;
        if (service === 'funblocks') {
            ai_objs = ['markdown', 'slides', 'flow', 'flow_notes', 'flow_background', 'image', 'edu', 'codes'];
        } else if (service === 'ril') {
            ai_objs = ['ril', 'instanote', 'instatext', 'askAI']
        } else if (service === 'slides') {
            ai_objs = ['slides']
        } else if (service === 'aiwriter') {
            ai_objs = ['markdown', 'codes'];
        } else if (service === 'extension') {
            ai_objs = ['markdown', 'ril', 'widget', 'image']
        }

        if (ai_objs) {
            appConfig.assistant_items = appConfig.assistant_items.filter(item => ai_objs.some(obj => item.objTypes.includes(obj)));
        }

        appConfig.assistant_items = appConfig.assistant_items
            .filter(item => !item.deprecated && !item.co_exist)
            .map(item => {
                if (item.prompt_user && !['expand_ideas', 'image_avatar', 'generate_image'].includes(item.action)) {
                    // item.prompt_user = req.t('prompt_user.' + item.action);
                    item.prompt_user = config.user_prompts[item.action];
                }

                if (item.action === 'draft' && service === 'ril') {
                    item.label = req.t('ai_items.draft');
                    item.prompt_user = req.t('ai_items.draft_prompt');
                    item.draft_types = [...item.content_type];
                    item.content_type = item.content_type.map(type => req.t('ai_items.draft_' + type));
                } else if (item.role) {
                    item.label = req.t('ai_roles.' + item.role);
                } else {
                    item.label = req.t('ai_items.' + item.action);
                }

                if (item.action == 'translate') {
                    item.sub_items = choosen_languages.map(lang => lang.intl_key);
                }

                if (item.sub_items) {
                    item.sub_items = item.sub_items.map(it => {
                        if (it === 'input') {
                            return {
                                label: req.t('ai_items.' + it),
                                value: it,
                                id: it
                            }
                        }

                        const action_item = config.appConfig.assistant_items.find(i => i.action == item.action + '_' + it);
                        return {
                            label: req.t('ai_items.' + item.action + '_' + it),
                            value: item.action + '_' + it,
                            id: it,
                            llms: action_item?.llms
                        }
                    })
                }

                if (item.args) {
                    item.args = item.args.map(arg => {
                        arg.hint = req.t("hint." + arg.name);
                        arg.label = req.t("label." + arg.name);
                        return arg;
                    })
                }

                if (item.subTasks) {
                    item.subTasks = item.subTasks.map(subTask => {
                        // subTask.prompt_user = req.t('prompt_user.' + subTask.action);
                        subTask.prompt_user = config.user_prompts[subTask.action];
                        subTask.label = req.t('sub_task.label', { action_label: req.t('ai_items.' + subTask.action) });
                        subTask.title = req.t('sub_task_titles.' + subTask.action);

                        if (subTask.args) {
                            subTask.args = subTask.args.map(arg => {
                                arg.hint = req.t("hint." + arg.name);
                                arg.label = req.t("label." + arg.name);
                                return arg;
                            })
                        }

                        if (subTask.dynamic_arg) {
                            subTask.dynamic_arg.label = req.t("label." + subTask.dynamic_arg.name);
                        }

                        return subTask;
                    });
                }

                if (!['aiwriter', 'funblocks', 'extension'].includes(service)) {
                    delete item.prompt;
                }
                return item;
            });

        if (['aiwriter', 'funblocks', 'extension'].includes(service)) {
            appConfig.assistant_items = appConfig.assistant_items.map(item => {
                if (item.sub_items) {
                    item.prompts = item.sub_items.map(subItem => {
                        let sub_item = subItem.value;
                        let assistant_item = config.appConfig.assistant_items.find(i => i.action == sub_item || i.goodAt?.includes(sub_item.replace(item.action + '_', '')));

                        if (assistant_item) {
                            return assistant_item.prompt;
                        }

                        return item.prompt?.replace(/{sub_action}/g, req.t("ai_items." + sub_item, { lng: 'en' }))
                    });
                } else if (item.action === 'draft') {
                    item.prompts = item.draft_types.map(type => {
                        let aItem = config.appConfig.assistant_items.find(it => {
                            return it.goodAt?.includes(type)
                        });
                        return aItem?.prompt;
                    });
                } else if (!item.prompt && !item.prompt_user) {
                    item.prompt_user = config.appConfig.assistant_items.find(it => {
                        return it.goodAt?.includes(item.action)
                    })?.prompt
                }

                return item;
            })

            appConfig.ai_providers = {
                openai: {
                    models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4o', 'gpt-o'],
                    excluded_models: ['gpt-3.5-turbo-instruct']
                },
                gemini: {
                    models: ['models/gemini-1.5', 'models/gemini-2.0', 'models/gemini-2.5']
                }
            }

            appConfig.ai_api_models = aiController.ai_api_models.map(model => {
                if (model.id.includes('_level_')) {
                    return {
                        ...model,
                        label: req.t(model.id)
                    }
                }

                return {
                    ...model,
                    desc: req.t('llm.' + model.id)
                }
            });
        }

        //'llama3-70b-8192', 'mixtral-8x7b-32768'

        appConfig.assistant_items_groups = appConfig.assistant_items_groups?.map(item => {
            item.label = req.t('ai_items.' + item.label)
            return item;
        })

        // appConfig.ai_roles = Object.keys(appConfig.ai_roles).map(key => {
        //     let role = appConfig.ai_roles[key];
        //     role.label = req.t("ai_roles." + key)
        //     role.people = role.people?.map(people => {
        //         return {
        //             label: req.t("ai_roles." + people),
        //             value: people
        //         }
        //     }) 
        //     return role;
        // })

        // console.log('app config', appConfig.assistant_items)

        return res.json({ success: 1, description: '获取成功！', code: 0, data: appConfig });
    } catch (err) {
        console.log('err', err);
        return res.json({ success: 0, description: '获取失败！', message: '获取失败', code: 1 });
    }
})

let doing = false;
router.get('/sendPromoteEmail', async function (req, res) {
    if (doing) {
        return res.json({ success: 0, description: '正在执行发送任务', data: {}, code: 0 });
    }

    let users = await User.find({ email: { $ne: null }, createdAt: { $gte: new Date("2023-12-21T16:44:34.866Z") } }).lean();
    // let users = [{email: '<EMAIL>'}, {email: '<EMAIL>'}]
    res.json({ success: 1, description: '任务启动', data: { count: users?.length }, code: 0 });

    const content = fs.readFileSync("./public/funblocks_updates.html", 'utf8');
    const emailHashMap = {};

    let sentCount = 0;
    for (let user of users) {
        if (emailHashMap[user.email]) {
            console.log('已发送邮件给该用户，无需重复发送');
            continue;
        }

        doing = true;
        await new Promise(resolve => setTimeout(resolve, 20000));

        console.log('will send emai to.............', sentCount++, users.length, user.email)
        await emailHelper.sendEmailTo({
            to: user.email,
            subject: '邀请下载FunBlocks AI浏览器插件，在任意网站使用 AI 助手',
            html: content.replace('{username}', user.nickname || 'FunBlocks用户')
        })

        emailHashMap[user.email] = true;
    }

    doing = false

});

router.get('/login-status', async function (req, res) {
    const userId = req.session.userId;

    if (!userId) {
        return res.json({
            isLoggedIn: false
        });
    }

    try {
        const user = await User.findById(userId);
        if (!user) {
            return res.json({ isLoggedIn: false });
        }

        return res.json({
            isLoggedIn: true,
            username: user.nickname || '',
            email: user.username || ''
        });
    } catch (err) {
        console.log('Error checking login status:', err);
        return res.json({ isLoggedIn: false });
    }
});

module.exports = router;
