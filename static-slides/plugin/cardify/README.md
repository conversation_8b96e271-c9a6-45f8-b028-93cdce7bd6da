# Reveal.js Cardify Plugin with Image Scaling

## 功能概述

Cardify 插件为 Reveal.js 提供了强大的内容布局和图片处理功能：

1. **卡片化列表**：将 ul/ol 列表转换为美观的卡片网格
2. **智能图文混排**：自动检测并重新排列图片和文本内容
3. **自动图片缩放**：确保所有图片不会超出幻灯片显示区域

## 新增功能：图片自动缩放

### 功能特点

- **自动检测**：插件会自动检测幻灯片中的所有图片
- **智能缩放**：根据图片所在的容器和幻灯片尺寸进行智能缩放
- **响应式设计**：支持不同屏幕尺寸下的自适应缩放
- **保持比例**：缩放时保持图片原始宽高比
- **性能优化**：只在需要时进行缩放，避免不必要的处理

### 缩放规则

1. **普通图片**：
   - 最大宽度：幻灯片宽度的 90%
   - 最大高度：幻灯片高度的 80%

2. **卡片中的图片**：
   - 最大宽度：可用宽度的 40%
   - 最大高度：200px

3. **文本区域中的图片**：
   - 最大宽度：可用宽度的 30%
   - 最大高度：150px

4. **SmartLayout 中的图片**：
   - 使用专门的布局样式
   - 单图片：最大高度 70vh
   - 多图片：最大高度 30vh

### 样式特性

- **圆角边框**：8px 圆角，提升视觉效果
- **阴影效果**：轻微阴影，增加层次感
- **悬停动画**：鼠标悬停时的缩放和阴影变化
- **居中对齐**：图片自动居中显示

### 响应式支持

在移动设备上（屏幕宽度 < 768px）：
- 普通图片最大高度调整为 60vh
- 卡片中图片最大高度调整为 150px
- 文本区域图片最大高度调整为 120px

## 使用方法

### 基本使用

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="dist/reveal.css">
    <link rel="stylesheet" href="dist/theme/black.css">
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <section>
                <h2>图片缩放测试</h2>
                <img src="large-image.jpg" alt="大尺寸图片">
                <p>图片会自动缩放到合适大小</p>
            </section>
        </div>
    </div>
    
    <script src="dist/reveal.js"></script>
    <script src="plugin/cardify/plugin.js"></script>
    <script>
        Reveal.initialize({
            plugins: [RevealCardify]
        });
    </script>
</body>
</html>
```

### 图文混排示例

```html
<section>
    <h2>产品介绍</h2>
    <p>这是一段详细的产品描述文本，当文本内容足够长时，插件会自动将图片和文本进行混排布局。</p>
    <img src="product-image.jpg" alt="产品图片">
    <ul>
        <li>特性1：高性能</li>
        <li>特性2：易使用</li>
        <li>特性3：可扩展</li>
    </ul>
</section>
```

### 卡片中的图片

```html
<section>
    <h2>功能特性</h2>
    <ul>
        <li>特性一
            <ul>
                <li>详细说明</li>
                <li><img src="feature1.jpg" alt="特性图片"></li>
            </ul>
        </li>
        <li>特性二
            <ul>
                <li>更多说明</li>
                <li><img src="feature2.jpg" alt="特性图片"></li>
            </ul>
        </li>
    </ul>
</section>
```

## 技术实现

### 核心方法

- `scaleAllImages()`: 缩放所有幻灯片中的图片
- `scaleImagesInSlide(slide)`: 缩放指定幻灯片中的图片
- `scaleImage(img, slide)`: 缩放单个图片
- `applyImageConstraints(img, maxWidth, maxHeight)`: 应用尺寸约束

### 事件监听

- `slidechanged`: 幻灯片切换时重新缩放图片
- `ready`: 初始化完成后缩放所有图片
- `resize`: 窗口大小变化时重新缩放图片

### CSS 类

- `.cardify-scaled`: 标记已处理的图片
- `.smart-layout-images`: SmartLayout 图片容器
- `.card-item img`: 卡片中的图片
- `.smart-layout-text img`: 文本区域中的图片

## 兼容性

- 支持所有现代浏览器
- 响应式设计，适配移动设备
- 与其他 Reveal.js 插件兼容
- 不影响原有的图片样式和功能

## 注意事项

1. 图片缩放是非破坏性的，不会修改原始图片文件
2. 缩放只在图片尺寸超出限制时进行
3. 保持图片原始宽高比，避免变形
4. 支持动态加载的图片
5. 在嵌套的 section 中的图片会被忽略，避免重复处理
